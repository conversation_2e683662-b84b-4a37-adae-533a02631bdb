PODS:
  - Flutter (1.0.0)
  - flutter_secure_storage (6.0.0):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqlite3 (3.50.2):
    - sqlite3/common (= 3.50.2)
  - sqlite3/common (3.50.2)
  - sqlite3/dbstatvtab (3.50.2):
    - sqlite3/common
  - sqlite3/fts5 (3.50.2):
    - sqlite3/common
  - sqlite3/math (3.50.2):
    - sqlite3/common
  - sqlite3/perf-threadsafe (3.50.2):
    - sqlite3/common
  - sqlite3/rtree (3.50.2):
    - sqlite3/common
  - sqlite3_flutter_libs (0.0.1):
    - Flutter
    - FlutterMacOS
    - sqlite3 (~> 3.50.1)
    - sqlite3/dbstatvtab
    - sqlite3/fts5
    - sqlite3/math
    - sqlite3/perf-threadsafe
    - sqlite3/rtree

DEPENDENCIES:
  - Flutter (from `Flutter`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - sqlite3_flutter_libs (from `.symlinks/plugins/sqlite3_flutter_libs/darwin`)

SPEC REPOS:
  trunk:
    - sqlite3

EXTERNAL SOURCES:
  Flutter:
    :path: Flutter
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  sqlite3_flutter_libs:
    :path: ".symlinks/plugins/sqlite3_flutter_libs/darwin"

SPEC CHECKSUMS:
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_secure_storage: 1ed9476fba7e7a782b22888f956cce43e2c62f13
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  sqlite3: 3e82a2daae39ba3b41ae6ee84a130494585460fc
  sqlite3_flutter_libs: e7fc8c9ea2200ff3271f08f127842131746b70e2

PODFILE CHECKSUM: 4305caec6b40dde0ae97be1573c53de1882a07e5

COCOAPODS: 1.16.2
