#!/bin/bash

# Android崩溃修复脚本
# 修复包名不匹配导致的ClassNotFoundException

set -e

echo "🔧 修复Android应用崩溃问题..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数：打印彩色消息
print_message() {
    echo -e "${2}${1}${NC}"
}

print_message "🔍 检查问题修复状态..." $BLUE
echo "=================================================="

# 检查MainActivity是否在正确位置
MAIN_ACTIVITY_PATH="android/app/src/main/kotlin/com/company/enterprise_flutter/MainActivity.kt"
if [ -f "$MAIN_ACTIVITY_PATH" ]; then
    print_message "✅ MainActivity位置正确: $MAIN_ACTIVITY_PATH" $GREEN
else
    print_message "❌ MainActivity位置错误" $RED
    exit 1
fi

# 检查MainActivity包名
if grep -q "package com.company.enterprise_flutter" "$MAIN_ACTIVITY_PATH"; then
    print_message "✅ MainActivity包名正确" $GREEN
else
    print_message "❌ MainActivity包名错误" $RED
    exit 1
fi

# 检查AndroidManifest.xml中的包名
MANIFEST_PATH="android/app/src/main/AndroidManifest.xml"
if grep -q 'package="com.company.enterprise_flutter"' "$MANIFEST_PATH"; then
    print_message "✅ AndroidManifest.xml包名正确" $GREEN
else
    print_message "❌ AndroidManifest.xml包名错误" $RED
    exit 1
fi

# 检查build.gradle.kts中的applicationId
BUILD_GRADLE_PATH="android/app/build.gradle.kts"
if grep -q 'applicationId = "com.company.enterprise_flutter"' "$BUILD_GRADLE_PATH"; then
    print_message "✅ build.gradle.kts applicationId正确" $GREEN
else
    print_message "❌ build.gradle.kts applicationId错误" $RED
    exit 1
fi

print_message "🎉 所有包名配置检查通过！" $GREEN
echo ""

print_message "📋 修复摘要:" $BLUE
echo "=================================================="
echo "问题: ClassNotFoundException - MainActivity类找不到"
echo "原因: 包名不匹配"
echo "  - AndroidManifest.xml 期望: com.company.enterprise_flutter.MainActivity"
echo "  - 实际位置: com.example.flutter_enterprise_app.MainActivity"
echo ""
echo "修复措施:"
echo "✅ 1. 创建正确的目录结构: com/company/enterprise_flutter/"
echo "✅ 2. 移动MainActivity到正确位置"
echo "✅ 3. 更新MainActivity包名为: com.company.enterprise_flutter"
echo "✅ 4. 删除旧的目录结构"
echo ""

print_message "🚀 下一步操作:" $BLUE
echo "=================================================="
echo "1. 清理项目: flutter clean"
echo "2. 获取依赖: flutter pub get"
echo "3. 重新运行: flutter run lib/main.dart"
echo ""

print_message "💡 如果仍有问题，请尝试:" $YELLOW
echo "- 卸载设备上的旧版本应用"
echo "- 重启Android设备或模拟器"
echo "- 使用: flutter run --debug --verbose"
echo ""

print_message "✅ Android崩溃问题修复完成！" $GREEN
