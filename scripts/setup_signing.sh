#!/bin/bash

# 签名配置设置脚本
# 用于设置Android和iOS的签名配置

set -e

echo "🔐 设置应用签名配置..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数：打印彩色消息
print_message() {
    echo -e "${2}${1}${NC}"
}

# Android签名配置
setup_android_signing() {
    print_message "设置Android签名配置..." $BLUE
    
    # 创建keystore目录
    mkdir -p android/keystore
    
    # 检查是否已有签名配置
    if [ -f "android/key.properties" ]; then
        print_message "Android签名配置已存在" $GREEN
        return
    fi
    
    # 检查是否有模板文件
    if [ ! -f "android/key.properties.template" ]; then
        print_message "错误：android/key.properties.template 文件不存在" $RED
        return 1
    fi
    
    print_message "请按照以下步骤设置Android签名:" $YELLOW
    echo ""
    echo "1. 生成密钥库文件:"
    echo "   keytool -genkey -v -keystore android/keystore/release.keystore \\"
    echo "           -alias release -keyalg RSA -keysize 2048 -validity 10000"
    echo ""
    echo "2. 复制模板文件并填入信息:"
    echo "   cp android/key.properties.template android/key.properties"
    echo ""
    echo "3. 编辑 android/key.properties 文件，填入实际的签名信息"
    echo ""
    echo "4. 确保将以下文件添加到 .gitignore:"
    echo "   android/key.properties"
    echo "   android/keystore/"
    echo ""
    
    # 询问是否现在生成密钥库
    read -p "是否现在生成密钥库文件? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        generate_keystore
    fi
}

# 生成密钥库
generate_keystore() {
    print_message "生成Android密钥库..." $BLUE
    
    # 设置默认值
    KEYSTORE_PATH="android/keystore/release.keystore"
    KEY_ALIAS="release"
    
    echo "请输入密钥库信息:"
    read -p "密钥库密码: " -s STORE_PASSWORD
    echo
    read -p "密钥密码: " -s KEY_PASSWORD
    echo
    read -p "您的姓名: " DNAME_CN
    read -p "组织单位: " DNAME_OU
    read -p "组织: " DNAME_O
    read -p "城市: " DNAME_L
    read -p "省份: " DNAME_ST
    read -p "国家代码 (如 CN): " DNAME_C
    
    # 构建DN字符串
    DNAME="CN=$DNAME_CN, OU=$DNAME_OU, O=$DNAME_O, L=$DNAME_L, ST=$DNAME_ST, C=$DNAME_C"
    
    # 生成密钥库
    keytool -genkey -v -keystore "$KEYSTORE_PATH" \
            -alias "$KEY_ALIAS" \
            -keyalg RSA \
            -keysize 2048 \
            -validity 10000 \
            -storepass "$STORE_PASSWORD" \
            -keypass "$KEY_PASSWORD" \
            -dname "$DNAME"
    
    if [ $? -eq 0 ]; then
        print_message "密钥库生成成功: $KEYSTORE_PATH" $GREEN
        
        # 创建key.properties文件
        cat > android/key.properties << EOF
storeFile=keystore/release.keystore
storePassword=$STORE_PASSWORD
keyAlias=$KEY_ALIAS
keyPassword=$KEY_PASSWORD
EOF
        
        print_message "key.properties 文件已创建" $GREEN
        
        # 更新.gitignore
        update_gitignore
    else
        print_message "密钥库生成失败" $RED
        return 1
    fi
}

# 更新.gitignore
update_gitignore() {
    print_message "更新.gitignore文件..." $BLUE
    
    # 检查.gitignore是否存在
    if [ ! -f ".gitignore" ]; then
        touch .gitignore
    fi
    
    # 添加签名相关文件到.gitignore
    if ! grep -q "android/key.properties" .gitignore; then
        echo "" >> .gitignore
        echo "# Android签名文件" >> .gitignore
        echo "android/key.properties" >> .gitignore
        echo "android/keystore/" >> .gitignore
        print_message "已将签名文件添加到.gitignore" $GREEN
    fi
}

# iOS签名配置
setup_ios_signing() {
    print_message "设置iOS签名配置..." $BLUE
    
    if [[ "$OSTYPE" != "darwin"* ]]; then
        print_message "iOS签名配置仅支持macOS" $YELLOW
        return
    fi
    
    print_message "iOS签名配置说明:" $YELLOW
    echo ""
    echo "1. 在Apple Developer Portal创建App ID:"
    echo "   - Bundle ID: com.company.enterprise-flutter"
    echo "   - 启用所需的Capabilities"
    echo ""
    echo "2. 创建Distribution Certificate"
    echo ""
    echo "3. 创建Provisioning Profile"
    echo ""
    echo "4. 在Xcode中配置签名:"
    echo "   - 打开 ios/Runner.xcworkspace"
    echo "   - 选择Runner target"
    echo "   - 在Signing & Capabilities中配置Team和Provisioning Profile"
    echo ""
    echo "5. 构建和导出:"
    echo "   flutter build ios --release"
    echo "   然后在Xcode中Archive和Export"
    echo ""
}

# 验证签名配置
verify_signing() {
    print_message "验证签名配置..." $BLUE
    
    # 验证Android签名
    if [ -f "android/key.properties" ]; then
        print_message "✓ Android签名配置存在" $GREEN
        
        # 读取配置
        source android/key.properties
        if [ -f "android/$storeFile" ]; then
            print_message "✓ Android密钥库文件存在" $GREEN
        else
            print_message "✗ Android密钥库文件不存在: android/$storeFile" $RED
        fi
    else
        print_message "✗ Android签名配置不存在" $RED
    fi
    
    # 验证iOS配置（仅macOS）
    if [[ "$OSTYPE" == "darwin"* ]]; then
        if [ -f "ios/Runner.xcworkspace" ]; then
            print_message "✓ iOS项目文件存在" $GREEN
        else
            print_message "✗ iOS项目文件不存在" $RED
        fi
    fi
}

# 主函数
main() {
    case "${1:-setup}" in
        "android")
            setup_android_signing
            ;;
        "ios")
            setup_ios_signing
            ;;
        "verify")
            verify_signing
            ;;
        "setup"|*)
            setup_android_signing
            setup_ios_signing
            verify_signing
            ;;
    esac
}

# 显示帮助信息
if [ "$1" == "--help" ] || [ "$1" == "-h" ]; then
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  setup     设置Android和iOS签名配置 (默认)"
    echo "  android   仅设置Android签名配置"
    echo "  ios       仅设置iOS签名配置"
    echo "  verify    验证签名配置"
    echo "  --help    显示此帮助信息"
    echo ""
    exit 0
fi

# 执行主函数
main "$1"

print_message "签名配置脚本执行完成！" $GREEN
