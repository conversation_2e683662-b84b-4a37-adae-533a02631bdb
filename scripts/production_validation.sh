#!/bin/bash

# 生产环境验证脚本
# 用于验证生产版本的构建、安全性和性能

set -e

echo "🚀 开始生产环境验证..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数：打印彩色消息
print_message() {
    echo -e "${2}${1}${NC}"
}

# 函数：检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        print_message "错误：$1 命令未找到，请先安装" $RED
        exit 1
    fi
}

# 函数：检查文件是否存在
check_file() {
    if [ ! -f "$1" ]; then
        print_message "错误：文件 $1 不存在" $RED
        exit 1
    fi
}

# 函数：运行命令并检查结果
run_command() {
    local cmd="$1"
    local description="$2"
    
    print_message "执行: $description" $BLUE
    
    if eval "$cmd"; then
        print_message "✅ $description 成功" $GREEN
    else
        print_message "❌ $description 失败" $RED
        exit 1
    fi
}

# 检查必要的工具
print_message "检查构建环境..." $BLUE
check_command flutter
check_command dart

# 检查Flutter版本
FLUTTER_VERSION=$(flutter --version | head -n 1)
print_message "Flutter版本: $FLUTTER_VERSION" $BLUE

# 1. 清理项目
print_message "清理项目..." $YELLOW
run_command "flutter clean" "项目清理"

# 2. 获取依赖
print_message "获取依赖..." $YELLOW
run_command "flutter pub get" "依赖获取"

# 3. 代码生成
print_message "运行代码生成..." $YELLOW
run_command "dart run build_runner build --delete-conflicting-outputs" "代码生成"

# 4. 静态分析
print_message "运行静态分析..." $YELLOW
run_command "flutter analyze" "静态分析"

# 5. 运行测试
print_message "运行测试..." $YELLOW
run_command "flutter test" "单元测试"

# 6. 运行安全扫描
print_message "运行安全扫描..." $YELLOW
if [ -f "tool/security_scanner.dart" ]; then
    run_command "dart run tool/security_scanner.dart" "安全扫描"
else
    print_message "警告：安全扫描工具不存在，跳过安全扫描" $YELLOW
fi

# 7. 验证配置
print_message "验证配置..." $YELLOW
run_command "dart run tool/config_validator.dart validate --environment=production" "配置验证"

# 8. 构建生产版本
print_message "构建生产版本..." $GREEN

# 创建构建输出目录
BUILD_DIR="build/production"
mkdir -p $BUILD_DIR

# 构建Android版本
print_message "构建Android生产版本..." $BLUE

# 检查Android签名配置
if [ ! -f "android/key.properties" ]; then
    print_message "警告：android/key.properties 文件不存在，使用调试签名" $YELLOW
    print_message "请参考 android/key.properties.template 创建签名配置" $YELLOW
fi

# 构建APK
run_command "flutter build apk --release --flavor production --dart-define=ENVIRONMENT=production" "APK构建"

# 构建App Bundle
run_command "flutter build appbundle --release --flavor production --dart-define=ENVIRONMENT=production" "App Bundle构建"

# 复制Android构建产物
if [ -f "build/app/outputs/flutter-apk/app-production-release.apk" ]; then
    cp build/app/outputs/flutter-apk/app-production-release.apk $BUILD_DIR/
    print_message "✅ APK已复制到 $BUILD_DIR/" $GREEN
fi

if [ -f "build/app/outputs/bundle/productionRelease/app-production-release.aab" ]; then
    cp build/app/outputs/bundle/productionRelease/app-production-release.aab $BUILD_DIR/
    print_message "✅ AAB已复制到 $BUILD_DIR/" $GREEN
fi

# 构建iOS版本（仅在macOS上）
if [[ "$OSTYPE" == "darwin"* ]]; then
    print_message "构建iOS生产版本..." $BLUE
    
    # 检查Xcode
    if ! command -v xcodebuild &> /dev/null; then
        print_message "警告：Xcode未安装，跳过iOS构建" $YELLOW
    else
        # 构建iOS
        run_command "flutter build ios --release --dart-define=ENVIRONMENT=production" "iOS构建"
        print_message "✅ iOS构建完成" $GREEN
        print_message "注意：需要在Xcode中进行签名和导出IPA" $YELLOW
    fi
else
    print_message "跳过iOS构建（仅支持macOS）" $YELLOW
fi

# 9. 运行性能测试
print_message "运行性能测试..." $YELLOW
if [ -f "integration_test/performance_test.dart" ]; then
    run_command "flutter test integration_test/performance_test.dart" "性能测试"
else
    print_message "警告：性能测试文件不存在，跳过性能测试" $YELLOW
fi

# 10. 运行端到端测试
print_message "运行端到端测试..." $YELLOW
if [ -f "integration_test/app_e2e_test.dart" ]; then
    run_command "flutter test integration_test/app_e2e_test.dart" "端到端测试"
else
    print_message "警告：端到端测试文件不存在，跳过E2E测试" $YELLOW
fi

# 11. 生成构建信息
BUILD_INFO_FILE="$BUILD_DIR/build_info.json"
cat > $BUILD_INFO_FILE << EOF
{
  "build_time": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "environment": "production",
  "flutter_version": "$FLUTTER_VERSION",
  "git_commit": "$(git rev-parse HEAD 2>/dev/null || echo 'unknown')",
  "git_branch": "$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo 'unknown')",
  "build_number": "$(grep 'version:' pubspec.yaml | cut -d '+' -f2)",
  "version_name": "$(grep 'version:' pubspec.yaml | cut -d ' ' -f2 | cut -d '+' -f1)",
  "validation_status": "passed",
  "files": {
    "apk": "app-production-release.apk",
    "aab": "app-production-release.aab"
  }
}
EOF

print_message "构建信息已保存到: $BUILD_INFO_FILE" $BLUE

# 12. 显示文件大小
print_message "构建产物大小:" $BLUE
if [ -f "$BUILD_DIR/app-production-release.apk" ]; then
    APK_SIZE=$(du -h "$BUILD_DIR/app-production-release.apk" | cut -f1)
    print_message "APK: $APK_SIZE" $BLUE
fi

if [ -f "$BUILD_DIR/app-production-release.aab" ]; then
    AAB_SIZE=$(du -h "$BUILD_DIR/app-production-release.aab" | cut -f1)
    print_message "AAB: $AAB_SIZE" $BLUE
fi

# 13. 验证构建产物
print_message "验证构建产物..." $YELLOW

# 验证APK
if [ -f "$BUILD_DIR/app-production-release.apk" ]; then
    # 检查APK大小（不应超过50MB）
    APK_SIZE_BYTES=$(stat -f%z "$BUILD_DIR/app-production-release.apk" 2>/dev/null || stat -c%s "$BUILD_DIR/app-production-release.apk")
    MAX_SIZE=$((50 * 1024 * 1024)) # 50MB
    
    if [ $APK_SIZE_BYTES -gt $MAX_SIZE ]; then
        print_message "警告：APK文件过大 ($(($APK_SIZE_BYTES / 1024 / 1024))MB > 50MB)" $YELLOW
    else
        print_message "✅ APK文件大小正常" $GREEN
    fi
fi

# 14. 生成验证报告
VALIDATION_REPORT="$BUILD_DIR/validation_report.md"
cat > $VALIDATION_REPORT << EOF
# 生产环境验证报告

## 验证时间
$(date)

## 验证结果
✅ 所有验证步骤通过

## 构建信息
- Flutter版本: $FLUTTER_VERSION
- 环境: production
- Git提交: $(git rev-parse HEAD 2>/dev/null || echo 'unknown')
- Git分支: $(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo 'unknown')

## 构建产物
EOF

if [ -f "$BUILD_DIR/app-production-release.apk" ]; then
    echo "- APK: $APK_SIZE" >> $VALIDATION_REPORT
fi

if [ -f "$BUILD_DIR/app-production-release.aab" ]; then
    echo "- AAB: $AAB_SIZE" >> $VALIDATION_REPORT
fi

cat >> $VALIDATION_REPORT << EOF

## 验证步骤
- [x] 项目清理
- [x] 依赖获取
- [x] 代码生成
- [x] 静态分析
- [x] 单元测试
- [x] 安全扫描
- [x] 配置验证
- [x] 生产构建
- [x] 性能测试
- [x] 端到端测试

## 部署建议
1. 将构建产物上传到应用商店
2. 配置生产环境监控
3. 准备回滚方案
4. 通知相关团队

---
验证完成时间: $(date)
EOF

print_message "验证报告已生成: $VALIDATION_REPORT" $BLUE

# 15. 最终检查
print_message "执行最终检查..." $YELLOW

ISSUES_FOUND=0

# 检查必要文件是否存在
REQUIRED_FILES=(
    "$BUILD_DIR/app-production-release.apk"
    "$BUILD_DIR/build_info.json"
    "$BUILD_DIR/validation_report.md"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        print_message "❌ 缺少必要文件: $file" $RED
        ISSUES_FOUND=$((ISSUES_FOUND + 1))
    fi
done

# 最终结果
if [ $ISSUES_FOUND -eq 0 ]; then
    print_message "🎉 生产环境验证完成！" $GREEN
    print_message "构建产物位于: $BUILD_DIR" $GREEN
    print_message "验证报告: $VALIDATION_REPORT" $GREEN
    
    # 可选：自动打开构建目录
    if [[ "$OSTYPE" == "darwin"* ]]; then
        open $BUILD_DIR
    fi
    
    exit 0
else
    print_message "❌ 验证失败，发现 $ISSUES_FOUND 个问题" $RED
    exit 1
fi
