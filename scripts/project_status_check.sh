#!/bin/bash

# 项目状态检查脚本
# 用于检查项目的完整性和就绪状态

set -e

echo "📊 开始项目状态检查..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 计数器
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# 函数：打印彩色消息
print_message() {
    echo -e "${2}${1}${NC}"
}

# 函数：执行检查
check_item() {
    local description="$1"
    local command="$2"
    local is_critical="${3:-false}"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    printf "%-50s" "$description"
    
    if eval "$command" &>/dev/null; then
        print_message "✅ 通过" $GREEN
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        if [ "$is_critical" = "true" ]; then
            print_message "❌ 失败 (关键)" $RED
        else
            print_message "⚠️  失败" $YELLOW
        fi
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
}

# 函数：检查文件是否存在
check_file_exists() {
    local file="$1"
    [ -f "$file" ]
}

# 函数：检查目录是否存在
check_dir_exists() {
    local dir="$1"
    [ -d "$dir" ]
}

print_message "🔍 项目结构检查" $BLUE
echo "=================================================="

# 检查核心目录结构
check_item "lib/ 目录存在" "check_dir_exists 'lib'"
check_item "assets/ 目录存在" "check_dir_exists 'assets'"
check_item "test/ 目录存在" "check_dir_exists 'test'"
check_item "integration_test/ 目录存在" "check_dir_exists 'integration_test'"
check_item "docs/ 目录存在" "check_dir_exists 'docs'"
check_item "scripts/ 目录存在" "check_dir_exists 'scripts'"
check_item "tool/ 目录存在" "check_dir_exists 'tool'"

echo ""
print_message "📄 核心文件检查" $BLUE
echo "=================================================="

# 检查核心文件
check_item "pubspec.yaml 存在" "check_file_exists 'pubspec.yaml'" true
check_item "lib/main.dart 存在" "check_file_exists 'lib/main.dart'" true
check_item "README.md 存在" "check_file_exists 'README.md'"
check_item "analysis_options.yaml 存在" "check_file_exists 'analysis_options.yaml'"

echo ""
print_message "⚙️ 配置文件检查" $BLUE
echo "=================================================="

# 检查配置文件
check_item "基础配置文件存在" "check_file_exists 'assets/config/app_config.yaml'" true
check_item "开发环境配置存在" "check_file_exists 'assets/config/app_config.dev.yaml'"
check_item "生产环境配置存在" "check_file_exists 'assets/config/app_config.prod.yaml'" true
check_item "Firebase配置存在" "check_file_exists 'assets/config/firebase_config_production.json'"

echo ""
print_message "🏗️ 构建配置检查" $BLUE
echo "=================================================="

# 检查构建配置
check_item "Android构建配置存在" "check_file_exists 'android/app/build.gradle.kts'" true
check_item "iOS配置文件存在" "check_file_exists 'ios/Runner/Info.plist'" true
check_item "ProGuard规则存在" "check_file_exists 'android/app/proguard-rules.pro'"
check_item "签名配置模板存在" "check_file_exists 'android/key.properties.template'"

echo ""
print_message "🔧 工具脚本检查" $BLUE
echo "=================================================="

# 检查工具脚本
check_item "生产构建脚本存在" "check_file_exists 'scripts/build_production.sh'"
check_item "签名设置脚本存在" "check_file_exists 'scripts/setup_signing.sh'"
check_item "生产验证脚本存在" "check_file_exists 'scripts/production_validation.sh'"
check_item "安全扫描工具存在" "check_file_exists 'tool/security_scanner.dart'"
check_item "配置验证工具存在" "check_file_exists 'tool/config_validator.dart'"

echo ""
print_message "📚 文档检查" $BLUE
echo "=================================================="

# 检查文档
check_item "用户手册存在" "check_file_exists 'docs/user_manual.md'"
check_item "开发者文档存在" "check_file_exists 'docs/developer_guide.md'"
check_item "项目完成总结存在" "check_file_exists 'docs/project_completion_summary.md'"

echo ""
print_message "🧪 测试文件检查" $BLUE
echo "=================================================="

# 检查测试文件
check_item "端到端测试存在" "check_file_exists 'integration_test/app_e2e_test.dart'"

echo ""
print_message "🔒 安全配置检查" $BLUE
echo "=================================================="

# 检查安全相关文件
check_item "数据保护模块存在" "check_file_exists 'lib/core/security/data_protection.dart'"

echo ""
print_message "⚡ 性能优化检查" $BLUE
echo "=================================================="

# 检查性能优化文件
check_item "启动优化器存在" "check_file_exists 'lib/core/performance/app_startup_optimizer.dart'"
check_item "内存优化器存在" "check_file_exists 'lib/core/performance/memory_optimizer.dart'"
check_item "网络优化器存在" "check_file_exists 'lib/core/performance/network_optimizer.dart'"

echo ""
print_message "🔧 环境配置检查" $BLUE
echo "=================================================="

# 检查环境配置
check_item "生产配置类存在" "check_file_exists 'lib/core/config/production_config.dart'"
check_item "环境管理器存在" "check_file_exists 'lib/core/config/environment_manager.dart'"

echo ""
print_message "📊 代码质量检查" $BLUE
echo "=================================================="

# 检查代码质量（如果工具可用）
if command -v flutter &> /dev/null; then
    check_item "Flutter分析通过" "flutter analyze --no-fatal-infos"
    check_item "依赖获取成功" "flutter pub get"
else
    print_message "Flutter未安装，跳过代码质量检查" $YELLOW
fi

echo ""
print_message "🏷️ 版本信息检查" $BLUE
echo "=================================================="

# 检查版本信息
if [ -f "pubspec.yaml" ]; then
    VERSION=$(grep '^version:' pubspec.yaml | cut -d ' ' -f2)
    if [ -n "$VERSION" ]; then
        print_message "应用版本: $VERSION" $GREEN
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        print_message "❌ 版本信息缺失" $RED
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    fi
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
fi

echo ""
print_message "📈 统计信息" $BLUE
echo "=================================================="

# 统计代码行数（如果可能）
if command -v find &> /dev/null && command -v wc &> /dev/null; then
    DART_FILES=$(find lib -name "*.dart" | wc -l)
    DART_LINES=$(find lib -name "*.dart" -exec wc -l {} + | tail -1 | awk '{print $1}')
    TEST_FILES=$(find test -name "*.dart" 2>/dev/null | wc -l)
    
    print_message "Dart文件数量: $DART_FILES" $BLUE
    print_message "代码行数: $DART_LINES" $BLUE
    print_message "测试文件数量: $TEST_FILES" $BLUE
fi

echo ""
print_message "📋 检查结果摘要" $BLUE
echo "=================================================="

print_message "总检查项目: $TOTAL_CHECKS" $BLUE
print_message "通过检查: $PASSED_CHECKS" $GREEN
print_message "失败检查: $FAILED_CHECKS" $RED

# 计算通过率
if [ $TOTAL_CHECKS -gt 0 ]; then
    PASS_RATE=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))
    print_message "通过率: $PASS_RATE%" $BLUE
fi

echo ""

# 最终评估
if [ $FAILED_CHECKS -eq 0 ]; then
    print_message "🎉 项目状态检查完全通过！" $GREEN
    print_message "项目已达到生产就绪状态" $GREEN
    exit 0
elif [ $PASS_RATE -ge 90 ]; then
    print_message "✅ 项目状态检查基本通过" $GREEN
    print_message "少数非关键项目需要完善" $YELLOW
    exit 0
elif [ $PASS_RATE -ge 80 ]; then
    print_message "⚠️  项目状态检查部分通过" $YELLOW
    print_message "建议完善失败的检查项目" $YELLOW
    exit 1
else
    print_message "❌ 项目状态检查未通过" $RED
    print_message "需要修复多个关键问题" $RED
    exit 1
fi
