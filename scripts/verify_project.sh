#!/bin/bash

# 项目验证脚本
# 检查项目的完整性和Android运行就绪状态

set -e

echo "🔍 开始项目验证..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 计数器
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# 函数：打印彩色消息
print_message() {
    echo -e "${2}${1}${NC}"
}

# 函数：执行检查
check_item() {
    local description="$1"
    local command="$2"
    local is_critical="${3:-false}"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    printf "%-60s" "$description"
    
    if eval "$command" &>/dev/null; then
        print_message "✅ 通过" $GREEN
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        if [ "$is_critical" = "true" ]; then
            print_message "❌ 失败 (关键)" $RED
        else
            print_message "⚠️  失败" $YELLOW
        fi
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
}

# 函数：检查文件是否存在
check_file_exists() {
    local file="$1"
    [ -f "$file" ]
}

# 函数：检查目录是否存在
check_dir_exists() {
    local dir="$1"
    [ -d "$dir" ]
}

print_message "📁 核心文件结构检查" $BLUE
echo "================================================================"

# 检查核心目录
check_item "lib/ 目录" "check_dir_exists 'lib'" true
check_item "android/ 目录" "check_dir_exists 'android'" true
check_item "assets/ 目录" "check_dir_exists 'assets'"
check_item "docs/ 目录" "check_dir_exists 'docs'"

# 检查核心文件
check_item "pubspec.yaml" "check_file_exists 'pubspec.yaml'" true
check_item "lib/main.dart" "check_file_exists 'lib/main.dart'" true
check_item "lib/main_simple.dart" "check_file_exists 'lib/main_simple.dart'" true
check_item "README.md" "check_file_exists 'README.md'"

echo ""
print_message "⚙️ 配置文件检查" $BLUE
echo "================================================================"

# 检查配置文件
check_item "基础配置文件" "check_file_exists 'assets/config/app_config.yaml'" true
check_item "开发环境配置" "check_file_exists 'assets/config/app_config.dev.yaml'"
check_item "生产环境配置" "check_file_exists 'assets/config/app_config.prod.yaml'"

echo ""
print_message "🏗️ 核心架构文件检查" $BLUE
echo "================================================================"

# 检查核心架构文件
check_item "依赖注入配置" "check_file_exists 'lib/core/di/injection.dart'"
check_item "简化依赖注入" "check_file_exists 'lib/core/di/simple_injection.dart'" true
check_item "功能配置" "check_file_exists 'lib/core/config/feature_config.dart'" true
check_item "环境配置" "check_file_exists 'lib/core/config/environment_config.dart'" true
check_item "环境管理器" "check_file_exists 'lib/core/config/environment_manager.dart'"

echo ""
print_message "📱 Android 配置检查" $BLUE
echo "================================================================"

# 检查Android配置
check_item "Android构建配置" "check_file_exists 'android/app/build.gradle.kts'" true
check_item "Android清单文件" "check_file_exists 'android/app/src/main/AndroidManifest.xml'" true
check_item "MainActivity" "check_file_exists 'android/app/src/main/kotlin/com/example/flutter_enterprise_app/MainActivity.kt'"
check_item "ProGuard规则" "check_file_exists 'android/app/proguard-rules.pro'"

echo ""
print_message "🔧 工具和脚本检查" $BLUE
echo "================================================================"

# 检查工具脚本
check_item "Android运行指南" "check_file_exists 'docs/android_setup_guide.md'" true
check_item "项目状态检查脚本" "check_file_exists 'scripts/project_status_check.sh'"
check_item "生产验证脚本" "check_file_exists 'scripts/production_validation.sh'"

echo ""
print_message "📚 文档检查" $BLUE
echo "================================================================"

# 检查文档
check_item "用户手册" "check_file_exists 'docs/user_manual.md'"
check_item "开发者指南" "check_file_exists 'docs/developer_guide.md'"
check_item "项目完成总结" "check_file_exists 'docs/project_completion_summary.md'"

echo ""
print_message "🔍 内容验证" $BLUE
echo "================================================================"

# 检查文件内容
if [ -f "pubspec.yaml" ]; then
    if grep -q "flutter:" pubspec.yaml; then
        print_message "✅ pubspec.yaml 包含Flutter配置" $GREEN
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        print_message "❌ pubspec.yaml 缺少Flutter配置" $RED
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    fi
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
fi

if [ -f "lib/main_simple.dart" ]; then
    if grep -q "FlutterEnterpriseApp" lib/main_simple.dart; then
        print_message "✅ main_simple.dart 包含应用类" $GREEN
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        print_message "❌ main_simple.dart 缺少应用类" $RED
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    fi
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
fi

echo ""
print_message "🛠️ 环境检查" $BLUE
echo "================================================================"

# 检查开发环境（如果可用）
if command -v flutter &> /dev/null; then
    check_item "Flutter 可用" "flutter --version"
    check_item "Flutter 医生检查" "flutter doctor --machine"
else
    print_message "⚠️  Flutter 未安装或不在PATH中" $YELLOW
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
fi

if command -v dart &> /dev/null; then
    check_item "Dart 可用" "dart --version"
else
    print_message "⚠️  Dart 未安装或不在PATH中" $YELLOW
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
fi

echo ""
print_message "📊 验证结果" $BLUE
echo "================================================================"

print_message "总检查项目: $TOTAL_CHECKS" $BLUE
print_message "通过检查: $PASSED_CHECKS" $GREEN
print_message "失败检查: $FAILED_CHECKS" $RED

# 计算通过率
if [ $TOTAL_CHECKS -gt 0 ]; then
    PASS_RATE=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))
    print_message "通过率: $PASS_RATE%" $BLUE
fi

echo ""

# 提供运行建议
print_message "🚀 运行建议" $BLUE
echo "================================================================"

if [ $PASS_RATE -ge 90 ]; then
    print_message "✅ 项目验证通过！可以尝试运行应用" $GREEN
    echo ""
    print_message "推荐运行命令:" $BLUE
    echo "1. 简化版本（推荐）: flutter run lib/main_simple.dart"
    echo "2. 完整版本: flutter run"
    echo "3. 如果Flutter未安装，请参考 docs/android_setup_guide.md"
elif [ $PASS_RATE -ge 70 ]; then
    print_message "⚠️  项目基本完整，但有一些问题需要解决" $YELLOW
    echo ""
    print_message "建议:" $BLUE
    echo "1. 先尝试运行简化版本: flutter run lib/main_simple.dart"
    echo "2. 查看失败的检查项目并修复"
    echo "3. 参考 docs/android_setup_guide.md 进行环境配置"
else
    print_message "❌ 项目存在较多问题，需要修复后再运行" $RED
    echo ""
    print_message "建议:" $BLUE
    echo "1. 检查并修复所有关键文件"
    echo "2. 确保Flutter环境正确安装"
    echo "3. 参考 docs/android_setup_guide.md 进行完整设置"
fi

echo ""
print_message "📖 更多帮助" $BLUE
echo "================================================================"
echo "- Android运行指南: docs/android_setup_guide.md"
echo "- 开发者文档: docs/developer_guide.md"
echo "- 用户手册: docs/user_manual.md"
echo "- 项目完成总结: docs/project_completion_summary.md"

# 最终退出码
if [ $PASS_RATE -ge 70 ]; then
    exit 0
else
    exit 1
fi
