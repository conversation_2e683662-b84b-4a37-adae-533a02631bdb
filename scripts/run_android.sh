#!/bin/bash

# Android运行脚本
# 检查环境并运行Flutter应用

set -e

echo "🚀 准备运行Flutter企业级应用..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数：打印彩色消息
print_message() {
    echo -e "${2}${1}${NC}"
}

# 函数：检查命令是否存在
check_command() {
    if command -v $1 &> /dev/null; then
        print_message "✅ $1 已安装" $GREEN
        return 0
    else
        print_message "❌ $1 未安装或不在PATH中" $RED
        return 1
    fi
}

# 检查环境
print_message "🔍 检查开发环境..." $BLUE
echo "=================================================="

FLUTTER_OK=false
DART_OK=false
ADB_OK=false

# 检查Flutter
if check_command flutter; then
    FLUTTER_VERSION=$(flutter --version | head -n 1)
    print_message "Flutter版本: $FLUTTER_VERSION" $BLUE
    FLUTTER_OK=true
fi

# 检查Dart
if check_command dart; then
    DART_VERSION=$(dart --version)
    print_message "Dart版本: $DART_VERSION" $BLUE
    DART_OK=true
fi

# 检查ADB
if check_command adb; then
    print_message "ADB 可用" $GREEN
    ADB_OK=true
fi

echo ""

# 如果Flutter不可用，提供安装指导
if [ "$FLUTTER_OK" = false ]; then
    print_message "⚠️  Flutter 环境未配置" $YELLOW
    echo ""
    print_message "请按照以下步骤安装Flutter:" $BLUE
    echo "1. 访问 https://flutter.dev/docs/get-started/install"
    echo "2. 下载适合您操作系统的Flutter SDK"
    echo "3. 解压并将flutter/bin目录添加到PATH"
    echo "4. 运行 'flutter doctor' 检查环境"
    echo ""
    print_message "或者使用包管理器安装:" $BLUE
    echo "# macOS (使用Homebrew)"
    echo "brew install --cask flutter"
    echo ""
    echo "# Linux (使用snap)"
    echo "sudo snap install flutter --classic"
    echo ""
    exit 1
fi

# 运行Flutter Doctor
print_message "🏥 运行Flutter Doctor..." $BLUE
echo "=================================================="
flutter doctor

echo ""

# 检查设备
print_message "📱 检查连接的设备..." $BLUE
echo "=================================================="
flutter devices

echo ""

# 获取依赖
print_message "📦 获取项目依赖..." $BLUE
echo "=================================================="
flutter pub get

echo ""

# 运行应用
print_message "🚀 启动应用..." $GREEN
echo "=================================================="

# 检查是否有连接的设备
DEVICES=$(flutter devices --machine | jq -r '.[].id' 2>/dev/null || flutter devices | grep -c "•" || echo "0")

if [ "$DEVICES" = "0" ]; then
    print_message "⚠️  没有检测到连接的设备" $YELLOW
    echo ""
    print_message "请确保:" $BLUE
    echo "1. Android设备已连接并启用USB调试"
    echo "2. 或者启动Android模拟器"
    echo "3. 或者使用Chrome浏览器运行Web版本"
    echo ""
    print_message "启动模拟器命令:" $BLUE
    echo "flutter emulators --launch <emulator_id>"
    echo ""
    print_message "查看可用模拟器:" $BLUE
    echo "flutter emulators"
    echo ""
    exit 1
fi

# 选择运行方式
echo "请选择运行方式:"
echo "1. 运行简化版本 (推荐，确保基本功能)"
echo "2. 运行完整版本"
echo "3. 运行Web版本"
echo "4. 构建APK"
echo ""
read -p "请输入选择 (1-4): " choice

case $choice in
    1)
        print_message "🚀 运行简化版本..." $GREEN
        flutter run lib/main_simple.dart
        ;;
    2)
        print_message "🚀 运行完整版本..." $GREEN
        flutter run lib/main.dart
        ;;
    3)
        print_message "🌐 运行Web版本..." $GREEN
        flutter run -d chrome
        ;;
    4)
        print_message "📦 构建APK..." $GREEN
        flutter build apk --debug
        APK_PATH="build/app/outputs/flutter-apk/app-debug.apk"
        if [ -f "$APK_PATH" ]; then
            print_message "✅ APK构建成功: $APK_PATH" $GREEN
            
            # 询问是否安装
            read -p "是否安装到连接的设备? (y/n): " install_choice
            if [[ $install_choice =~ ^[Yy]$ ]]; then
                adb install "$APK_PATH"
                print_message "✅ APK安装完成" $GREEN
            fi
        else
            print_message "❌ APK构建失败" $RED
        fi
        ;;
    *)
        print_message "❌ 无效选择，默认运行简化版本" $YELLOW
        flutter run lib/main_simple.dart
        ;;
esac

print_message "🎉 运行完成！" $GREEN
