#!/bin/bash

# 生产环境构建脚本
# 用于构建生产版本的Android APK和iOS IPA

set -e

echo "🚀 开始生产环境构建..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数：打印彩色消息
print_message() {
    echo -e "${2}${1}${NC}"
}

# 函数：检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        print_message "错误：$1 命令未找到，请先安装" $RED
        exit 1
    fi
}

# 函数：检查文件是否存在
check_file() {
    if [ ! -f "$1" ]; then
        print_message "错误：文件 $1 不存在" $RED
        exit 1
    fi
}

# 检查必要的工具
print_message "检查构建环境..." $BLUE
check_command flutter
check_command dart

# 检查Flutter版本
FLUTTER_VERSION=$(flutter --version | head -n 1)
print_message "Flutter版本: $FLUTTER_VERSION" $BLUE

# 清理项目
print_message "清理项目..." $YELLOW
flutter clean

# 获取依赖
print_message "获取依赖..." $YELLOW
flutter pub get

# 代码生成
print_message "运行代码生成..." $YELLOW
dart run build_runner build --delete-conflicting-outputs

# 运行测试
print_message "运行测试..." $YELLOW
flutter test

# 检查代码质量
print_message "检查代码质量..." $YELLOW
flutter analyze

# 创建构建输出目录
BUILD_DIR="build/production"
mkdir -p $BUILD_DIR

# 构建Android版本
print_message "构建Android生产版本..." $GREEN

# 检查Android签名配置
if [ ! -f "android/key.properties" ]; then
    print_message "警告：android/key.properties 文件不存在，使用调试签名" $YELLOW
    print_message "请参考 android/key.properties.template 创建签名配置" $YELLOW
fi

# 构建APK
print_message "构建APK..." $BLUE
flutter build apk --release --flavor production --dart-define=ENVIRONMENT=production

# 构建App Bundle
print_message "构建App Bundle..." $BLUE
flutter build appbundle --release --flavor production --dart-define=ENVIRONMENT=production

# 复制Android构建产物
cp build/app/outputs/flutter-apk/app-production-release.apk $BUILD_DIR/
cp build/app/outputs/bundle/productionRelease/app-production-release.aab $BUILD_DIR/

print_message "Android构建完成！" $GREEN
print_message "APK: $BUILD_DIR/app-production-release.apk" $GREEN
print_message "AAB: $BUILD_DIR/app-production-release.aab" $GREEN

# 构建iOS版本（仅在macOS上）
if [[ "$OSTYPE" == "darwin"* ]]; then
    print_message "构建iOS生产版本..." $GREEN
    
    # 检查Xcode
    if ! command -v xcodebuild &> /dev/null; then
        print_message "警告：Xcode未安装，跳过iOS构建" $YELLOW
    else
        # 构建iOS
        print_message "构建iOS..." $BLUE
        flutter build ios --release --dart-define=ENVIRONMENT=production
        
        print_message "iOS构建完成！" $GREEN
        print_message "注意：需要在Xcode中进行签名和导出IPA" $YELLOW
    fi
else
    print_message "跳过iOS构建（仅支持macOS）" $YELLOW
fi

# 生成构建信息
BUILD_INFO_FILE="$BUILD_DIR/build_info.json"
cat > $BUILD_INFO_FILE << EOF
{
  "build_time": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "environment": "production",
  "flutter_version": "$FLUTTER_VERSION",
  "git_commit": "$(git rev-parse HEAD 2>/dev/null || echo 'unknown')",
  "git_branch": "$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo 'unknown')",
  "build_number": "$(grep 'version:' pubspec.yaml | cut -d '+' -f2)",
  "version_name": "$(grep 'version:' pubspec.yaml | cut -d ' ' -f2 | cut -d '+' -f1)",
  "files": {
    "apk": "app-production-release.apk",
    "aab": "app-production-release.aab"
  }
}
EOF

print_message "构建信息已保存到: $BUILD_INFO_FILE" $BLUE

# 显示文件大小
print_message "构建产物大小:" $BLUE
if [ -f "$BUILD_DIR/app-production-release.apk" ]; then
    APK_SIZE=$(du -h "$BUILD_DIR/app-production-release.apk" | cut -f1)
    print_message "APK: $APK_SIZE" $BLUE
fi

if [ -f "$BUILD_DIR/app-production-release.aab" ]; then
    AAB_SIZE=$(du -h "$BUILD_DIR/app-production-release.aab" | cut -f1)
    print_message "AAB: $AAB_SIZE" $BLUE
fi

print_message "🎉 生产环境构建完成！" $GREEN
print_message "构建产物位于: $BUILD_DIR" $GREEN

# 可选：上传到分发平台
if [ "$1" == "--upload" ]; then
    print_message "准备上传到分发平台..." $YELLOW
    # 这里可以添加上传到Firebase App Distribution、TestFlight等的脚本
    print_message "注意：请手动配置分发平台的上传脚本" $YELLOW
fi

print_message "构建脚本执行完成！" $GREEN
