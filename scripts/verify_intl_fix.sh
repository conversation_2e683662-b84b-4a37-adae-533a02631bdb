#!/bin/bash

# intl版本冲突修复验证脚本

set -e

echo "🔧 验证intl版本冲突修复..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数：打印彩色消息
print_message() {
    echo -e "${2}${1}${NC}"
}

print_message "🔍 检查intl版本配置..." $BLUE
echo "================================================================"

# 检查pubspec.yaml中的intl版本
if grep -q "intl: \^0.19.0" pubspec.yaml; then
    print_message "✅ intl版本已修复为 ^0.19.0" $GREEN
else
    print_message "❌ intl版本仍有问题" $RED
    echo "当前配置:"
    grep -n "intl:" pubspec.yaml || echo "未找到intl配置"
    exit 1
fi

# 检查flutter_localizations配置
if grep -q "flutter_localizations:" pubspec.yaml; then
    print_message "✅ flutter_localizations配置正确" $GREEN
else
    print_message "❌ flutter_localizations配置缺失" $RED
    exit 1
fi

echo ""
print_message "📋 修复摘要:" $BLUE
echo "================================================================"

print_message "问题诊断:" $YELLOW
echo "❌ 原问题: intl版本冲突"
echo "   - flutter_localizations 需要 intl 0.19.0"
echo "   - pubspec.yaml 指定了 intl ^0.18.1"
echo "   - 导致版本解析失败"

echo ""
print_message "修复措施:" $GREEN
echo "✅ 更新intl版本: ^0.18.1 → ^0.19.0"
echo "✅ 保持flutter_localizations配置不变"
echo "✅ 确保版本兼容性"

echo ""
print_message "📦 当前国际化配置:" $BLUE
echo "================================================================"
echo "flutter_localizations: sdk"
echo "intl: ^0.19.0"

echo ""
print_message "🚀 下一步操作:" $BLUE
echo "================================================================"
echo "1. 运行: flutter pub get"
echo "2. 验证: flutter pub deps"
echo "3. 运行: flutter run lib/main.dart"

echo ""
print_message "💡 验证命令:" $BLUE
echo "================================================================"
echo "# 检查依赖解析"
echo "flutter pub get"
echo ""
echo "# 查看依赖树"
echo "flutter pub deps"
echo ""
echo "# 检查版本冲突"
echo "flutter pub deps --style=compact | grep intl"

echo ""
print_message "🔍 其他可能的问题:" $YELLOW
echo "================================================================"
echo "如果仍有问题，可能的原因:"
echo "1. Flutter SDK版本过旧"
echo "2. 其他包也依赖不同版本的intl"
echo "3. pub cache需要清理"
echo ""
echo "解决方案:"
echo "flutter clean"
echo "flutter pub cache repair"
echo "flutter pub get"

echo ""
print_message "✅ intl版本冲突修复验证完成！" $GREEN

# 显示当前pubspec.yaml中的国际化相关配置
echo ""
print_message "📄 当前配置确认:" $BLUE
echo "================================================================"
echo "国际化相关配置:"
grep -A 3 -B 1 "flutter_localizations\|intl:" pubspec.yaml
