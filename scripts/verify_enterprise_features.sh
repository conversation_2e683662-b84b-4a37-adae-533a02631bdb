#!/bin/bash

# 企业级功能验证脚本
# 确认第五阶段的所有企业级功能都已恢复

set -e

echo "🔍 验证企业级功能恢复状态..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数：打印彩色消息
print_message() {
    echo -e "${2}${1}${NC}"
}

# 函数：检查功能
check_feature() {
    local description="$1"
    local file="$2"
    local pattern="$3"
    
    printf "%-60s" "$description"
    
    if [ -f "$file" ] && grep -q "$pattern" "$file"; then
        print_message "✅ 已恢复" $GREEN
        return 0
    else
        print_message "❌ 缺失" $RED
        return 1
    fi
}

# 函数：检查依赖
check_dependency() {
    local dep_name="$1"
    local pattern="$2"
    
    printf "%-60s" "$dep_name"
    
    if grep -q "$pattern" "pubspec.yaml"; then
        print_message "✅ 已恢复" $GREEN
        return 0
    else
        print_message "❌ 缺失" $RED
        return 1
    fi
}

TOTAL_CHECKS=0
PASSED_CHECKS=0

print_message "📦 核心依赖验证" $BLUE
echo "================================================================"

# 检查核心依赖
check_dependency "状态管理 (flutter_bloc)" "flutter_bloc:"
TOTAL_CHECKS=$((TOTAL_CHECKS + 1)); [ $? -eq 0 ] && PASSED_CHECKS=$((PASSED_CHECKS + 1))

check_dependency "依赖注入 (injectable)" "injectable:"
TOTAL_CHECKS=$((TOTAL_CHECKS + 1)); [ $? -eq 0 ] && PASSED_CHECKS=$((PASSED_CHECKS + 1))

check_dependency "网络请求 (retrofit)" "retrofit:"
TOTAL_CHECKS=$((TOTAL_CHECKS + 1)); [ $? -eq 0 ] && PASSED_CHECKS=$((PASSED_CHECKS + 1))

check_dependency "数据库 (drift)" "drift:"
TOTAL_CHECKS=$((TOTAL_CHECKS + 1)); [ $? -eq 0 ] && PASSED_CHECKS=$((PASSED_CHECKS + 1))

check_dependency "路由导航 (go_router)" "go_router:"
TOTAL_CHECKS=$((TOTAL_CHECKS + 1)); [ $? -eq 0 ] && PASSED_CHECKS=$((PASSED_CHECKS + 1))

echo ""
print_message "🔥 Firebase 集成验证" $BLUE
echo "================================================================"

check_dependency "Firebase 核心" "firebase_core:"
TOTAL_CHECKS=$((TOTAL_CHECKS + 1)); [ $? -eq 0 ] && PASSED_CHECKS=$((PASSED_CHECKS + 1))

check_dependency "Firebase 分析" "firebase_analytics:"
TOTAL_CHECKS=$((TOTAL_CHECKS + 1)); [ $? -eq 0 ] && PASSED_CHECKS=$((PASSED_CHECKS + 1))

check_dependency "Firebase 消息推送" "firebase_messaging:"
TOTAL_CHECKS=$((TOTAL_CHECKS + 1)); [ $? -eq 0 ] && PASSED_CHECKS=$((PASSED_CHECKS + 1))

check_dependency "Firebase 性能监控" "firebase_performance:"
TOTAL_CHECKS=$((TOTAL_CHECKS + 1)); [ $? -eq 0 ] && PASSED_CHECKS=$((PASSED_CHECKS + 1))

check_dependency "Firebase 崩溃报告" "firebase_crashlytics:"
TOTAL_CHECKS=$((TOTAL_CHECKS + 1)); [ $? -eq 0 ] && PASSED_CHECKS=$((PASSED_CHECKS + 1))

echo ""
print_message "🔒 安全功能验证" $BLUE
echo "================================================================"

check_dependency "安全存储" "flutter_secure_storage:"
TOTAL_CHECKS=$((TOTAL_CHECKS + 1)); [ $? -eq 0 ] && PASSED_CHECKS=$((PASSED_CHECKS + 1))

check_dependency "生物识别" "local_auth:"
TOTAL_CHECKS=$((TOTAL_CHECKS + 1)); [ $? -eq 0 ] && PASSED_CHECKS=$((PASSED_CHECKS + 1))

check_dependency "证书固定" "certificate_pinning:"
TOTAL_CHECKS=$((TOTAL_CHECKS + 1)); [ $? -eq 0 ] && PASSED_CHECKS=$((PASSED_CHECKS + 1))

check_dependency "权限管理" "permission_handler:"
TOTAL_CHECKS=$((TOTAL_CHECKS + 1)); [ $? -eq 0 ] && PASSED_CHECKS=$((PASSED_CHECKS + 1))

echo ""
print_message "🛠️ 开发工具验证" $BLUE
echo "================================================================"

check_dependency "代码生成 (build_runner)" "build_runner:"
TOTAL_CHECKS=$((TOTAL_CHECKS + 1)); [ $? -eq 0 ] && PASSED_CHECKS=$((PASSED_CHECKS + 1))

check_dependency "代码生成 (injectable_generator)" "injectable_generator:"
TOTAL_CHECKS=$((TOTAL_CHECKS + 1)); [ $? -eq 0 ] && PASSED_CHECKS=$((PASSED_CHECKS + 1))

check_dependency "测试工具 (bloc_test)" "bloc_test:"
TOTAL_CHECKS=$((TOTAL_CHECKS + 1)); [ $? -eq 0 ] && PASSED_CHECKS=$((PASSED_CHECKS + 1))

check_dependency "代码质量 (very_good_analysis)" "very_good_analysis:"
TOTAL_CHECKS=$((TOTAL_CHECKS + 1)); [ $? -eq 0 ] && PASSED_CHECKS=$((PASSED_CHECKS + 1))

echo ""
print_message "🏗️ Android 企业级配置验证" $BLUE
echo "================================================================"

check_feature "多环境构建配置" "android/app/build.gradle.kts" "flavorDimensions"
TOTAL_CHECKS=$((TOTAL_CHECKS + 1)); [ $? -eq 0 ] && PASSED_CHECKS=$((PASSED_CHECKS + 1))

check_feature "产品风味配置" "android/app/build.gradle.kts" "productFlavors"
TOTAL_CHECKS=$((TOTAL_CHECKS + 1)); [ $? -eq 0 ] && PASSED_CHECKS=$((PASSED_CHECKS + 1))

check_feature "多DEX支持" "android/app/build.gradle.kts" "multiDexEnabled"
TOTAL_CHECKS=$((TOTAL_CHECKS + 1)); [ $? -eq 0 ] && PASSED_CHECKS=$((PASSED_CHECKS + 1))

check_feature "ProGuard配置" "android/app/build.gradle.kts" "proguardFiles"
TOTAL_CHECKS=$((TOTAL_CHECKS + 1)); [ $? -eq 0 ] && PASSED_CHECKS=$((PASSED_CHECKS + 1))

check_feature "企业级仓库配置" "android/build.gradle.kts" "jitpack.io"
TOTAL_CHECKS=$((TOTAL_CHECKS + 1)); [ $? -eq 0 ] && PASSED_CHECKS=$((PASSED_CHECKS + 1))

check_feature "企业级构建任务" "android/build.gradle.kts" "codeQualityCheck"
TOTAL_CHECKS=$((TOTAL_CHECKS + 1)); [ $? -eq 0 ] && PASSED_CHECKS=$((PASSED_CHECKS + 1))

echo ""
print_message "📁 资源配置验证" $BLUE
echo "================================================================"

check_feature "Assets配置" "pubspec.yaml" "assets:"
TOTAL_CHECKS=$((TOTAL_CHECKS + 1)); [ $? -eq 0 ] && PASSED_CHECKS=$((PASSED_CHECKS + 1))

check_feature "国际化配置" "pubspec.yaml" "flutter_localizations:"
TOTAL_CHECKS=$((TOTAL_CHECKS + 1)); [ $? -eq 0 ] && PASSED_CHECKS=$((PASSED_CHECKS + 1))

echo ""
print_message "📊 验证结果" $BLUE
echo "================================================================"

print_message "总检查项目: $TOTAL_CHECKS" $BLUE
print_message "已恢复功能: $PASSED_CHECKS" $GREEN
MISSING_CHECKS=$((TOTAL_CHECKS - PASSED_CHECKS))
print_message "缺失功能: $MISSING_CHECKS" $RED

# 计算恢复率
if [ $TOTAL_CHECKS -gt 0 ]; then
    RECOVERY_RATE=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))
    print_message "恢复率: $RECOVERY_RATE%" $BLUE
fi

echo ""
print_message "📋 恢复摘要" $BLUE
echo "================================================================"

if [ $RECOVERY_RATE -ge 90 ]; then
    print_message "✅ 企业级功能恢复完成！" $GREEN
    echo ""
    print_message "已恢复的主要功能:" $GREEN
    echo "✅ 完整的依赖注入系统"
    echo "✅ 状态管理和架构支持"
    echo "✅ 网络请求和数据持久化"
    echo "✅ Firebase 完整集成"
    echo "✅ 安全功能和权限管理"
    echo "✅ 多环境构建配置"
    echo "✅ 企业级构建任务"
    echo "✅ 代码生成和质量工具"
elif [ $RECOVERY_RATE -ge 70 ]; then
    print_message "⚠️  大部分功能已恢复，但仍有少量缺失" $YELLOW
else
    print_message "❌ 功能恢复不完整，需要进一步检查" $RED
fi

echo ""
print_message "🚀 下一步操作" $BLUE
echo "================================================================"
echo "1. 运行代码生成: dart run build_runner build --delete-conflicting-outputs"
echo "2. 清理项目: flutter clean"
echo "3. 获取依赖: flutter pub get"
echo "4. 运行应用: flutter run lib/main.dart"

echo ""
print_message "✅ 企业级功能验证完成！" $GREEN
