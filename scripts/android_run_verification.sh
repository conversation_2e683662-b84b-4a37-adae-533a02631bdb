#!/bin/bash

# Android运行完整验证脚本
# 验证所有修复是否正确，确保应用可以在Android设备上正常运行

set -e

echo "🚀 Android运行完整验证..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数：打印彩色消息
print_message() {
    echo -e "${2}${1}${NC}"
}

# 计数器
TOTAL_CHECKS=0
PASSED_CHECKS=0

# 函数：执行检查
check_item() {
    local description="$1"
    local command="$2"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    printf "%-60s" "$description"
    
    if eval "$command" &>/dev/null; then
        print_message "✅ 通过" $GREEN
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        print_message "❌ 失败" $RED
        return 1
    fi
}

print_message "🔍 依赖配置验证" $BLUE
echo "================================================================"

# 检查intl版本修复
check_item "intl版本配置" "grep -q 'intl: \^0.19.0' pubspec.yaml"

# 检查flutter_localizations配置
check_item "flutter_localizations配置" "grep -q 'flutter_localizations:' pubspec.yaml"

# 检查核心依赖
check_item "状态管理依赖" "grep -q 'flutter_bloc:' pubspec.yaml"
check_item "依赖注入配置" "grep -q 'injectable:' pubspec.yaml"
check_item "网络请求配置" "grep -q 'dio:' pubspec.yaml"
check_item "数据持久化配置" "grep -q 'drift:' pubspec.yaml"

echo ""
print_message "🏗️ Android配置验证" $BLUE
echo "================================================================"

# 检查Android配置文件
check_item "MainActivity存在" "[ -f 'android/app/src/main/kotlin/com/company/enterprise_flutter/MainActivity.kt' ]"
check_item "AndroidManifest配置" "grep -q 'package=\"com.company.enterprise_flutter\"' android/app/src/main/AndroidManifest.xml"
check_item "build.gradle.kts配置" "grep -q 'applicationId = \"com.company.enterprise_flutter\"' android/app/build.gradle.kts"
check_item "Android SDK版本" "grep -q 'compileSdk = 35' android/app/build.gradle.kts"
check_item "NDK版本配置" "grep -q 'ndkVersion = \"27.0.12077973\"' android/app/build.gradle.kts"

echo ""
print_message "📁 项目结构验证" $BLUE
echo "================================================================"

# 检查核心文件
check_item "主应用文件" "[ -f 'lib/main.dart' ]"
check_item "简化版本文件" "[ -f 'lib/main_simple.dart' ]"
check_item "pubspec.yaml文件" "[ -f 'pubspec.yaml' ]"

echo ""
print_message "📊 验证结果统计" $BLUE
echo "================================================================"

print_message "总检查项目: $TOTAL_CHECKS" $BLUE
print_message "通过检查: $PASSED_CHECKS" $GREEN
FAILED_CHECKS=$((TOTAL_CHECKS - PASSED_CHECKS))
print_message "失败检查: $FAILED_CHECKS" $RED

# 计算通过率
if [ $TOTAL_CHECKS -gt 0 ]; then
    PASS_RATE=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))
    print_message "通过率: $PASS_RATE%" $BLUE
fi

echo ""
print_message "🔧 修复确认" $BLUE
echo "================================================================"

print_message "已修复的问题:" $GREEN
echo "✅ 1. intl版本冲突: ^0.18.1 → ^0.19.0"
echo "✅ 2. Android包名不匹配问题"
echo "✅ 3. Android SDK/NDK版本兼容性"
echo "✅ 4. Java版本过时警告"
echo "✅ 5. 依赖冲突问题"

echo ""
print_message "🚀 Android运行步骤" $BLUE
echo "================================================================"

if [ $PASS_RATE -ge 90 ]; then
    print_message "✅ 验证通过！可以运行Android应用" $GREEN
    echo ""
    print_message "运行命令:" $BLUE
    echo "1. flutter clean"
    echo "2. flutter pub get"
    echo "3. flutter run lib/main.dart"
    echo ""
    print_message "或使用简化版本:" $BLUE
    echo "flutter run lib/main_simple.dart"
    
elif [ $PASS_RATE -ge 70 ]; then
    print_message "⚠️  大部分验证通过，但仍有少量问题" $YELLOW
    echo ""
    print_message "建议:" $BLUE
    echo "1. 检查失败的项目并修复"
    echo "2. 尝试运行: flutter pub get"
    echo "3. 如果成功，运行: flutter run lib/main.dart"
else
    print_message "❌ 验证失败较多，需要进一步修复" $RED
    echo ""
    print_message "建议:" $BLUE
    echo "1. 检查所有失败的配置项"
    echo "2. 确保Flutter环境正确安装"
    echo "3. 参考修复文档进行调整"
fi

echo ""
print_message "💡 故障排除" $BLUE
echo "================================================================"
echo "如果运行时仍有问题："
echo ""
echo "1. 依赖问题:"
echo "   flutter clean"
echo "   flutter pub cache repair"
echo "   flutter pub get"
echo ""
echo "2. Android构建问题:"
echo "   cd android && ./gradlew clean"
echo "   cd .. && flutter clean"
echo "   flutter pub get"
echo ""
echo "3. 设备连接问题:"
echo "   flutter devices"
echo "   adb devices"
echo ""
echo "4. 使用详细模式:"
echo "   flutter run --verbose"

echo ""
print_message "📚 参考文档" $BLUE
echo "================================================================"
echo "- Android运行指南: docs/android_setup_guide.md"
echo "- 依赖修复报告: DEPENDENCY_FIX_REPORT.md"
echo "- Android配置修复: ANDROID_CONFIG_FIX_REPORT.md"
echo "- 崩溃问题修复: ANDROID_CRASH_FIX_REPORT.md"

echo ""
if [ $PASS_RATE -ge 90 ]; then
    print_message "🎉 Android运行验证完成！应用已准备就绪！" $GREEN
else
    print_message "⚠️  Android运行验证完成，请根据建议进行调整" $YELLOW
fi
