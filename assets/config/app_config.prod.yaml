# 生产环境配置
# 继承基础配置并覆盖特定设置

app:
  name: "Enterprise Flutter App"
  version: "1.0.0"
  environment: "production"
  debug: false

# API配置
api:
  base_url: "https://api.production.company.com"
  timeout: 30000
  retry_attempts: 3
  enable_logging: false

# 功能模块配置 - 生产环境全部启用
features:
  # 核心功能 - 生产环境全部启用
  authentication: true
  network: true
  database: true
  state_management: true
  error_handling: true

  # UI/UX 功能 - 生产环境全部启用
  design_system: true
  theming: true
  internationalization: true
  routing: true
  performance_monitoring: true

  # 高级功能 - 根据需求启用
  analytics: true
  push_notifications: true
  offline_support: true
  security_enhanced: true

  # 开发工具 - 生产环境禁用
  dev_tools: false
  debug_features: false

# 安全配置
security:
  encryption_enabled: true
  certificate_pinning: true
  root_detection: true
  debug_protection: true

# 日志配置
logging:
  level: "WARNING"
  remote_logging: true
  crash_reporting: true
  enable_console: false
  enable_file: true

# 性能配置
performance:
  lazy_loading: true
  image_caching: true
  network_caching: true
  memory_optimization: true
  max_cache_size: *********  # 100MB

# 数据库配置
database:
  name: "app_prod.db"
  enable_logging: false
  encryption_enabled: true
