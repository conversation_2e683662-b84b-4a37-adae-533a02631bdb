import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/services.dart';
import 'dart:math';
import 'dart:typed_data';

// 导入第四阶段的功能模块
import 'package:flutter_template/features/analytics/domain/entities/tracking_event.dart';
import 'package:flutter_template/features/analytics/domain/entities/user_behavior.dart';
import 'package:flutter_template/features/notifications/domain/entities/push_notification.dart';
import 'package:flutter_template/features/offline/domain/entities/offline_data.dart';
import 'package:flutter_template/features/security/domain/entities/encryption_key.dart';
import 'package:flutter_template/features/enterprise/domain/entities/system_monitor.dart';
import 'package:flutter_template/features/enterprise/domain/entities/config_management.dart';

void main() {
  group('第四阶段：高级功能性能测试', () {
    group('分析统计系统性能测试', () {
      test('事件追踪性能测试', () async {
        final stopwatch = Stopwatch()..start();

        // 测试大量事件创建性能
        const eventCount = 10000;
        final events = <TrackingEvent>[];

        for (int i = 0; i < eventCount; i++) {
          final event = TrackingEvent.pageView(
            id: 'event_$i',
            pagePath: '/page_$i',
            pageTitle: 'Page $i',
            userId: 'user_${i % 100}',
            sessionId: 'session_${i % 50}',
            properties: {
              'index': i,
              'timestamp': DateTime.now().millisecondsSinceEpoch,
              'random_data': _generateRandomString(100),
            },
          );
          events.add(event);
        }

        stopwatch.stop();
        final creationTime = stopwatch.elapsedMilliseconds;

        print('创建 $eventCount 个事件耗时: ${creationTime}ms');
        print('平均每个事件创建时间: ${creationTime / eventCount}ms');

        // 性能要求：平均每个事件创建时间应小于0.1ms
        expect(creationTime / eventCount, lessThan(0.1));

        // 测试事件序列化性能
        stopwatch.reset();
        stopwatch.start();

        final serializedEvents = events.map((e) => e.toJson()).toList();

        stopwatch.stop();
        final serializationTime = stopwatch.elapsedMilliseconds;

        print('序列化 $eventCount 个事件耗时: ${serializationTime}ms');
        print('平均每个事件序列化时间: ${serializationTime / eventCount}ms');

        // 性能要求：平均每个事件序列化时间应小于0.05ms
        expect(serializationTime / eventCount, lessThan(0.05));

        // 测试内存使用
        final eventSize = _calculateObjectSize(events.first.toJson());
        final totalMemory = eventSize * eventCount;

        print('单个事件大小: ${eventSize}字节');
        print('总内存使用: ${totalMemory / 1024 / 1024}MB');

        // 内存要求：总内存使用应小于50MB
        expect(totalMemory / 1024 / 1024, lessThan(50));
      });

      test('用户行为分析性能测试', () async {
        final stopwatch = Stopwatch()..start();

        // 测试大量行为数据处理性能
        const behaviorCount = 5000;
        final behaviors = <UserBehavior>[];

        for (int i = 0; i < behaviorCount; i++) {
          final behavior = UserBehavior.pageVisit(
            id: 'behavior_$i',
            userId: 'user_${i % 100}',
            sessionId: 'session_${i % 50}',
            pagePath: '/page_${i % 20}',
            pageTitle: 'Page ${i % 20}',
            properties: {
              'visit_duration': Random().nextInt(300000), // 0-5分钟
              'scroll_depth': Random().nextDouble() * 100,
              'interactions': Random().nextInt(50),
            },
          );
          behaviors.add(behavior);
        }

        stopwatch.stop();
        final creationTime = stopwatch.elapsedMilliseconds;

        print('创建 $behaviorCount 个行为数据耗时: ${creationTime}ms');

        // 测试行为数据聚合性能
        stopwatch.reset();
        stopwatch.start();

        // 按用户分组统计
        final userStats = <String, Map<String, dynamic>>{};
        for (final behavior in behaviors) {
          final userId = behavior.userId;
          if (!userStats.containsKey(userId)) {
            userStats[userId] = {
              'total_visits': 0,
              'total_duration': 0,
              'unique_pages': <String>{},
            };
          }

          userStats[userId]!['total_visits'] =
              (userStats[userId]!['total_visits'] as int) + 1;
          userStats[userId]!['total_duration'] =
              (userStats[userId]!['total_duration'] as int) +
              (behavior.properties['visit_duration'] as int? ?? 0);
          (userStats[userId]!['unique_pages'] as Set<String>)
              .add(behavior.pagePath ?? '');
        }

        stopwatch.stop();
        final aggregationTime = stopwatch.elapsedMilliseconds;

        print('聚合 $behaviorCount 个行为数据耗时: ${aggregationTime}ms');
        print('生成 ${userStats.length} 个用户统计');

        // 性能要求：聚合时间应小于100ms
        expect(aggregationTime, lessThan(100));
      });
    });

    group('推送通知系统性能测试', () {
      test('通知创建和处理性能测试', () async {
        final stopwatch = Stopwatch()..start();

        // 测试大量通知创建性能
        const notificationCount = 20000;
        final notifications = <PushNotification>[];

        for (int i = 0; i < notificationCount; i++) {
          final notification = PushNotification.system(
            id: 'notification_$i',
            title: '系统通知 $i',
            body: '这是第 $i 个系统通知，包含一些重要信息。',
            userId: 'user_${i % 1000}',
            data: {
              'notification_id': i,
              'priority': i % 3,
              'category': 'system',
              'timestamp': DateTime.now().millisecondsSinceEpoch,
            },
          );
          notifications.add(notification);
        }

        stopwatch.stop();
        final creationTime = stopwatch.elapsedMilliseconds;

        print('创建 $notificationCount 个通知耗时: ${creationTime}ms');
        print('平均每个通知创建时间: ${creationTime / notificationCount}ms');

        // 性能要求：平均每个通知创建时间应小于0.05ms
        expect(creationTime / notificationCount, lessThan(0.05));

        // 测试通知过滤性能
        stopwatch.reset();
        stopwatch.start();

        // 模拟用户通知设置过滤
        final filteredNotifications = notifications.where((notification) {
          // 模拟复杂的过滤逻辑
          final priority = notification.data['priority'] as int;
          final userId = notification.userId;

          // 模拟用户设置检查
          if (priority == 0 && userId?.hashCode.isEven == true) return false;
          if (notification.title.contains('系统') && priority > 1) return true;

          return true;
        }).toList();

        stopwatch.stop();
        final filterTime = stopwatch.elapsedMilliseconds;

        print('过滤 $notificationCount 个通知耗时: ${filterTime}ms');
        print('过滤后剩余: ${filteredNotifications.length} 个通知');

        // 性能要求：过滤时间应小于50ms
        expect(filterTime, lessThan(50));
      });

      test('通知批量操作性能测试', () async {
        // 测试批量状态更新性能
        const batchSize = 1000;
        final notifications = List.generate(batchSize, (i) =>
          PushNotification.marketing(
            id: 'batch_notification_$i',
            title: '营销通知 $i',
            body: '限时优惠活动，不要错过！',
            userGroups: ['group_${i % 10}'],
          ),
        );

        final stopwatch = Stopwatch()..start();

        // 批量标记为已发送
        final sentNotifications = notifications.map((n) => n.markAsSent()).toList();

        stopwatch.stop();
        final batchUpdateTime = stopwatch.elapsedMilliseconds;

        print('批量更新 $batchSize 个通知状态耗时: ${batchUpdateTime}ms');

        // 验证所有通知都已更新
        expect(sentNotifications.every((n) => n.status == NotificationStatus.sent), true);

        // 性能要求：批量更新时间应小于10ms
        expect(batchUpdateTime, lessThan(10));
      });
    });

    group('离线支持系统性能测试', () {
      test('离线数据存储性能测试', () async {
        final stopwatch = Stopwatch()..start();

        // 测试大量离线数据创建性能
        const dataCount = 15000;
        final offlineDataList = <OfflineData>[];

        for (int i = 0; i < dataCount; i++) {
          final data = OfflineData.create(
            id: 'offline_data_$i',
            dataType: 'user_content',
            data: {
              'content_id': i,
              'title': '内容标题 $i',
              'body': _generateRandomString(500), // 500字符的内容
              'tags': List.generate(5, (j) => 'tag_${i}_$j'),
              'metadata': {
                'created_at': DateTime.now().toIso8601String(),
                'version': 1,
                'size': 500,
              },
            },
            userId: 'user_${i % 100}',
            deviceId: 'device_${i % 10}',
          );
          offlineDataList.add(data);
        }

        stopwatch.stop();
        final creationTime = stopwatch.elapsedMilliseconds;

        print('创建 $dataCount 个离线数据耗时: ${creationTime}ms');
        print('平均每个数据创建时间: ${creationTime / dataCount}ms');

        // 性能要求：平均每个数据创建时间应小于0.1ms
        expect(creationTime / dataCount, lessThan(0.1));

        // 测试数据序列化性能
        stopwatch.reset();
        stopwatch.start();

        final serializedData = offlineDataList.map((d) => d.toJson()).toList();

        stopwatch.stop();
        final serializationTime = stopwatch.elapsedMilliseconds;

        print('序列化 $dataCount 个离线数据耗时: ${serializationTime}ms');

        // 测试数据压缩模拟
        stopwatch.reset();
        stopwatch.start();

        var totalSize = 0;
        for (final data in serializedData) {
          totalSize += _calculateObjectSize(data);
        }

        stopwatch.stop();
        final sizeCalculationTime = stopwatch.elapsedMilliseconds;

        print('计算 $dataCount 个数据大小耗时: ${sizeCalculationTime}ms');
        print('总数据大小: ${totalSize / 1024 / 1024}MB');

        // 性能要求：序列化时间应小于200ms
        expect(serializationTime, lessThan(200));
      });

      test('离线数据同步性能测试', () async {
        // 模拟同步队列处理性能
        const syncBatchSize = 100;
        final syncQueue = List.generate(syncBatchSize, (i) =>
          OfflineData.update(
            id: 'sync_data_$i',
            dataType: 'sync_content',
            data: {
              'id': i,
              'content': _generateRandomString(200),
              'last_modified': DateTime.now().toIso8601String(),
            },
            version: Random().nextInt(10) + 1,
            userId: 'user_${i % 20}',
          ),
        );

        final stopwatch = Stopwatch()..start();

        // 模拟同步处理：状态更新和冲突检测
        final processedData = <OfflineData>[];
        for (final data in syncQueue) {
          // 模拟网络延迟和处理时间
          await Future.delayed(Duration(microseconds: 100));

          // 模拟冲突检测
          final hasConflict = Random().nextBool() && Random().nextDouble() < 0.1;

          final processedItem = hasConflict
              ? data.markAsConflict(
                  conflictData: {'server_version': data.version + 1},
                  serverVersion: data.version + 1,
                )
              : data.markAsSynced(serverVersion: data.version);

          processedData.add(processedItem);
        }

        stopwatch.stop();
        final syncTime = stopwatch.elapsedMilliseconds;

        print('同步处理 $syncBatchSize 个数据耗时: ${syncTime}ms');
        print('平均每个数据同步时间: ${syncTime / syncBatchSize}ms');

        final conflictCount = processedData.where((d) => d.hasConflict).length;
        print('检测到 $conflictCount 个冲突');

        // 性能要求：平均每个数据同步时间应小于2ms（不包括网络延迟）
        expect((syncTime - syncBatchSize * 0.1) / syncBatchSize, lessThan(2));
      });
    });

    group('安全增强系统性能测试', () {
      test('加密密钥操作性能测试', () async {
        final stopwatch = Stopwatch()..start();

        // 测试大量密钥创建性能
        const keyCount = 1000;
        final keys = <EncryptionKey>[];

        for (int i = 0; i < keyCount; i++) {
          final key = EncryptionKey.aes256(
            keyId: 'key_$i',
            alias: 'data_key_$i',
            keyData: _generateRandomString(64), // 模拟加密的密钥数据
            purposes: ['encryption', 'decryption'],
            createdBy: 'system',
          );
          keys.add(key);
        }

        stopwatch.stop();
        final keyCreationTime = stopwatch.elapsedMilliseconds;

        print('创建 $keyCount 个密钥耗时: ${keyCreationTime}ms');
        print('平均每个密钥创建时间: ${keyCreationTime / keyCount}ms');

        // 性能要求：平均每个密钥创建时间应小于0.5ms
        expect(keyCreationTime / keyCount, lessThan(0.5));

        // 测试密钥查找性能
        stopwatch.reset();
        stopwatch.start();

        // 模拟密钥查找操作
        const lookupCount = 10000;
        var foundKeys = 0;

        for (int i = 0; i < lookupCount; i++) {
          final targetKeyId = 'key_${Random().nextInt(keyCount)}';
          final foundKey = keys.where((k) => k.keyId == targetKeyId).firstOrNull;
          if (foundKey != null) foundKeys++;
        }

        stopwatch.stop();
        final lookupTime = stopwatch.elapsedMilliseconds;

        print('执行 $lookupCount 次密钥查找耗时: ${lookupTime}ms');
        print('找到 $foundKeys 个密钥');
        print('平均每次查找时间: ${lookupTime / lookupCount}ms');

        // 性能要求：平均每次查找时间应小于0.01ms
        expect(lookupTime / lookupCount, lessThan(0.01));
      });

      test('安全配置验证性能测试', () async {
        // 测试大量安全配置验证性能
        const configCount = 5000;
        final configs = List.generate(configCount, (i) => {
          'user_data': Random().nextBool() ? 'confidential' : 'internal',
          'api_keys': 'secret',
          'logs': 'internal',
          'cache_data': 'public',
        });

        final stopwatch = Stopwatch()..start();

        var encryptionDecisions = 0;
        for (final config in configs) {
          for (final entry in config.entries) {
            // 模拟安全策略检查
            final dataType = entry.key;
            final classification = entry.value;

            final shouldEncrypt = classification == 'secret' ||
                                classification == 'confidential' ||
                                (classification == 'internal' && dataType.contains('user'));

            if (shouldEncrypt) encryptionDecisions++;
          }
        }

        stopwatch.stop();
        final validationTime = stopwatch.elapsedMilliseconds;

        print('验证 ${configCount * 4} 个安全配置耗时: ${validationTime}ms');
        print('需要加密的数据: $encryptionDecisions 项');

        // 性能要求：验证时间应小于50ms
        expect(validationTime, lessThan(50));
      });
    });

    group('企业级管理功能性能测试', () {
      test('系统监控数据处理性能测试', () async {
        final stopwatch = Stopwatch()..start();

        // 测试大量监控数据创建和处理性能
        const monitorCount = 10000;
        final monitors = <SystemMonitor>[];

        for (int i = 0; i < monitorCount; i++) {
          final monitor = SystemMonitor.cpu(
            id: 'monitor_$i',
            cpuUsage: Random().nextDouble() * 100,
            threshold: 80.0,
            tags: {
              'server': 'server_${i % 50}',
              'environment': i % 3 == 0 ? 'production' : 'staging',
              'region': 'region_${i % 5}',
            },
          );
          monitors.add(monitor);
        }

        stopwatch.stop();
        final creationTime = stopwatch.elapsedMilliseconds;

        print('创建 $monitorCount 个监控数据耗时: ${creationTime}ms');

        // 测试监控数据聚合性能
        stopwatch.reset();
        stopwatch.start();

        // 按服务器分组统计
        final serverStats = <String, Map<String, dynamic>>{};
        for (final monitor in monitors) {
          final server = monitor.tags['server']!;
          if (!serverStats.containsKey(server)) {
            serverStats[server] = {
              'total_monitors': 0,
              'avg_cpu': 0.0,
              'max_cpu': 0.0,
              'alerts': 0,
            };
          }

          final stats = serverStats[server]!;
          stats['total_monitors'] = (stats['total_monitors'] as int) + 1;
          stats['avg_cpu'] = ((stats['avg_cpu'] as double) + monitor.currentValue) / 2;
          stats['max_cpu'] = math.max(stats['max_cpu'] as double, monitor.currentValue);
          if (monitor.isAlerted) stats['alerts'] = (stats['alerts'] as int) + 1;
        }

        stopwatch.stop();
        final aggregationTime = stopwatch.elapsedMilliseconds;

        print('聚合 $monitorCount 个监控数据耗时: ${aggregationTime}ms');
        print('生成 ${serverStats.length} 个服务器统计');

        // 性能要求：聚合时间应小于100ms
        expect(aggregationTime, lessThan(100));
      });

      test('配置管理批量操作性能测试', () async {
        // 测试大量配置管理操作性能
        const configCount = 2000;
        final configs = List.generate(configCount, (i) =>
          ConfigManagement.application(
            id: 'config_$i',
            key: 'app.setting_$i',
            name: '应用设置 $i',
            value: Random().nextBool() ? Random().nextInt(1000) : _generateRandomString(50),
            environment: ConfigEnvironment.values[i % ConfigEnvironment.values.length],
            description: '这是第 $i 个应用配置设置',
            dataType: Random().nextBool() ? 'int' : 'string',
            isRequired: Random().nextBool(),
            createdBy: 'admin_${i % 10}',
          ),
        );

        final stopwatch = Stopwatch()..start();

        // 测试配置验证性能
        var validConfigs = 0;
        for (final config in configs) {
          if (config.validateValue(config.value)) {
            validConfigs++;
          }
        }

        stopwatch.stop();
        final validationTime = stopwatch.elapsedMilliseconds;

        print('验证 $configCount 个配置耗时: ${validationTime}ms');
        print('有效配置: $validConfigs 个');

        // 测试配置序列化性能
        stopwatch.reset();
        stopwatch.start();

        final serializedConfigs = configs.map((c) => c.toJson()).toList();

        stopwatch.stop();
        final serializationTime = stopwatch.elapsedMilliseconds;

        print('序列化 $configCount 个配置耗时: ${serializationTime}ms');

        // 性能要求：验证时间应小于50ms，序列化时间应小于100ms
        expect(validationTime, lessThan(50));
        expect(serializationTime, lessThan(100));
      });
    });

    group('内存和资源使用测试', () {
      test('内存使用优化测试', () async {
        // 测试大量对象创建后的内存使用
        const objectCount = 50000;
        final objects = <dynamic>[];

        // 创建混合类型的对象
        for (int i = 0; i < objectCount; i++) {
          switch (i % 5) {
            case 0:
              objects.add(TrackingEvent.pageView(
                id: 'event_$i',
                pagePath: '/page',
                userId: 'user',
                sessionId: 'session',
              ));
              break;
            case 1:
              objects.add(PushNotification.system(
                id: 'notification_$i',
                title: 'Title',
                body: 'Body',
              ));
              break;
            case 2:
              objects.add(OfflineData.create(
                id: 'data_$i',
                dataType: 'test',
                data: {'value': i},
              ));
              break;
            case 3:
              objects.add(SystemMonitor.cpu(
                id: 'monitor_$i',
                cpuUsage: 50.0,
              ));
              break;
            case 4:
              objects.add(ConfigManagement.application(
                id: 'config_$i',
                key: 'key_$i',
                name: 'Config $i',
                value: i,
                createdBy: 'system',
              ));
              break;
          }
        }

        // 估算内存使用
        final sampleSize = _calculateObjectSize(objects.first.toJson());
        final estimatedMemory = sampleSize * objectCount;

        print('创建 $objectCount 个混合对象');
        print('估算内存使用: ${estimatedMemory / 1024 / 1024}MB');

        // 内存要求：总内存使用应小于100MB
        expect(estimatedMemory / 1024 / 1024, lessThan(100));

        // 清理对象，测试垃圾回收
        objects.clear();

        // 强制垃圾回收（在实际应用中不推荐）
        // 这里只是为了测试目的
        await Future.delayed(Duration(milliseconds: 100));

        print('对象已清理，等待垃圾回收');
      });
    });
  });
}

/// 生成随机字符串
String _generateRandomString(int length) {
  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  final random = Random();
  return String.fromCharCodes(
    Iterable.generate(length, (_) => chars.codeUnitAt(random.nextInt(chars.length))),
  );
}

/// 计算对象大小（简化估算）
int _calculateObjectSize(Map<String, dynamic> object) {
  var size = 0;

  void calculateSize(dynamic value) {
    if (value is String) {
      size += value.length * 2; // UTF-16编码
    } else if (value is int) {
      size += 8;
    } else if (value is double) {
      size += 8;
    } else if (value is bool) {
      size += 1;
    } else if (value is List) {
      size += 24; // 列表开销
      for (final item in value) {
        calculateSize(item);
      }
    } else if (value is Map) {
      size += 32; // Map开销
      for (final entry in value.entries) {
        calculateSize(entry.key);
        calculateSize(entry.value);
      }
    } else if (value != null) {
      size += 16; // 其他对象的基础开销
    }
  }

  calculateSize(object);
  return size;
}