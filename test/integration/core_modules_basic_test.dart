import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import '../../lib/core/di/injection.dart';
import '../../lib/core/config/feature_config.dart';
import '../../lib/core/config/environment_config.dart';

/// 核心模块基础集成测试
/// 
/// 验证第二阶段实现的核心业务模块基础功能
void main() {
  group('核心模块基础集成测试', () {
    setUpAll(() async {
      // 初始化测试环境
      await _setupTestEnvironment();
    });

    tearDownAll(() async {
      // 清理测试环境
      await _cleanupTestEnvironment();
    });

    group('依赖注入基础测试', () {
      test('应该正确初始化依赖注入容器', () async {
        // 验证GetIt容器已初始化
        expect(getIt, isNotNull);
        
        // 验证配置服务已注册
        expect(getIt.isRegistered<FeatureConfig>(), isTrue);
        expect(getIt.isRegistered<EnvironmentConfig>(), isTrue);
        
        // 获取配置实例
        final featureConfig = getIt.get<FeatureConfig>();
        final environmentConfig = getIt.get<EnvironmentConfig>();
        
        expect(featureConfig, isNotNull);
        expect(environmentConfig, isNotNull);
        
        print('✅ 依赖注入容器初始化成功');
        print('已注册服务数量: ${getIt.allReadySync().length}');
      });

      test('应该正确配置功能开关', () async {
        final featureConfig = getIt.get<FeatureConfig>();
        
        // 验证测试环境的功能配置
        expect(featureConfig.isFeatureEnabled('authentication'), isTrue);
        
        print('✅ 功能配置验证成功');
        print('已启用功能: ${featureConfig.enabledFeatures}');
      });

      test('应该正确配置环境变量', () async {
        final environmentConfig = getIt.get<EnvironmentConfig>();
        
        // 验证环境配置
        expect(environmentConfig.environment, equals('test'));
        expect(environmentConfig.apiBaseUrl, isNotEmpty);
        expect(environmentConfig.databaseName, isNotEmpty);
        
        print('✅ 环境配置验证成功');
        print('当前环境: ${environmentConfig.environment}');
        print('API地址: ${environmentConfig.apiBaseUrl}');
      });
    });

    group('核心服务基础测试', () {
      test('应该能够处理服务注册状态', () async {
        final featureConfig = getIt.get<FeatureConfig>();
        
        // 根据功能配置检查服务注册状态
        if (featureConfig.isFeatureEnabled('authentication')) {
          print('✅ 认证模块已启用');
        } else {
          print('ℹ️ 认证模块未启用');
        }
        
        if (featureConfig.isFeatureEnabled('database')) {
          print('✅ 数据库模块已启用');
        } else {
          print('ℹ️ 数据库模块未启用');
        }
        
        if (featureConfig.isFeatureEnabled('network')) {
          print('✅ 网络模块已启用');
        } else {
          print('ℹ️ 网络模块未启用');
        }
        
        // 测试总是通过，因为我们只是验证配置状态
        expect(true, isTrue);
      });
    });

    group('错误处理基础测试', () {
      test('应该能够处理基本错误情况', () async {
        // 测试基本的错误处理
        expect(() => throw Exception('测试异常'), throwsException);
        
        // 测试异步错误处理
        expect(
          () async => throw Exception('异步测试异常'),
          throwsException,
        );
        
        print('✅ 基础错误处理验证成功');
      });
    });

    group('模块集成状态测试', () {
      test('应该显示所有模块的集成状态', () async {
        final featureConfig = getIt.get<FeatureConfig>();
        
        print('\n=== 第二阶段模块集成状态 ===');
        
        // 认证授权模块
        final authEnabled = featureConfig.isFeatureEnabled('authentication');
        print('🔐 认证授权模块: ${authEnabled ? "✅ 已集成" : "❌ 未启用"}');
        
        // 网络通信层
        final networkEnabled = featureConfig.isFeatureEnabled('network');
        print('🌐 网络通信层: ${networkEnabled ? "✅ 已集成" : "❌ 未启用"}');
        
        // 数据持久化系统
        final databaseEnabled = featureConfig.isFeatureEnabled('database');
        print('💾 数据持久化系统: ${databaseEnabled ? "✅ 已集成" : "❌ 未启用"}');
        
        // 状态管理系统
        final stateEnabled = featureConfig.isFeatureEnabled('state_management');
        print('🔄 状态管理系统: ${stateEnabled ? "✅ 已集成" : "❌ 未启用"}');
        
        // 错误处理系统
        final errorEnabled = featureConfig.isFeatureEnabled('error_handling');
        print('⚠️ 错误处理系统: ${errorEnabled ? "✅ 已集成" : "❌ 未启用"}');
        
        print('==============================\n');
        
        // 验证至少有基础功能启用
        expect(authEnabled || networkEnabled || databaseEnabled, isTrue);
      });
    });
  });
}

/// 设置测试环境
Future<void> _setupTestEnvironment() async {
  try {
    // 初始化功能配置
    await FeatureConfig.initialize({
      'features': {
        'authentication': true,
        'network': false,
        'database': false,
        'state_management': false,
        'error_handling': false,
      },
    });

    // 初始化环境配置
    await EnvironmentConfig.initialize('test');

    // 配置依赖注入
    await configureDependencies(environment: 'test');
    
    print('🚀 测试环境初始化完成');
  } catch (e) {
    print('❌ 测试环境初始化失败: $e');
    rethrow;
  }
}

/// 清理测试环境
Future<void> _cleanupTestEnvironment() async {
  try {
    // 重置依赖注入容器
    await getIt.reset();
    
    // 清理配置
    try {
      FeatureConfig.instance.dispose();
    } catch (e) {
      // 忽略清理错误
    }
    
    try {
      EnvironmentConfig.instance.dispose();
    } catch (e) {
      // 忽略清理错误
    }
    
    print('🧹 测试环境清理完成');
  } catch (e) {
    print('⚠️ 测试环境清理警告: $e');
  }
}
