import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:dio/dio.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../../lib/core/di/injection.dart';
import '../../lib/core/config/feature_config.dart';
import '../../lib/core/database/database_manager.dart';
import '../../lib/core/cache/cache_manager.dart';
import '../../lib/core/sync/sync_manager.dart';
import '../../lib/core/state/global_state_manager.dart';
import '../../lib/core/state/module_state_manager.dart';
import '../../lib/core/errors/enhanced_error_handler.dart';
import '../../lib/core/network/dio_client.dart';
import '../../lib/features/auth/domain/repositories/auth_repository.dart';
import '../../lib/features/auth/domain/usecases/login_usecase.dart';
import '../../lib/features/auth/presentation/bloc/auth_bloc.dart';
import '../../lib/features/auth/presentation/bloc/auth_state.dart';

/// 核心模块集成测试
/// 
/// 验证第二阶段实现的核心业务模块是否正确集成
void main() {
  group('核心模块集成测试', () {
    setUpAll(() async {
      // 初始化测试环境
      await _setupTestEnvironment();
    });

    tearDownAll(() async {
      // 清理测试环境
      await _cleanupTestEnvironment();
    });

    group('依赖注入集成测试', () {
      test('应该正确注册所有核心依赖', () async {
        // 验证配置服务
        expect(getIt.isRegistered<FeatureConfig>(), isTrue);
        
        // 验证基础服务
        expect(getIt.isRegistered<FlutterSecureStorage>(), isTrue);
        expect(getIt.isRegistered<HiveInterface>(), isTrue);
        expect(getIt.isRegistered<Dio>(), isTrue);

        // 验证核心服务（如果启用）
        if (getIt.get<FeatureConfig>().isFeatureEnabled('database')) {
          expect(getIt.isRegistered<DatabaseManager>(), isTrue);
        }

        if (getIt.get<FeatureConfig>().isFeatureEnabled('cache')) {
          expect(getIt.isRegistered<CacheManager>(), isTrue);
        }

        if (getIt.get<FeatureConfig>().isFeatureEnabled('sync')) {
          expect(getIt.isRegistered<SyncManager>(), isTrue);
        }

        if (getIt.get<FeatureConfig>().isFeatureEnabled('state_management')) {
          expect(getIt.isRegistered<GlobalStateManager>(), isTrue);
          expect(getIt.isRegistered<ModuleStateManager>(), isTrue);
        }

        if (getIt.get<FeatureConfig>().isFeatureEnabled('error_handling')) {
          expect(getIt.isRegistered<EnhancedErrorHandler>(), isTrue);
        }

        if (getIt.get<FeatureConfig>().isFeatureEnabled('network')) {
          expect(getIt.isRegistered<DioClient>(), isTrue);
        }
      });

      test('应该正确注册认证模块依赖', () async {
        final featureConfig = getIt.get<FeatureConfig>();

        if (featureConfig.isFeatureEnabled('authentication')) {
          expect(getIt.isRegistered<IAuthRepository>(), isTrue);
          expect(getIt.isRegistered<LoginUseCase>(), isTrue);
          expect(getIt.isRegistered<AuthBloc>(), isTrue);
        }
      });
    });

    group('数据库管理器集成测试', () {
      test('应该正确初始化数据库', () async {
        final featureConfig = getIt.get<FeatureConfig>();

        if (featureConfig.isFeatureEnabled('database')) {
          final databaseManager = getIt.get<DatabaseManager>();
          
          // 验证数据库已初始化
          expect(databaseManager.defaultBox, isNotNull);
          expect(databaseManager.cacheBox, isNotNull);
          expect(databaseManager.userBox, isNotNull);
          expect(databaseManager.settingsBox, isNotNull);
          
          // 测试数据库操作
          await databaseManager.defaultBox.put('test_key', 'test_value');
          expect(databaseManager.defaultBox.get('test_key'), equals('test_value'));
          
          // 清理测试数据
          await databaseManager.defaultBox.delete('test_key');
        }
      });
    });

    group('缓存管理器集成测试', () {
      test('应该正确管理缓存', () async {
        final featureConfig = get<FeatureConfig>();
        
        if (featureConfig.isFeatureEnabled('cache')) {
          final cacheManager = get<CacheManager>();
          
          // 测试缓存存储
          await cacheManager.put('test_cache_key', 'test_cache_value');
          
          // 测试缓存读取
          final cachedValue = await cacheManager.get<String>('test_cache_key');
          expect(cachedValue, equals('test_cache_value'));
          
          // 测试缓存检查
          final exists = await cacheManager.contains('test_cache_key');
          expect(exists, isTrue);
          
          // 测试缓存删除
          await cacheManager.remove('test_cache_key');
          final removedValue = await cacheManager.get<String>('test_cache_key');
          expect(removedValue, isNull);
        }
      });

      test('应该正确处理缓存过期', () async {
        final featureConfig = get<FeatureConfig>();
        
        if (featureConfig.isFeatureEnabled('cache')) {
          final cacheManager = get<CacheManager>();
          
          // 设置短期缓存
          await cacheManager.put(
            'short_cache_key',
            'short_cache_value',
            ttl: const Duration(milliseconds: 100),
          );
          
          // 立即检查缓存存在
          expect(await cacheManager.contains('short_cache_key'), isTrue);
          
          // 等待缓存过期
          await Future.delayed(const Duration(milliseconds: 150));
          
          // 检查缓存已过期
          expect(await cacheManager.contains('short_cache_key'), isFalse);
        }
      });
    });

    group('全局状态管理器集成测试', () {
      test('应该正确管理全局状态', () async {
        final featureConfig = get<FeatureConfig>();
        
        if (featureConfig.isFeatureEnabled('state_management')) {
          final globalStateManager = get<GlobalStateManager>();
          
          // 测试状态设置
          await globalStateManager.setState('test_state', 'test_value');
          
          // 测试状态读取
          final stateValue = globalStateManager.getState<String>('test_state');
          expect(stateValue, equals('test_value'));
          
          // 测试状态存在检查
          expect(globalStateManager.hasState('test_state'), isTrue);
          
          // 测试状态删除
          await globalStateManager.removeState('test_state');
          expect(globalStateManager.hasState('test_state'), isFalse);
        }
      });
    });

    group('错误处理器集成测试', () {
      test('应该正确处理和记录错误', () async {
        final featureConfig = get<FeatureConfig>();
        
        if (featureConfig.isFeatureEnabled('error_handling')) {
          final errorHandler = get<EnhancedErrorHandler>();
          
          // 测试错误处理
          final testError = Exception('Test error');
          await errorHandler.handleAndLogError(
            testError,
            context: 'Integration Test',
            metadata: {'test': true},
          );
          
          // 验证错误日志
          final errorLogs = await errorHandler.getErrorLogs(limit: 1);
          expect(errorLogs.isNotEmpty, isTrue);
          expect(errorLogs.first.context, equals('Integration Test'));
          expect(errorLogs.first.metadata['test'], isTrue);
          
          // 清理错误日志
          await errorHandler.clearErrorLogs();
        }
      });
    });

    group('网络客户端集成测试', () {
      test('应该正确配置网络客户端', () async {
        final featureConfig = get<FeatureConfig>();
        
        if (featureConfig.isFeatureEnabled('network')) {
          final dioClient = get<DioClient>();
          
          // 验证客户端配置
          expect(dioClient.dio, isNotNull);
          expect(dioClient.uploadDio, isNotNull);
          expect(dioClient.downloadDio, isNotNull);
          
          // 验证基础配置
          expect(dioClient.dio.options.connectTimeout, isNotNull);
          expect(dioClient.dio.options.receiveTimeout, isNotNull);
          expect(dioClient.dio.options.sendTimeout, isNotNull);
        }
      });
    });

    group('认证模块集成测试', () {
      test('应该正确处理认证流程', () async {
        final featureConfig = get<FeatureConfig>();
        
        if (featureConfig.isFeatureEnabled('authentication')) {
          final authBloc = get<AuthBloc>();
          
          // 验证初始状态
          expect(authBloc.state, isA<AuthInitial>());
          
          // 测试认证状态检查
          authBloc.add(const AuthCheckRequested());
          
          // 等待状态变化
          await Future.delayed(const Duration(milliseconds: 100));
          
          // 验证状态变化（应该是未认证状态）
          expect(authBloc.state, isA<AuthUnauthenticated>());
        }
      });
    });

    group('模块间协作测试', () {
      test('认证模块应该与状态管理器协作', () async {
        final featureConfig = get<FeatureConfig>();
        
        if (featureConfig.isFeatureEnabled('authentication') &&
            featureConfig.isFeatureEnabled('state_management')) {
          final authBloc = get<AuthBloc>();
          final globalStateManager = get<GlobalStateManager>();
          
          // 设置认证相关的全局状态
          await globalStateManager.setState('auth_initialized', true);
          
          // 验证状态设置
          expect(globalStateManager.getState<bool>('auth_initialized'), isTrue);
          
          // 验证认证模块可以访问全局状态
          expect(authBloc.state, isNotNull);
        }
      });

      test('错误处理器应该与数据库管理器协作', () async {
        final featureConfig = get<FeatureConfig>();
        
        if (featureConfig.isFeatureEnabled('error_handling') &&
            featureConfig.isFeatureEnabled('database')) {
          final errorHandler = get<EnhancedErrorHandler>();
          final databaseManager = get<DatabaseManager>();
          
          // 测试错误记录到数据库
          await errorHandler.handleAndLogError(
            Exception('Database integration test'),
            context: 'Module Integration',
          );
          
          // 验证错误已记录到数据库
          final errorLogs = await errorHandler.getErrorLogs(limit: 1);
          expect(errorLogs.isNotEmpty, isTrue);
          
          // 验证数据库中有错误记录
          final errorLogData = databaseManager.defaultBox.get('error_logs');
          expect(errorLogData, isNotNull);
        }
      });
    });
  });
}

/// 设置测试环境
Future<void> _setupTestEnvironment() async {
  // 初始化依赖注入
  await configureDependencies(environment: 'test');
}

/// 清理测试环境
Future<void> _cleanupTestEnvironment() async {
  // 重置依赖注入容器
  await resetDependencies();
}
