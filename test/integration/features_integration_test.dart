import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:flutter_template/features/theming/domain/entities/app_theme.dart';
import 'package:flutter_template/features/theming/domain/repositories/theme_repository.dart';
import 'package:flutter_template/features/internationalization/domain/entities/locale_info.dart';
import 'package:flutter_template/features/internationalization/domain/repositories/localization_repository.dart';
import 'package:flutter_template/features/performance/domain/entities/performance_metric.dart';
import 'package:flutter_template/features/performance/domain/repositories/performance_repository.dart';
import 'package:flutter_template/core/navigation/entities/app_route.dart';
import 'package:flutter_template/core/navigation/services/navigation_service.dart';

// Mock classes
class MockThemeRepository extends Mock implements IThemeRepository {}
class MockLocalizationRepository extends Mock implements ILocalizationRepository {}
class MockPerformanceRepository extends Mock implements IPerformanceRepository {}
class MockNavigationService extends Mock implements INavigationService {}

void main() {
  group('功能模块集成测试', () {
    late MockThemeRepository mockThemeRepository;
    late MockLocalizationRepository mockLocalizationRepository;
    late MockPerformanceRepository mockPerformanceRepository;
    late MockNavigationService mockNavigationService;

    setUp(() {
      mockThemeRepository = MockThemeRepository();
      mockLocalizationRepository = MockLocalizationRepository();
      mockPerformanceRepository = MockPerformanceRepository();
      mockNavigationService = MockNavigationService();
    });

    group('主题系统集成测试', () {
      testWidgets('主题实体功能正常', (WidgetTester tester) async {
        // 测试默认主题创建
        final lightTheme = AppTheme.defaultLight();
        expect(lightTheme.id, 'default_light');
        expect(lightTheme.name, '默认浅色');
        expect(lightTheme.isDark, false);
        expect(lightTheme.isBuiltIn, true);

        final darkTheme = AppTheme.defaultDark();
        expect(darkTheme.id, 'default_dark');
        expect(darkTheme.name, '默认深色');
        expect(darkTheme.isDark, true);
        expect(darkTheme.isBuiltIn, true);

        // 测试从种子颜色创建主题
        final customTheme = AppTheme.fromSeed(
          id: 'custom_blue',
          name: '自定义蓝色',
          seedColor: Colors.blue,
          isDark: false,
        );
        expect(customTheme.id, 'custom_blue');
        expect(customTheme.name, '自定义蓝色');
        expect(customTheme.isDark, false);
        expect(customTheme.isBuiltIn, false);

        // 测试主题转换为ThemeData
        final themeData = lightTheme.toThemeData();
        expect(themeData.useMaterial3, true);
        expect(themeData.colorScheme.brightness, Brightness.light);

        // 测试主题JSON序列化
        final json = lightTheme.toJson();
        final fromJson = AppTheme.fromJson(json);
        expect(fromJson.id, lightTheme.id);
        expect(fromJson.name, lightTheme.name);
        expect(fromJson.isDark, lightTheme.isDark);
      });

      testWidgets('主题预设功能正常', (WidgetTester tester) async {
        final builtInThemes = ThemePresets.getBuiltInThemes();
        expect(builtInThemes.length, greaterThan(2));
        
        // 验证包含默认主题
        expect(builtInThemes.any((theme) => theme.id == 'default_light'), true);
        expect(builtInThemes.any((theme) => theme.id == 'default_dark'), true);
        
        // 验证所有主题都是内置主题
        expect(builtInThemes.every((theme) => theme.isBuiltIn), true);
      });

      test('主题仓库模拟测试', () async {
        // 设置模拟行为
        when(mockThemeRepository.getThemeMode())
            .thenAnswer((_) async => ThemeMode.light);
        when(mockThemeRepository.getCurrentThemeId())
            .thenAnswer((_) async => 'default_light');
        when(mockThemeRepository.getAvailableThemes())
            .thenAnswer((_) async => ThemePresets.getBuiltInThemes());

        // 测试获取主题模式
        final themeMode = await mockThemeRepository.getThemeMode();
        expect(themeMode, ThemeMode.light);

        // 测试获取当前主题ID
        final themeId = await mockThemeRepository.getCurrentThemeId();
        expect(themeId, 'default_light');

        // 测试获取可用主题
        final themes = await mockThemeRepository.getAvailableThemes();
        expect(themes.isNotEmpty, true);

        // 验证方法调用
        verify(mockThemeRepository.getThemeMode()).called(1);
        verify(mockThemeRepository.getCurrentThemeId()).called(1);
        verify(mockThemeRepository.getAvailableThemes()).called(1);
      });
    });

    group('国际化系统集成测试', () {
      testWidgets('语言环境实体功能正常', (WidgetTester tester) async {
        // 测试预设语言环境
        final zhCN = LocaleInfo.zhCN();
        expect(zhCN.locale.languageCode, 'zh');
        expect(zhCN.locale.countryCode, 'CN');
        expect(zhCN.name, 'Chinese (Simplified)');
        expect(zhCN.nativeName, '简体中文');
        expect(zhCN.flag, '🇨🇳');
        expect(zhCN.isRTL, false);

        final enUS = LocaleInfo.enUS();
        expect(enUS.locale.languageCode, 'en');
        expect(enUS.locale.countryCode, 'US');
        expect(enUS.name, 'English (US)');
        expect(enUS.nativeName, 'English');
        expect(enUS.flag, '🇺🇸');
        expect(enUS.isRTL, false);

        final arSA = LocaleInfo.arSA();
        expect(arSA.isRTL, true);
        expect(arSA.textDirection, TextDirection.rtl);

        // 测试语言环境代码
        expect(zhCN.localeCode, 'zh_CN');
        expect(enUS.localeCode, 'en_US');

        // 测试完成度
        expect(zhCN.isFullyTranslated, true);
        expect(zhCN.completenessPercentage, 100);

        // 测试JSON序列化
        final json = zhCN.toJson();
        final fromJson = LocaleInfo.fromJson(json);
        expect(fromJson.locale, zhCN.locale);
        expect(fromJson.name, zhCN.name);
        expect(fromJson.nativeName, zhCN.nativeName);
      });

      testWidgets('语言环境预设功能正常', (WidgetTester tester) async {
        final supportedLocales = LocalePresets.getSupportedLocales();
        expect(supportedLocales.length, greaterThan(2));
        
        // 验证包含主要语言
        expect(supportedLocales.any((locale) => locale.locale.languageCode == 'en'), true);
        expect(supportedLocales.any((locale) => locale.locale.languageCode == 'zh'), true);
        
        // 测试默认语言环境
        final defaultLocale = LocalePresets.getDefaultLocale();
        expect(defaultLocale.locale.languageCode, 'en');
        
        // 测试RTL语言检查
        expect(LocalePresets.isRTLLanguage('ar'), true);
        expect(LocalePresets.isRTLLanguage('en'), false);
        expect(LocalePresets.isRTLLanguage('zh'), false);
      });

      test('本地化仓库模拟测试', () async {
        // 设置模拟行为
        when(mockLocalizationRepository.getCurrentLocale())
            .thenAnswer((_) async => const Locale('zh', 'CN'));
        when(mockLocalizationRepository.getSupportedLocales())
            .thenAnswer((_) async => LocalePresets.getSupportedLocales());
        when(mockLocalizationRepository.isLocaleSupported(any))
            .thenAnswer((_) async => true);

        // 测试获取当前语言环境
        final currentLocale = await mockLocalizationRepository.getCurrentLocale();
        expect(currentLocale.languageCode, 'zh');
        expect(currentLocale.countryCode, 'CN');

        // 测试获取支持的语言环境
        final supportedLocales = await mockLocalizationRepository.getSupportedLocales();
        expect(supportedLocales.isNotEmpty, true);

        // 测试语言环境支持检查
        final isSupported = await mockLocalizationRepository.isLocaleSupported(
          const Locale('en', 'US')
        );
        expect(isSupported, true);

        // 验证方法调用
        verify(mockLocalizationRepository.getCurrentLocale()).called(1);
        verify(mockLocalizationRepository.getSupportedLocales()).called(1);
        verify(mockLocalizationRepository.isLocaleSupported(any)).called(1);
      });
    });

    group('性能监控系统集成测试', () {
      testWidgets('性能指标实体功能正常', (WidgetTester tester) async {
        // 测试应用启动指标
        final startupMetric = PerformanceMetric.appStartup(
          id: 'test_startup',
          startupTime: 1200.0,
          tags: {'platform': 'test'},
        );
        expect(startupMetric.type, PerformanceMetricType.appStartup);
        expect(startupMetric.value, 1200.0);
        expect(startupMetric.unit, 'ms');
        expect(startupMetric.severity, PerformanceSeverity.normal);
        expect(startupMetric.formattedValue, '1200 ms');

        // 测试页面加载指标
        final pageLoadMetric = PerformanceMetric.pageLoad(
          id: 'test_page_load',
          pageName: 'home',
          loadTime: 2500.0,
        );
        expect(pageLoadMetric.type, PerformanceMetricType.pageLoad);
        expect(pageLoadMetric.severity, PerformanceSeverity.critical);
        expect(pageLoadMetric.isCritical, true);

        // 测试网络请求指标
        final networkMetric = PerformanceMetric.networkRequest(
          id: 'test_network',
          url: 'https://api.example.com/data',
          responseTime: 800.0,
          statusCode: 200,
        );
        expect(networkMetric.type, PerformanceMetricType.networkRequest);
        expect(networkMetric.severity, PerformanceSeverity.normal);
        expect(networkMetric.tags?['status_code'], '200');

        // 测试内存使用指标
        final memoryMetric = PerformanceMetric.memoryUsage(
          id: 'test_memory',
          memoryUsage: 150.0,
        );
        expect(memoryMetric.type, PerformanceMetricType.memoryUsage);
        expect(memoryMetric.unit, 'MB');
        expect(memoryMetric.severity, PerformanceSeverity.normal);

        // 测试帧率指标
        final frameRateMetric = PerformanceMetric.frameRate(
          id: 'test_fps',
          fps: 25.0,
        );
        expect(frameRateMetric.type, PerformanceMetricType.frameRate);
        expect(frameRateMetric.unit, 'fps');
        expect(frameRateMetric.severity, PerformanceSeverity.critical);

        // 测试JSON序列化
        final json = startupMetric.toJson();
        final fromJson = PerformanceMetric.fromJson(json);
        expect(fromJson.id, startupMetric.id);
        expect(fromJson.type, startupMetric.type);
        expect(fromJson.value, startupMetric.value);
        expect(fromJson.severity, startupMetric.severity);
      });

      test('性能仓库模拟测试', () async {
        final testMetric = PerformanceMetric.appStartup(
          id: 'test_metric',
          startupTime: 1000.0,
        );

        // 设置模拟行为
        when(mockPerformanceRepository.recordMetric(any))
            .thenAnswer((_) async {});
        when(mockPerformanceRepository.getMetrics())
            .thenAnswer((_) async => [testMetric]);
        when(mockPerformanceRepository.getMetricStats())
            .thenAnswer((_) async => {
              'count': 1,
              'average': 1000.0,
              'min': 1000.0,
              'max': 1000.0,
            });

        // 测试记录指标
        await mockPerformanceRepository.recordMetric(testMetric);

        // 测试获取指标
        final metrics = await mockPerformanceRepository.getMetrics();
        expect(metrics.length, 1);
        expect(metrics.first.id, 'test_metric');

        // 测试获取统计信息
        final stats = await mockPerformanceRepository.getMetricStats();
        expect(stats['count'], 1);
        expect(stats['average'], 1000.0);

        // 验证方法调用
        verify(mockPerformanceRepository.recordMetric(any)).called(1);
        verify(mockPerformanceRepository.getMetrics()).called(1);
        verify(mockPerformanceRepository.getMetricStats()).called(1);
      });
    });

    group('导航系统集成测试', () {
      testWidgets('路由实体功能正常', (WidgetTester tester) async {
        // 测试基础路由
        final homeRoute = AppRoute(
          name: 'home',
          path: '/',
          builder: (context, params) => const Text('Home'),
          permission: RoutePermission.public,
        );
        expect(homeRoute.name, 'home');
        expect(homeRoute.path, '/');
        expect(homeRoute.fullPath, '/');
        expect(homeRoute.isRoot, true);
        expect(homeRoute.hasChildren, false);

        // 测试嵌套路由
        final settingsRoute = AppRoute(
          name: 'settings',
          path: '/settings',
          builder: (context, params) => const Text('Settings'),
          children: [
            AppRoute(
              name: 'theme_settings',
              path: '/theme',
              builder: (context, params) => const Text('Theme Settings'),
            ),
          ],
        );
        expect(settingsRoute.hasChildren, true);
        expect(settingsRoute.children!.length, 1);

        final themeRoute = settingsRoute.children!.first.copyWith(
          parent: settingsRoute.fullPath,
        );
        expect(themeRoute.fullPath, '/settings/theme');

        // 测试路径匹配
        expect(homeRoute.matchesPath('/'), true);
        expect(homeRoute.matchesPath('/home'), false);

        // 测试参数路由
        final userRoute = AppRoute(
          name: 'user_profile',
          path: '/user/:id',
          builder: (context, params) => Text('User ${params['id']}'),
        );
        expect(userRoute.matchesPath('/user/123'), true);
        expect(userRoute.matchesPath('/user'), false);

        final params = userRoute.extractParams('/user/123');
        expect(params['id'], '123');
      });

      test('导航服务模拟测试', () async {
        final testRoute = AppRoute(
          name: 'test_route',
          path: '/test',
          builder: (context, params) => const Text('Test'),
        );

        // 设置模拟行为
        when(mockNavigationService.findRoute('test_route'))
            .thenReturn(testRoute);
        when(mockNavigationService.hasRoute('test_route'))
            .thenReturn(true);
        when(mockNavigationService.getAllRoutes())
            .thenReturn([testRoute]);
        when(mockNavigationService.navigateTo<void>('test_route'))
            .thenAnswer((_) async {});

        // 测试查找路由
        final foundRoute = mockNavigationService.findRoute('test_route');
        expect(foundRoute?.name, 'test_route');

        // 测试路由存在检查
        final hasRoute = mockNavigationService.hasRoute('test_route');
        expect(hasRoute, true);

        // 测试获取所有路由
        final allRoutes = mockNavigationService.getAllRoutes();
        expect(allRoutes.length, 1);

        // 测试导航
        await mockNavigationService.navigateTo('test_route');

        // 验证方法调用
        verify(mockNavigationService.findRoute('test_route')).called(1);
        verify(mockNavigationService.hasRoute('test_route')).called(1);
        verify(mockNavigationService.getAllRoutes()).called(1);
        verify(mockNavigationService.navigateTo<void>('test_route')).called(1);
      });
    });
  });

  group('模块间集成测试', () {
    test('主题与国际化集成', () async {
      // 模拟主题和语言环境同时变化
      when(mockThemeRepository.getCurrentThemeId())
          .thenAnswer((_) async => 'default_dark');
      when(mockLocalizationRepository.getCurrentLocale())
          .thenAnswer((_) async => const Locale('zh', 'CN'));

      final themeId = await mockThemeRepository.getCurrentThemeId();
      final locale = await mockLocalizationRepository.getCurrentLocale();

      expect(themeId, 'default_dark');
      expect(locale.languageCode, 'zh');

      // 验证两个系统可以独立工作
      verify(mockThemeRepository.getCurrentThemeId()).called(1);
      verify(mockLocalizationRepository.getCurrentLocale()).called(1);
    });

    test('性能监控与导航集成', () async {
      final navigationMetric = PerformanceMetric.pageLoad(
        id: 'nav_test',
        pageName: 'home',
        loadTime: 500.0,
      );

      // 模拟导航时记录性能指标
      when(mockNavigationService.navigateTo<void>('home'))
          .thenAnswer((_) async {});
      when(mockPerformanceRepository.recordMetric(any))
          .thenAnswer((_) async {});

      await mockNavigationService.navigateTo('home');
      await mockPerformanceRepository.recordMetric(navigationMetric);

      verify(mockNavigationService.navigateTo<void>('home')).called(1);
      verify(mockPerformanceRepository.recordMetric(any)).called(1);
    });
  });
}
