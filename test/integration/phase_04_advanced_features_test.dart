import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

// 导入第四阶段的功能模块
import 'package:flutter_template/features/analytics/domain/entities/tracking_event.dart';
import 'package:flutter_template/features/analytics/domain/entities/user_behavior.dart';
import 'package:flutter_template/features/analytics/domain/services/event_tracking_service.dart';
import 'package:flutter_template/features/analytics/domain/services/user_behavior_service.dart';

import 'package:flutter_template/features/notifications/domain/entities/push_notification.dart';
import 'package:flutter_template/features/notifications/domain/entities/notification_settings.dart';
import 'package:flutter_template/features/notifications/domain/services/push_notification_service.dart';

import 'package:flutter_template/features/offline/domain/entities/offline_data.dart';
import 'package:flutter_template/features/offline/domain/entities/offline_config.dart';
import 'package:flutter_template/features/offline/domain/services/offline_sync_service.dart';

import 'package:flutter_template/features/security/domain/entities/encryption_key.dart';
import 'package:flutter_template/features/security/domain/entities/security_config.dart';

import 'package:flutter_template/features/enterprise/domain/entities/system_monitor.dart';
import 'package:flutter_template/features/enterprise/domain/entities/config_management.dart';

import 'package:flutter_template/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('第四阶段：高级功能模块集成测试', () {
    setUpAll(() async {
      // 初始化应用
      await app.main();
      await tester.pumpAndSettle();
    });

    tearDownAll(() async {
      // 清理资源
      await GetIt.instance.reset();
    });

    group('分析统计系统测试', () {
      testWidgets('事件追踪功能测试', (WidgetTester tester) async {
        // 测试事件追踪实体创建
        final pageViewEvent = TrackingEvent.pageView(
          id: 'test_page_view_1',
          pagePath: '/home',
          pageTitle: '首页',
          userId: 'test_user_1',
          sessionId: 'test_session_1',
        );

        expect(pageViewEvent.type, EventType.pageView);
        expect(pageViewEvent.name, 'page_view');
        expect(pageViewEvent.pagePath, '/home');
        expect(pageViewEvent.userId, 'test_user_1');

        // 测试用户操作事件
        final userActionEvent = TrackingEvent.userAction(
          id: 'test_action_1',
          action: 'button_click',
          target: 'login_button',
          userId: 'test_user_1',
          sessionId: 'test_session_1',
        );

        expect(userActionEvent.type, EventType.userAction);
        expect(userActionEvent.properties['action'], 'button_click');
        expect(userActionEvent.properties['target'], 'login_button');

        // 测试业务事件
        final businessEvent = TrackingEvent.businessEvent(
          id: 'test_business_1',
          eventName: 'purchase_completed',
          priority: EventPriority.high,
          properties: {
            'amount': 99.99,
            'currency': 'USD',
            'product_id': 'prod_123',
          },
          userId: 'test_user_1',
          sessionId: 'test_session_1',
        );

        expect(businessEvent.type, EventType.businessEvent);
        expect(businessEvent.priority, EventPriority.high);
        expect(businessEvent.isHighPriority, true);
      });

      testWidgets('用户行为分析功能测试', (WidgetTester tester) async {
        // 测试页面访问行为
        final pageVisitBehavior = UserBehavior.pageVisit(
          id: 'test_behavior_1',
          userId: 'test_user_1',
          sessionId: 'test_session_1',
          pagePath: '/dashboard',
          pageTitle: '仪表板',
        );

        expect(pageVisitBehavior.type, BehaviorType.pageVisit);
        expect(pageVisitBehavior.name, 'page_visit');
        expect(pageVisitBehavior.status, BehaviorStatus.started);

        // 测试功能使用行为
        final featureUsageBehavior = UserBehavior.featureUsage(
          id: 'test_behavior_2',
          userId: 'test_user_1',
          sessionId: 'test_session_1',
          featureName: 'data_export',
          target: 'export_button',
        );

        expect(featureUsageBehavior.type, BehaviorType.featureUsage);
        expect(featureUsageBehavior.properties['feature_name'], 'data_export');

        // 测试行为完成
        final completedBehavior = pageVisitBehavior.complete();
        expect(completedBehavior.status, BehaviorStatus.completed);
        expect(completedBehavior.isCompleted, true);
        expect(completedBehavior.actualDuration, isNotNull);
      });
    });

    group('推送通知系统测试', () {
      testWidgets('推送通知实体测试', (WidgetTester tester) async {
        // 测试系统通知
        final systemNotification = PushNotification.system(
          id: 'test_notification_1',
          title: '系统维护通知',
          body: '系统将于今晚进行维护，预计持续2小时',
          userId: 'test_user_1',
        );

        expect(systemNotification.type, NotificationType.system);
        expect(systemNotification.priority, NotificationPriority.normal);
        expect(systemNotification.status, NotificationStatus.pending);

        // 测试营销通知
        final marketingNotification = PushNotification.marketing(
          id: 'test_notification_2',
          title: '限时优惠',
          body: '新用户专享8折优惠，仅限今日！',
          image: 'https://example.com/promo.jpg',
          deepLink: 'app://promo/new-user',
          userGroups: ['new_users'],
          tags: ['promotion', 'discount'],
        );

        expect(marketingNotification.type, NotificationType.marketing);
        expect(marketingNotification.priority, NotificationPriority.low);
        expect(marketingNotification.userGroups, contains('new_users'));

        // 测试紧急通知
        final emergencyNotification = PushNotification.emergency(
          id: 'test_notification_3',
          title: '安全警告',
          body: '检测到异常登录，请立即检查您的账户',
          userId: 'test_user_1',
        );

        expect(emergencyNotification.type, NotificationType.emergency);
        expect(emergencyNotification.priority, NotificationPriority.urgent);
        expect(emergencyNotification.isUrgent, true);
        expect(emergencyNotification.maxRetries, 5);

        // 测试通知状态变更
        final sentNotification = systemNotification.markAsSent();
        expect(sentNotification.status, NotificationStatus.sent);
        expect(sentNotification.sentAt, isNotNull);

        final deliveredNotification = sentNotification.markAsDelivered();
        expect(deliveredNotification.status, NotificationStatus.delivered);

        final readNotification = deliveredNotification.markAsRead();
        expect(readNotification.status, NotificationStatus.read);
      });

      testWidgets('通知设置测试', (WidgetTester tester) async {
        // 测试默认设置
        final defaultSettings = NotificationSettings.defaultSettings('test_user_1');
        expect(defaultSettings.pushNotificationsEnabled, true);
        expect(defaultSettings.inAppNotificationsEnabled, true);
        expect(defaultSettings.typeSettings[NotificationType.system], true);
        expect(defaultSettings.typeSettings[NotificationType.marketing], false);

        // 测试通知权限检查
        final systemNotification = PushNotification.system(
          id: 'test_check_1',
          title: '测试通知',
          body: '这是一个测试通知',
          userId: 'test_user_1',
        );

        expect(defaultSettings.shouldReceiveNotification(systemNotification), true);

        final marketingNotification = PushNotification.marketing(
          id: 'test_check_2',
          title: '营销通知',
          body: '这是一个营销通知',
        );

        expect(defaultSettings.shouldReceiveNotification(marketingNotification), false);

        // 测试免打扰模式
        final dndSettings = defaultSettings.copyWith(
          doNotDisturbEnabled: true,
          doNotDisturbStartTime: '22:00',
          doNotDisturbEndTime: '08:00',
        );

        // 紧急通知应该能突破免打扰
        final emergencyNotification = PushNotification.emergency(
          id: 'test_check_3',
          title: '紧急通知',
          body: '这是一个紧急通知',
          userId: 'test_user_1',
        );

        expect(dndSettings.shouldReceiveNotification(emergencyNotification), true);
      });
    });

    group('离线支持系统测试', () {
      testWidgets('离线数据管理测试', (WidgetTester tester) async {
        // 测试创建离线数据
        final createData = OfflineData.create(
          id: 'test_data_1',
          dataType: 'user_profile',
          data: {
            'name': 'Test User',
            'email': '<EMAIL>',
            'avatar': 'https://example.com/avatar.jpg',
          },
          userId: 'test_user_1',
          deviceId: 'test_device_1',
        );

        expect(createData.operationType, OfflineOperationType.create);
        expect(createData.status, OfflineDataStatus.pending);
        expect(createData.isPending, true);
        expect(createData.canRetry, true);

        // 测试更新离线数据
        final updateData = OfflineData.update(
          id: 'test_data_2',
          dataType: 'user_profile',
          data: {
            'name': 'Updated User',
            'email': '<EMAIL>',
          },
          version: 2,
          userId: 'test_user_1',
        );

        expect(updateData.operationType, OfflineOperationType.update);
        expect(updateData.version, 2);

        // 测试删除离线数据
        final deleteData = OfflineData.delete(
          id: 'test_data_3',
          dataType: 'user_profile',
          userId: 'test_user_1',
        );

        expect(deleteData.operationType, OfflineOperationType.delete);
        expect(deleteData.data['deleted'], true);

        // 测试状态变更
        final syncingData = createData.markAsSyncing();
        expect(syncingData.status, OfflineDataStatus.syncing);
        expect(syncingData.isSyncing, true);

        final syncedData = syncingData.markAsSynced(serverVersion: 1);
        expect(syncedData.status, OfflineDataStatus.synced);
        expect(syncedData.isSynced, true);
        expect(syncedData.serverVersion, 1);

        final failedData = syncingData.markAsFailed(
          errorMessage: '网络连接失败',
          incrementRetry: true,
        );
        expect(failedData.status, OfflineDataStatus.failed);
        expect(failedData.isFailed, true);
        expect(failedData.retryCount, 1);
        expect(failedData.errorMessage, '网络连接失败');
      });

      testWidgets('离线配置测试', (WidgetTester tester) async {
        // 测试默认配置
        final defaultConfig = OfflineConfig.defaultConfig();
        expect(defaultConfig.offlineEnabled, true);
        expect(defaultConfig.syncStrategy, SyncStrategy.intelligent);
        expect(defaultConfig.conflictResolutionStrategy, ConflictResolutionStrategy.lastWriteWins);

        // 测试高性能配置
        final highPerfConfig = OfflineConfig.highPerformance();
        expect(highPerfConfig.syncStrategy, SyncStrategy.batch);
        expect(highPerfConfig.batchSyncSize, 100);
        expect(highPerfConfig.maxOfflineDataSize, 500);
        expect(highPerfConfig.encryptData, true);

        // 测试低功耗配置
        final lowPowerConfig = OfflineConfig.lowPower();
        expect(lowPowerConfig.syncStrategy, SyncStrategy.manual);
        expect(lowPowerConfig.enableBackgroundSync, false);
        expect(lowPowerConfig.maxOfflineDataSize, 50);

        // 测试数据类型配置
        expect(defaultConfig.isDataTypeOfflineEnabled('user_data'), true);
        expect(defaultConfig.getDataTypeSyncStrategy('user_data'), SyncStrategy.intelligent);
      });
    });

    group('安全增强系统测试', () {
      testWidgets('加密密钥管理测试', (WidgetTester tester) async {
        // 测试AES密钥
        final aesKey = EncryptionKey.aes256(
          keyId: 'test_aes_1',
          alias: 'user_data_key',
          keyData: 'encrypted_key_data_here',
          purposes: ['encryption', 'decryption'],
          createdBy: 'system',
        );

        expect(aesKey.keyType, KeyType.symmetric);
        expect(aesKey.algorithm, EncryptionAlgorithm.aes256gcm);
        expect(aesKey.keyLength, 256);
        expect(aesKey.isSymmetric, true);
        expect(aesKey.canEncrypt, true);
        expect(aesKey.canDecrypt, true);

        // 测试RSA密钥
        final rsaKey = EncryptionKey.rsa(
          keyId: 'test_rsa_1',
          alias: 'api_signing_key',
          privateKeyData: 'private_key_data',
          publicKeyData: 'public_key_data',
          keyLength: 2048,
          purposes: ['signing', 'verification'],
          createdBy: 'admin',
        );

        expect(rsaKey.keyType, KeyType.asymmetricPrivate);
        expect(rsaKey.algorithm, EncryptionAlgorithm.rsa2048);
        expect(rsaKey.isAsymmetric, true);
        expect(rsaKey.canSign, true);

        // 测试ECDSA密钥
        final ecdsaKey = EncryptionKey.ecdsa(
          keyId: 'test_ecdsa_1',
          alias: 'transaction_signing_key',
          privateKeyData: 'ecdsa_private_key',
          publicKeyData: 'ecdsa_public_key',
          curve: 'P-256',
          createdBy: 'security_admin',
        );

        expect(ecdsaKey.keyType, KeyType.signing);
        expect(ecdsaKey.algorithm, EncryptionAlgorithm.ecdsaP256);
        expect(ecdsaKey.isSigningKey, true);

        // 测试密钥状态管理
        expect(aesKey.isActive, true);
        expect(aesKey.isExpired, false);

        final revokedKey = aesKey.revoke();
        expect(revokedKey.status, KeyStatus.revoked);
        expect(revokedKey.isRevoked, true);
        expect(revokedKey.isActive, false);

        final usedKey = aesKey.recordUsage();
        expect(usedKey.usageCount, 1);
        expect(usedKey.lastUsedAt, isNotNull);
      });

      testWidgets('安全配置测试', (WidgetTester tester) async {
        // 测试低安全级别配置
        final lowSecurityConfig = SecurityConfig.lowSecurity();
        expect(lowSecurityConfig.securityLevel, SecurityLevel.low);
        expect(lowSecurityConfig.encryptionEnabled, false);
        expect(lowSecurityConfig.sessionTimeout, 7200);

        // 测试高安全级别配置
        final highSecurityConfig = SecurityConfig.highSecurity();
        expect(highSecurityConfig.securityLevel, SecurityLevel.high);
        expect(highSecurityConfig.encryptionEnabled, true);
        expect(highSecurityConfig.endToEndEncryptionEnabled, true);
        expect(highSecurityConfig.biometricEnabled, true);
        expect(highSecurityConfig.twoFactorEnabled, true);
        expect(highSecurityConfig.sessionTimeout, 1800);

        // 测试企业级配置
        final enterpriseConfig = SecurityConfig.enterprise();
        expect(enterpriseConfig.securityLevel, SecurityLevel.critical);
        expect(enterpriseConfig.keyRotationInterval, 7);
        expect(enterpriseConfig.complianceRequirements, contains('GDPR'));
        expect(enterpriseConfig.rootDetectionEnabled, true);

        // 测试安全策略检查
        expect(highSecurityConfig.shouldEncrypt('user_data'), true);
        expect(highSecurityConfig.requiresStrongAuthentication(), true);
        expect(highSecurityConfig.isBiometricRequired(), true);
        expect(highSecurityConfig.isTwoFactorRequired(), true);
      });
    });

    group('企业级管理功能测试', () {
      testWidgets('系统监控测试', (WidgetTester tester) async {
        // 测试CPU监控
        final cpuMonitor = SystemMonitor.cpu(
          id: 'cpu_monitor_1',
          cpuUsage: 75.5,
          threshold: 80.0,
          tags: {'server': 'web-01', 'environment': 'production'},
        );

        expect(cpuMonitor.metricType, MetricType.cpuUsage);
        expect(cpuMonitor.currentValue, 75.5);
        expect(cpuMonitor.unit, '%');
        expect(cpuMonitor.isThresholdExceeded, false);
        expect(cpuMonitor.level, MonitorLevel.info);

        // 测试内存监控（超过阈值）
        final memoryMonitor = SystemMonitor.memory(
          id: 'memory_monitor_1',
          memoryUsage: 90.0,
          threshold: 85.0,
        );

        expect(memoryMonitor.metricType, MetricType.memoryUsage);
        expect(memoryMonitor.isThresholdExceeded, true);
        expect(memoryMonitor.level, MonitorLevel.warning);
        expect(memoryMonitor.isAlerted, true);
        expect(memoryMonitor.alertMessage, contains('内存使用率过高'));

        // 测试应用性能监控
        final perfMonitor = SystemMonitor.appPerformance(
          id: 'perf_monitor_1',
          responseTime: 1500.0,
          threshold: 1000.0,
        );

        expect(perfMonitor.metricType, MetricType.appPerformance);
        expect(perfMonitor.unit, 'ms');
        expect(perfMonitor.needsAlert, true);

        // 测试添加历史数据
        final updatedMonitor = cpuMonitor.addDataPoint(85.0);
        expect(updatedMonitor.currentValue, 85.0);
        expect(updatedMonitor.isThresholdExceeded, true);
        expect(updatedMonitor.historyData.length, 1);

        // 测试健康度评分
        expect(cpuMonitor.healthScore, greaterThan(80.0));
        expect(memoryMonitor.healthScore, lessThan(80.0));
      });

      testWidgets('配置管理测试', (WidgetTester tester) async {
        // 测试应用配置
        final appConfig = ConfigManagement.application(
          id: 'app_config_1',
          key: 'app.max_users',
          name: '最大用户数',
          value: 1000,
          environment: ConfigEnvironment.production,
          description: '系统支持的最大并发用户数',
          dataType: 'int',
          isRequired: true,
          createdBy: 'admin',
        );

        expect(appConfig.type, ConfigType.application);
        expect(appConfig.environment, ConfigEnvironment.production);
        expect(appConfig.value, 1000);
        expect(appConfig.isValid, false); // 因为状态是draft

        // 测试功能开关配置
        final featureFlag = ConfigManagement.featureFlag(
          id: 'feature_flag_1',
          key: 'feature.new_dashboard',
          name: '新仪表板功能',
          enabled: true,
          environment: ConfigEnvironment.staging,
          description: '启用新的用户仪表板界面',
          createdBy: 'product_manager',
          affectedServices: ['web-app', 'mobile-app'],
        );

        expect(featureFlag.type, ConfigType.feature);
        expect(featureFlag.value, true);
        expect(featureFlag.dataType, 'boolean');
        expect(featureFlag.affectedServices, contains('web-app'));

        // 测试安全配置
        final securityConfig = ConfigManagement.security(
          id: 'security_config_1',
          key: 'security.api_key',
          name: 'API密钥',
          value: 'sk_test_1234567890abcdef',
          environment: ConfigEnvironment.production,
          description: '第三方服务API密钥',
          createdBy: 'security_admin',
        );

        expect(securityConfig.type, ConfigType.security);
        expect(securityConfig.isSensitive, true);
        expect(securityConfig.isRequired, true);

        // 测试配置值验证
        expect(appConfig.validateValue(2000), true);
        expect(appConfig.validateValue('invalid'), false);

        // 测试配置更新
        final updatedConfig = appConfig.updateValue(
          newValue: 1500,
          updatedBy: 'admin',
          changeReason: '增加用户容量',
        );

        expect(updatedConfig.value, 1500);
        expect(updatedConfig.version, 2);
        expect(updatedConfig.changeHistory.length, 1);
        expect(updatedConfig.changeHistory.first.newValue, 1500);
        expect(updatedConfig.changeHistory.first.reason, '增加用户容量');

        // 测试配置部署
        final deployedConfig = updatedConfig.deploy(
          deployedBy: 'devops',
          effectiveAt: DateTime.now(),
        );

        expect(deployedConfig.status, ConfigStatus.deployed);
        expect(deployedConfig.isValid, true);
        expect(deployedConfig.isEffective, true);

        // 测试配置回滚
        final rolledBackConfig = deployedConfig.rollback(
          rolledBackBy: 'admin',
          reason: '发现性能问题',
        );

        expect(rolledBackConfig.status, ConfigStatus.rollback);
        expect(rolledBackConfig.value, 1000); // 回滚到原始值
        expect(rolledBackConfig.version, 3);
      });
    });

    group('模块集成测试', () {
      testWidgets('跨模块功能测试', (WidgetTester tester) async {
        // 测试分析统计与通知的集成
        // 当用户行为触发特定条件时发送通知
        final userBehavior = UserBehavior.purchase(
          id: 'purchase_behavior_1',
          userId: 'test_user_1',
          sessionId: 'test_session_1',
          productId: 'premium_plan',
          amount: 99.99,
          currency: 'USD',
        );

        // 基于用户行为创建通知
        final purchaseNotification = PushNotification.transactional(
          id: 'purchase_notification_1',
          title: '购买成功',
          body: '感谢您购买高级计划，功能已激活！',
          userId: userBehavior.userId,
          data: {
            'purchase_id': userBehavior.id,
            'product_id': userBehavior.properties['product_id'],
            'amount': userBehavior.properties['amount'],
          },
        );

        expect(purchaseNotification.userId, userBehavior.userId);
        expect(purchaseNotification.data['purchase_id'], userBehavior.id);

        // 测试离线数据与安全的集成
        // 敏感数据需要加密存储
        final sensitiveData = OfflineData.create(
          id: 'sensitive_data_1',
          dataType: 'payment_info',
          data: {
            'card_number': '****-****-****-1234',
            'expiry_date': '12/25',
            'encrypted': true,
          },
          userId: 'test_user_1',
        );

        // 验证敏感数据标记
        expect(sensitiveData.data['encrypted'], true);
        expect(sensitiveData.dataType, 'payment_info');

        // 测试企业监控与配置管理的集成
        // 配置变更应该触发监控告警
        final configMonitor = SystemMonitor(
          id: 'config_monitor_1',
          name: '配置变更监控',
          metricType: MetricType.custom,
          currentValue: 1.0,
          threshold: 0.0,
          unit: 'changes',
          description: '监控关键配置的变更',
          timestamp: DateTime.now(),
          tags: {'type': 'config_change', 'severity': 'high'},
        );

        expect(configMonitor.isThresholdExceeded, true);
        expect(configMonitor.tags['type'], 'config_change');
      });

      testWidgets('端到端功能流程测试', (WidgetTester tester) async {
        // 模拟完整的用户操作流程
        
        // 1. 用户登录 - 触发事件追踪
        final loginEvent = TrackingEvent.userAction(
          id: 'login_event_1',
          action: 'login',
          target: 'login_form',
          userId: 'test_user_1',
          sessionId: 'test_session_1',
        );

        // 2. 记录用户行为
        final loginBehavior = UserBehavior.featureUsage(
          id: 'login_behavior_1',
          userId: 'test_user_1',
          sessionId: 'test_session_1',
          featureName: 'user_authentication',
        );

        // 3. 发送欢迎通知
        final welcomeNotification = PushNotification.system(
          id: 'welcome_notification_1',
          title: '欢迎回来',
          body: '您已成功登录，开始使用应用吧！',
          userId: 'test_user_1',
        );

        // 4. 离线数据同步
        final userProfileData = OfflineData.update(
          id: 'user_profile_sync_1',
          dataType: 'user_profile',
          data: {
            'last_login': DateTime.now().toIso8601String(),
            'login_count': 42,
          },
          userId: 'test_user_1',
        );

        // 5. 系统监控记录
        final loginMonitor = SystemMonitor(
          id: 'login_monitor_1',
          name: '用户登录监控',
          metricType: MetricType.userActivity,
          currentValue: 1.0,
          threshold: 0.0,
          unit: 'logins',
          description: '监控用户登录活动',
          timestamp: DateTime.now(),
          tags: {'user_id': 'test_user_1'},
        );

        // 验证整个流程的数据一致性
        expect(loginEvent.userId, loginBehavior.userId);
        expect(loginBehavior.userId, welcomeNotification.userId);
        expect(welcomeNotification.userId, userProfileData.userId);
        expect(userProfileData.userId, loginMonitor.tags['user_id']);

        // 验证时间戳的合理性
        final now = DateTime.now();
        expect(loginEvent.timestamp.isBefore(now), true);
        expect(loginBehavior.startTime.isBefore(now), true);
        expect(welcomeNotification.createdAt.isBefore(now), true);
        expect(userProfileData.createdAt.isBefore(now), true);
        expect(loginMonitor.timestamp.isBefore(now), true);
      });
    });
  });
}
