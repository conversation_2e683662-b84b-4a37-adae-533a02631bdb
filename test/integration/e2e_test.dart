import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:flutter_template/core/design/design_system.dart';
import 'package:flutter_template/shared/widgets/loading/loading_overlay.dart';
import 'package:flutter_template/shared/widgets/error/error_widget.dart';
import 'package:flutter_template/shared/services/user_experience_service.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('端到端集成测试', () {
    testWidgets('完整用户体验流程测试', (WidgetTester tester) async {
      // 创建完整的应用
      await tester.pumpWidget(
        MaterialApp(
          title: 'Flutter Template E2E Test',
          theme: DesignSystem.lightTheme,
          darkTheme: DesignSystem.darkTheme,
          home: const E2ETestApp(),
        ),
      );

      // 等待应用完全加载
      await tester.pumpAndSettle();

      // 验证初始状态
      expect(find.text('Flutter Template E2E Test'), findsOneWidget);
      expect(find.text('欢迎使用 Flutter 模板'), findsOneWidget);

      // 测试主题切换
      await tester.tap(find.byKey(const Key('theme_toggle')));
      await tester.pumpAndSettle();
      
      // 验证主题已切换（通过检查背景色变化）
      final scaffold = tester.widget<Scaffold>(find.byType(Scaffold));
      expect(scaffold.backgroundColor != null, true);

      // 测试语言切换
      await tester.tap(find.byKey(const Key('language_toggle')));
      await tester.pumpAndSettle();
      
      // 验证语言已切换
      expect(find.text('Welcome to Flutter Template'), findsOneWidget);

      // 测试导航功能
      await tester.tap(find.byKey(const Key('navigate_to_settings')));
      await tester.pumpAndSettle();
      
      expect(find.text('设置页面'), findsOneWidget);

      // 测试返回导航
      await tester.tap(find.byKey(const Key('back_button')));
      await tester.pumpAndSettle();
      
      expect(find.text('欢迎使用 Flutter 模板'), findsOneWidget);

      // 测试加载状态
      await tester.tap(find.byKey(const Key('show_loading')));
      await tester.pumpAndSettle();
      
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('加载中...'), findsOneWidget);

      // 等待加载完成
      await tester.pump(const Duration(seconds: 3));
      await tester.pumpAndSettle();
      
      expect(find.byType(CircularProgressIndicator), findsNothing);

      // 测试错误状态
      await tester.tap(find.byKey(const Key('show_error')));
      await tester.pumpAndSettle();
      
      expect(find.byType(AppErrorWidget), findsOneWidget);
      expect(find.text('网络连接失败'), findsOneWidget);

      // 测试错误重试
      await tester.tap(find.text('重试'));
      await tester.pumpAndSettle();
      
      expect(find.byType(AppErrorWidget), findsNothing);

      // 测试表单输入
      await tester.tap(find.byKey(const Key('show_form')));
      await tester.pumpAndSettle();
      
      expect(find.byKey(const Key('username_field')), findsOneWidget);
      expect(find.byKey(const Key('email_field')), findsOneWidget);

      // 输入表单数据
      await tester.enterText(find.byKey(const Key('username_field')), 'testuser');
      await tester.enterText(find.byKey(const Key('email_field')), '<EMAIL>');
      
      // 提交表单
      await tester.tap(find.byKey(const Key('submit_button')));
      await tester.pumpAndSettle();
      
      // 验证表单提交成功
      expect(find.text('表单提交成功'), findsOneWidget);

      // 测试响应式布局
      await tester.binding.setSurfaceSize(const Size(800, 600));
      await tester.pumpAndSettle();
      
      // 验证布局适应了新的屏幕尺寸
      expect(find.byKey(const Key('responsive_content')), findsOneWidget);

      // 恢复原始屏幕尺寸
      await tester.binding.setSurfaceSize(const Size(400, 800));
      await tester.pumpAndSettle();
    });

    testWidgets('性能监控集成测试', (WidgetTester tester) async {
      final performanceMetrics = <String>[];

      await tester.pumpWidget(
        MaterialApp(
          theme: DesignSystem.lightTheme,
          home: PerformanceTestApp(
            onMetricRecorded: (metric) => performanceMetrics.add(metric),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 触发性能监控事件
      await tester.tap(find.byKey(const Key('trigger_performance')));
      await tester.pumpAndSettle();

      // 验证性能指标被记录
      expect(performanceMetrics.isNotEmpty, true);
      expect(performanceMetrics.contains('page_load'), true);

      // 测试内存使用监控
      await tester.tap(find.byKey(const Key('memory_test')));
      await tester.pumpAndSettle();

      expect(performanceMetrics.contains('memory_usage'), true);

      // 测试网络请求监控
      await tester.tap(find.byKey(const Key('network_test')));
      await tester.pumpAndSettle();

      expect(performanceMetrics.contains('network_request'), true);
    });

    testWidgets('用户体验优化集成测试', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: DesignSystem.lightTheme,
          home: const UXTestApp(),
        ),
      );

      await tester.pumpAndSettle();

      // 测试成功消息
      await tester.tap(find.byKey(const Key('show_success')));
      await tester.pumpAndSettle();
      
      expect(find.text('操作成功'), findsOneWidget);

      // 等待消息消失
      await tester.pump(const Duration(seconds: 5));
      await tester.pumpAndSettle();
      
      expect(find.text('操作成功'), findsNothing);

      // 测试错误消息
      await tester.tap(find.byKey(const Key('show_error_message')));
      await tester.pumpAndSettle();
      
      expect(find.text('操作失败'), findsOneWidget);

      // 测试确认对话框
      await tester.tap(find.byKey(const Key('show_confirm')));
      await tester.pumpAndSettle();
      
      expect(find.text('确认操作'), findsOneWidget);
      expect(find.text('确认'), findsOneWidget);
      expect(find.text('取消'), findsOneWidget);

      // 点击确认
      await tester.tap(find.text('确认'));
      await tester.pumpAndSettle();
      
      expect(find.text('确认操作'), findsNothing);

      // 测试输入对话框
      await tester.tap(find.byKey(const Key('show_input')));
      await tester.pumpAndSettle();
      
      expect(find.text('输入信息'), findsOneWidget);
      
      // 输入文本
      await tester.enterText(find.byType(TextFormField), 'test input');
      await tester.tap(find.text('确认'));
      await tester.pumpAndSettle();
      
      expect(find.text('输入信息'), findsNothing);

      // 测试选择对话框
      await tester.tap(find.byKey(const Key('show_choice')));
      await tester.pumpAndSettle();
      
      expect(find.text('选择选项'), findsOneWidget);
      expect(find.text('选项 1'), findsOneWidget);
      expect(find.text('选项 2'), findsOneWidget);

      // 选择选项
      await tester.tap(find.text('选项 1'));
      await tester.pumpAndSettle();
      
      expect(find.text('选择选项'), findsNothing);
    });

    testWidgets('完整应用流程压力测试', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: DesignSystem.lightTheme,
          darkTheme: DesignSystem.darkTheme,
          home: const StressTestApp(),
        ),
      );

      await tester.pumpAndSettle();

      // 执行多次操作来测试应用稳定性
      for (int i = 0; i < 10; i++) {
        // 切换主题
        await tester.tap(find.byKey(const Key('theme_toggle')));
        await tester.pump(const Duration(milliseconds: 100));

        // 切换语言
        await tester.tap(find.byKey(const Key('language_toggle')));
        await tester.pump(const Duration(milliseconds: 100));

        // 导航到不同页面
        await tester.tap(find.byKey(const Key('navigate_page_1')));
        await tester.pump(const Duration(milliseconds: 100));
        
        await tester.tap(find.byKey(const Key('navigate_page_2')));
        await tester.pump(const Duration(milliseconds: 100));
        
        await tester.tap(find.byKey(const Key('navigate_home')));
        await tester.pump(const Duration(milliseconds: 100));

        // 显示和隐藏加载状态
        await tester.tap(find.byKey(const Key('toggle_loading')));
        await tester.pump(const Duration(milliseconds: 50));
        
        await tester.tap(find.byKey(const Key('toggle_loading')));
        await tester.pump(const Duration(milliseconds: 50));
      }

      await tester.pumpAndSettle();

      // 验证应用仍然正常工作
      expect(find.byKey(const Key('stress_test_app')), findsOneWidget);
      expect(tester.takeException(), isNull);
    });
  });
}

// 测试应用组件
class E2ETestApp extends StatefulWidget {
  const E2ETestApp({super.key});

  @override
  State<E2ETestApp> createState() => _E2ETestAppState();
}

class _E2ETestAppState extends State<E2ETestApp> {
  bool _isDarkTheme = false;
  bool _isEnglish = false;
  bool _isLoading = false;
  bool _showError = false;
  bool _showForm = false;
  String _message = '';

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      theme: _isDarkTheme ? DesignSystem.darkTheme : DesignSystem.lightTheme,
      home: Scaffold(
        appBar: AppBar(
          title: const Text('Flutter Template E2E Test'),
          actions: [
            IconButton(
              key: const Key('theme_toggle'),
              icon: Icon(_isDarkTheme ? Icons.light_mode : Icons.dark_mode),
              onPressed: () => setState(() => _isDarkTheme = !_isDarkTheme),
            ),
            IconButton(
              key: const Key('language_toggle'),
              icon: const Icon(Icons.language),
              onPressed: () => setState(() => _isEnglish = !_isEnglish),
            ),
          ],
        ),
        body: LoadingOverlay(
          isLoading: _isLoading,
          loadingText: '加载中...',
          child: _buildBody(),
        ),
      ),
    );
  }

  Widget _buildBody() {
    if (_showError) {
      return AppErrorWidget.network(
        onRetry: () => setState(() => _showError = false),
      );
    }

    if (_showForm) {
      return _buildForm();
    }

    return Padding(
      key: const Key('responsive_content'),
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Text(
            _isEnglish ? 'Welcome to Flutter Template' : '欢迎使用 Flutter 模板',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const SizedBox(height: 20),
          if (_message.isNotEmpty) ...[
            Text(_message),
            const SizedBox(height: 20),
          ],
          ElevatedButton(
            key: const Key('navigate_to_settings'),
            onPressed: () => _navigateToSettings(),
            child: const Text('前往设置'),
          ),
          const SizedBox(height: 10),
          ElevatedButton(
            key: const Key('show_loading'),
            onPressed: () => _showLoading(),
            child: const Text('显示加载'),
          ),
          const SizedBox(height: 10),
          ElevatedButton(
            key: const Key('show_error'),
            onPressed: () => setState(() => _showError = true),
            child: const Text('显示错误'),
          ),
          const SizedBox(height: 10),
          ElevatedButton(
            key: const Key('show_form'),
            onPressed: () => setState(() => _showForm = true),
            child: const Text('显示表单'),
          ),
        ],
      ),
    );
  }

  Widget _buildForm() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          const TextField(
            key: Key('username_field'),
            decoration: InputDecoration(labelText: '用户名'),
          ),
          const SizedBox(height: 16),
          const TextField(
            key: Key('email_field'),
            decoration: InputDecoration(labelText: '邮箱'),
          ),
          const SizedBox(height: 20),
          ElevatedButton(
            key: const Key('submit_button'),
            onPressed: () => setState(() {
              _showForm = false;
              _message = '表单提交成功';
            }),
            child: const Text('提交'),
          ),
        ],
      ),
    );
  }

  void _navigateToSettings() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(
            title: const Text('设置'),
            leading: IconButton(
              key: const Key('back_button'),
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Navigator.pop(context),
            ),
          ),
          body: const Center(
            child: Text('设置页面'),
          ),
        ),
      ),
    );
  }

  void _showLoading() {
    setState(() => _isLoading = true);
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    });
  }
}

// 性能测试应用
class PerformanceTestApp extends StatelessWidget {
  final void Function(String) onMetricRecorded;

  const PerformanceTestApp({
    super.key,
    required this.onMetricRecorded,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          ElevatedButton(
            key: const Key('trigger_performance'),
            onPressed: () => onMetricRecorded('page_load'),
            child: const Text('触发性能监控'),
          ),
          ElevatedButton(
            key: const Key('memory_test'),
            onPressed: () => onMetricRecorded('memory_usage'),
            child: const Text('内存测试'),
          ),
          ElevatedButton(
            key: const Key('network_test'),
            onPressed: () => onMetricRecorded('network_request'),
            child: const Text('网络测试'),
          ),
        ],
      ),
    );
  }
}

// 用户体验测试应用
class UXTestApp extends StatelessWidget {
  const UXTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    final uxService = UserExperienceService();

    return Scaffold(
      body: Column(
        children: [
          ElevatedButton(
            key: const Key('show_success'),
            onPressed: () => uxService.showSuccessMessage('操作成功'),
            child: const Text('显示成功消息'),
          ),
          ElevatedButton(
            key: const Key('show_error_message'),
            onPressed: () => uxService.showErrorMessage('操作失败'),
            child: const Text('显示错误消息'),
          ),
          ElevatedButton(
            key: const Key('show_confirm'),
            onPressed: () => uxService.showConfirmDialog(
              title: '确认操作',
              message: '您确定要执行此操作吗？',
            ),
            child: const Text('显示确认对话框'),
          ),
          ElevatedButton(
            key: const Key('show_input'),
            onPressed: () => uxService.showInputDialog(
              title: '输入信息',
              message: '请输入您的信息',
            ),
            child: const Text('显示输入对话框'),
          ),
          ElevatedButton(
            key: const Key('show_choice'),
            onPressed: () => uxService.showChoiceDialog(
              title: '选择选项',
              choices: [
                const ChoiceItem(value: 1, title: '选项 1'),
                const ChoiceItem(value: 2, title: '选项 2'),
              ],
            ),
            child: const Text('显示选择对话框'),
          ),
        ],
      ),
    );
  }
}

// 压力测试应用
class StressTestApp extends StatefulWidget {
  const StressTestApp({super.key});

  @override
  State<StressTestApp> createState() => _StressTestAppState();
}

class _StressTestAppState extends State<StressTestApp> {
  bool _isDarkTheme = false;
  bool _isEnglish = false;
  bool _isLoading = false;
  int _currentPage = 0;

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      key: const Key('stress_test_app'),
      theme: _isDarkTheme ? DesignSystem.darkTheme : DesignSystem.lightTheme,
      home: Scaffold(
        body: LoadingOverlay(
          isLoading: _isLoading,
          child: Column(
            children: [
              Row(
                children: [
                  ElevatedButton(
                    key: const Key('theme_toggle'),
                    onPressed: () => setState(() => _isDarkTheme = !_isDarkTheme),
                    child: const Text('切换主题'),
                  ),
                  ElevatedButton(
                    key: const Key('language_toggle'),
                    onPressed: () => setState(() => _isEnglish = !_isEnglish),
                    child: const Text('切换语言'),
                  ),
                  ElevatedButton(
                    key: const Key('toggle_loading'),
                    onPressed: () => setState(() => _isLoading = !_isLoading),
                    child: const Text('切换加载'),
                  ),
                ],
              ),
              Row(
                children: [
                  ElevatedButton(
                    key: const Key('navigate_page_1'),
                    onPressed: () => setState(() => _currentPage = 1),
                    child: const Text('页面1'),
                  ),
                  ElevatedButton(
                    key: const Key('navigate_page_2'),
                    onPressed: () => setState(() => _currentPage = 2),
                    child: const Text('页面2'),
                  ),
                  ElevatedButton(
                    key: const Key('navigate_home'),
                    onPressed: () => setState(() => _currentPage = 0),
                    child: const Text('首页'),
                  ),
                ],
              ),
              Expanded(
                child: Center(
                  child: Text('当前页面: $_currentPage'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
