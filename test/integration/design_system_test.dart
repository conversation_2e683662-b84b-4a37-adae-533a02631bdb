import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_template/core/design/design_system.dart';
import 'package:flutter_template/core/design/design_tokens.dart';
import 'package:flutter_template/shared/widgets/buttons/app_button.dart';
import 'package:flutter_template/shared/widgets/inputs/app_text_field.dart';
import 'package:flutter_template/shared/widgets/cards/app_card.dart';
import 'package:flutter_template/shared/widgets/responsive/responsive_builder.dart';

void main() {
  group('设计系统集成测试', () {
    testWidgets('设计令牌应用正确', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: DesignSystem.lightTheme,
          home: Scaffold(
            body: Container(
              padding: DesignTokens.spacingM,
              child: Column(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: DesignTokens.radiusM,
                      boxShadow: DesignTokens.shadowM,
                    ),
                    child: const Text('测试容器'),
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      // 验证主题应用
      final theme = Theme.of(tester.element(find.byType(Scaffold)));
      expect(theme.useMaterial3, isTrue);
      expect(theme.colorScheme.brightness, Brightness.light);

      // 验证设计令牌
      final container = tester.widget<Container>(find.byType(Container).first);
      final decoration = container.decoration as BoxDecoration;
      expect(decoration.borderRadius, DesignTokens.radiusM);
      expect(decoration.boxShadow, DesignTokens.shadowM);
    });

    testWidgets('按钮组件功能正常', (WidgetTester tester) async {
      bool buttonPressed = false;

      await tester.pumpWidget(
        MaterialApp(
          theme: DesignSystem.lightTheme,
          home: Scaffold(
            body: Column(
              children: [
                AppButton.primary(
                  text: '主要按钮',
                  onPressed: () => buttonPressed = true,
                ),
                const AppButton.secondary(
                  text: '次要按钮',
                ),
                AppButton.outlined(
                  text: '轮廓按钮',
                  icon: const Icon(Icons.add),
                ),
                const AppButton.text(
                  text: '文本按钮',
                ),
                const AppButton.primary(
                  text: '加载按钮',
                  isLoading: true,
                ),
              ],
            ),
          ),
        ),
      );

      // 验证按钮渲染
      expect(find.byType(AppButton), findsNWidgets(5));
      expect(find.text('主要按钮'), findsOneWidget);
      expect(find.text('次要按钮'), findsOneWidget);
      expect(find.text('轮廓按钮'), findsOneWidget);
      expect(find.text('文本按钮'), findsOneWidget);

      // 验证按钮点击
      await tester.tap(find.text('主要按钮'));
      expect(buttonPressed, isTrue);

      // 验证加载状态
      expect(find.byType(CircularProgressIndicator), findsOneWidget);

      // 验证图标
      expect(find.byIcon(Icons.add), findsOneWidget);
    });

    testWidgets('输入框组件功能正常', (WidgetTester tester) async {
      final controller = TextEditingController();

      await tester.pumpWidget(
        MaterialApp(
          theme: DesignSystem.lightTheme,
          home: Scaffold(
            body: Column(
              children: [
                AppTextField(
                  label: '用户名',
                  hint: '请输入用户名',
                  controller: controller,
                ),
                const AppTextField.email(
                  label: '邮箱',
                  hint: '请输入邮箱',
                ),
                const AppTextField.password(
                  label: '密码',
                  hint: '请输入密码',
                ),
                const AppTextField.multiline(
                  label: '描述',
                  hint: '请输入描述',
                ),
              ],
            ),
          ),
        ),
      );

      // 验证输入框渲染
      expect(find.byType(AppTextField), findsNWidgets(4));
      expect(find.text('用户名'), findsOneWidget);
      expect(find.text('邮箱'), findsOneWidget);
      expect(find.text('密码'), findsOneWidget);
      expect(find.text('描述'), findsOneWidget);

      // 验证输入功能
      await tester.enterText(find.byWidget(find.byType(AppTextField).first.evaluate().first.widget), 'test_user');
      expect(controller.text, 'test_user');

      // 验证密码可见性切换
      expect(find.byIcon(Icons.visibility), findsOneWidget);
      await tester.tap(find.byIcon(Icons.visibility));
      await tester.pump();
      expect(find.byIcon(Icons.visibility_off), findsOneWidget);
    });

    testWidgets('卡片组件功能正常', (WidgetTester tester) async {
      bool cardTapped = false;

      await tester.pumpWidget(
        MaterialApp(
          theme: DesignSystem.lightTheme,
          home: Scaffold(
            body: Column(
              children: [
                AppCard.basic(
                  onTap: () => cardTapped = true,
                  child: const Text('基础卡片'),
                ),
                const AppCard.info(
                  child: Text('信息卡片'),
                ),
                const AppCard.action(
                  child: Text('操作卡片'),
                ),
                const AppCard.stats(
                  child: Text('统计卡片'),
                ),
                const InfoCard(
                  title: '信息标题',
                  content: '信息内容',
                  icon: Icon(Icons.info),
                ),
                const StatsCard(
                  title: '统计标题',
                  value: '100',
                  unit: '个',
                  change: '+10%',
                  isPositiveChange: true,
                ),
              ],
            ),
          ),
        ),
      );

      // 验证卡片渲染
      expect(find.byType(AppCard), findsNWidgets(4));
      expect(find.byType(InfoCard), findsOneWidget);
      expect(find.byType(StatsCard), findsOneWidget);

      // 验证卡片点击
      await tester.tap(find.text('基础卡片'));
      expect(cardTapped, isTrue);

      // 验证信息卡片内容
      expect(find.text('信息标题'), findsOneWidget);
      expect(find.text('信息内容'), findsOneWidget);

      // 验证统计卡片内容
      expect(find.text('统计标题'), findsOneWidget);
      expect(find.text('100'), findsOneWidget);
      expect(find.text('+10%'), findsOneWidget);
      expect(find.byIcon(Icons.trending_up), findsOneWidget);
    });

    testWidgets('响应式布局功能正常', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: DesignSystem.lightTheme,
          home: Scaffold(
            body: ResponsiveBuilder(
              mobile: (context, constraints) => const Text('移动端布局'),
              tablet: (context, constraints) => const Text('平板端布局'),
              desktop: (context, constraints) => const Text('桌面端布局'),
            ),
          ),
        ),
      );

      // 默认情况下应该显示移动端布局
      expect(find.text('移动端布局'), findsOneWidget);

      // 测试不同屏幕尺寸
      await tester.binding.setSurfaceSize(const Size(800, 600));
      await tester.pumpAndSettle();
      expect(find.text('平板端布局'), findsOneWidget);

      await tester.binding.setSurfaceSize(const Size(1200, 800));
      await tester.pumpAndSettle();
      expect(find.text('桌面端布局'), findsOneWidget);
    });

    testWidgets('响应式网格功能正常', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: DesignSystem.lightTheme,
          home: Scaffold(
            body: ResponsiveGrid(
              mobileColumns: 1,
              tabletColumns: 2,
              desktopColumns: 3,
              children: List.generate(
                6,
                (index) => Container(
                  color: Colors.blue,
                  child: Text('项目 $index'),
                ),
              ),
            ),
          ),
        ),
      );

      // 验证网格渲染
      expect(find.byType(GridView), findsOneWidget);
      expect(find.textContaining('项目'), findsNWidgets(6));
    });

    testWidgets('深色主题功能正常', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: DesignSystem.darkTheme,
          home: const Scaffold(
            body: Text('深色主题测试'),
          ),
        ),
      );

      // 验证深色主题应用
      final theme = Theme.of(tester.element(find.byType(Scaffold)));
      expect(theme.colorScheme.brightness, Brightness.dark);
      expect(theme.useMaterial3, isTrue);
    });

    testWidgets('主题切换功能正常', (WidgetTester tester) async {
      ThemeMode currentTheme = ThemeMode.light;

      await tester.pumpWidget(
        StatefulBuilder(
          builder: (context, setState) {
            return MaterialApp(
              theme: DesignSystem.lightTheme,
              darkTheme: DesignSystem.darkTheme,
              themeMode: currentTheme,
              home: Scaffold(
                body: ElevatedButton(
                  onPressed: () {
                    setState(() {
                      currentTheme = currentTheme == ThemeMode.light
                          ? ThemeMode.dark
                          : ThemeMode.light;
                    });
                  },
                  child: const Text('切换主题'),
                ),
              ),
            );
          },
        ),
      );

      // 验证初始主题
      var theme = Theme.of(tester.element(find.byType(Scaffold)));
      expect(theme.colorScheme.brightness, Brightness.light);

      // 切换主题
      await tester.tap(find.text('切换主题'));
      await tester.pumpAndSettle();

      // 验证主题切换
      theme = Theme.of(tester.element(find.byType(Scaffold)));
      expect(theme.colorScheme.brightness, Brightness.dark);
    });
  });

  group('设计系统性能测试', () {
    testWidgets('大量组件渲染性能', (WidgetTester tester) async {
      final stopwatch = Stopwatch()..start();

      await tester.pumpWidget(
        MaterialApp(
          theme: DesignSystem.lightTheme,
          home: Scaffold(
            body: ListView.builder(
              itemCount: 100,
              itemBuilder: (context, index) {
                return AppCard.basic(
                  child: ListTile(
                    leading: const Icon(Icons.person),
                    title: Text('项目 $index'),
                    subtitle: Text('描述 $index'),
                    trailing: AppButton.primary(
                      text: '操作',
                      onPressed: () {},
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      );

      stopwatch.stop();
      
      // 验证渲染时间在合理范围内（小于1秒）
      expect(stopwatch.elapsedMilliseconds, lessThan(1000));

      // 验证组件正确渲染
      expect(find.byType(AppCard), findsWidgets);
      expect(find.byType(AppButton), findsWidgets);
    });

    testWidgets('主题切换性能', (WidgetTester tester) async {
      ThemeMode currentTheme = ThemeMode.light;

      await tester.pumpWidget(
        StatefulBuilder(
          builder: (context, setState) {
            return MaterialApp(
              theme: DesignSystem.lightTheme,
              darkTheme: DesignSystem.darkTheme,
              themeMode: currentTheme,
              home: Scaffold(
                body: Column(
                  children: [
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          currentTheme = currentTheme == ThemeMode.light
                              ? ThemeMode.dark
                              : ThemeMode.light;
                        });
                      },
                      child: const Text('切换主题'),
                    ),
                    Expanded(
                      child: ListView.builder(
                        itemCount: 50,
                        itemBuilder: (context, index) {
                          return AppCard.basic(
                            child: Text('项目 $index'),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      );

      // 测试多次主题切换的性能
      final stopwatch = Stopwatch()..start();

      for (int i = 0; i < 5; i++) {
        await tester.tap(find.text('切换主题'));
        await tester.pumpAndSettle();
      }

      stopwatch.stop();

      // 验证主题切换时间在合理范围内（小于2秒）
      expect(stopwatch.elapsedMilliseconds, lessThan(2000));
    });
  });
}
