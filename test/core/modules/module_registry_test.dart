/// ModuleRegistry测试
/// 
/// 测试模块注册系统的各种场景
library;

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_enterprise_app/core/modules/module_registry.dart';
import 'package:flutter_enterprise_app/core/config/feature_config.dart';
import 'package:flutter_enterprise_app/core/di/injection.dart';
import '../../test_base.dart';

/// 测试用模块A
class TestModuleA extends ModuleBase {
  TestModuleA() : super(
    name: 'test_module_a',
    version: '1.0.0',
    description: 'Test Module A',
    dependencies: [],
  );

  bool _onInitializeCalled = false;
  bool _onRegisterServicesCalled = false;
  bool _onUnregisterServicesCalled = false;
  bool _onDisposeCalled = false;

  bool get onInitializeCalled => _onInitializeCalled;
  bool get onRegisterServicesCalled => _onRegisterServicesCalled;
  bool get onUnregisterServicesCalled => _onUnregisterServicesCalled;
  bool get onDisposeCalled => _onDisposeCalled;

  @override
  Future<void> onInitialize() async {
    _onInitializeCalled = true;
  }

  @override
  Future<void> onRegisterServices() async {
    _onRegisterServicesCalled = true;
  }

  @override
  Future<void> onUnregisterServices() async {
    _onUnregisterServicesCalled = true;
  }

  @override
  Future<void> onDispose() async {
    _onDisposeCalled = true;
  }

  void reset() {
    _onInitializeCalled = false;
    _onRegisterServicesCalled = false;
    _onUnregisterServicesCalled = false;
    _onDisposeCalled = false;
  }
}

/// 测试用模块B（依赖模块A）
class TestModuleB extends ModuleBase {
  TestModuleB() : super(
    name: 'test_module_b',
    version: '1.0.0',
    description: 'Test Module B',
    dependencies: ['test_module_a'],
  );

  bool _onInitializeCalled = false;
  bool _onRegisterServicesCalled = false;
  bool _onUnregisterServicesCalled = false;
  bool _onDisposeCalled = false;

  bool get onInitializeCalled => _onInitializeCalled;
  bool get onRegisterServicesCalled => _onRegisterServicesCalled;
  bool get onUnregisterServicesCalled => _onUnregisterServicesCalled;
  bool get onDisposeCalled => _onDisposeCalled;

  @override
  Future<void> onInitialize() async {
    _onInitializeCalled = true;
  }

  @override
  Future<void> onRegisterServices() async {
    _onRegisterServicesCalled = true;
  }

  @override
  Future<void> onUnregisterServices() async {
    _onUnregisterServicesCalled = true;
  }

  @override
  Future<void> onDispose() async {
    _onDisposeCalled = true;
  }

  void reset() {
    _onInitializeCalled = false;
    _onRegisterServicesCalled = false;
    _onUnregisterServicesCalled = false;
    _onDisposeCalled = false;
  }
}

/// 测试用失败模块
class TestFailingModule extends ModuleBase {
  TestFailingModule() : super(
    name: 'test_failing_module',
    version: '1.0.0',
    description: 'Test Failing Module',
    dependencies: [],
  );

  @override
  Future<void> onInitialize() async {
    throw Exception('Initialization failed');
  }

  @override
  Future<void> onRegisterServices() async {}

  @override
  Future<void> onUnregisterServices() async {}

  @override
  Future<void> onDispose() async {}
}

void main() {
  group('ModuleRegistry Tests', () {
    late ModuleRegistry registry;
    late TestModuleA moduleA;
    late TestModuleB moduleB;

    setUp(() async {
      TestWidgetsFlutterBinding.ensureInitialized();
      
      // 创建新的注册表实例
      registry = ModuleRegistry.instance;
      await registry.clear();
      
      // 创建测试模块
      moduleA = TestModuleA();
      moduleB = TestModuleB();
      
      // 设置功能配置
      final config = {
        'features': {
          'test_module_a': true,
          'test_module_b': true,
        }
      };
      await FeatureConfig.initialize(config);
    });

    tearDown(() async {
      moduleA.reset();
      moduleB.reset();
      await registry.clear();
      await GetIt.instance.reset();
    });

    group('模块注册测试', () {
      test('应该能够注册单个模块', () {
        // Act
        registry.registerModule(moduleA);

        // Assert
        expect(registry.isModuleRegistered('test_module_a'), isTrue);
        expect(registry.getModule('test_module_a'), equals(moduleA));
      });

      test('应该能够注册多个模块', () {
        // Act
        registry.registerModules([moduleA, moduleB]);

        // Assert
        expect(registry.isModuleRegistered('test_module_a'), isTrue);
        expect(registry.isModuleRegistered('test_module_b'), isTrue);
        expect(registry.getAllModules(), hasLength(2));
      });

      test('应该阻止重复注册同名模块', () {
        // Arrange
        registry.registerModule(moduleA);

        // Act & Assert
        expect(
          () => registry.registerModule(TestModuleA()),
          throwsA(isA<ArgumentError>()),
        );
      });

      test('应该能够获取不存在的模块时返回null', () {
        // Act
        final module = registry.getModule('non_existent_module');

        // Assert
        expect(module, isNull);
      });
    });

    group('模块初始化测试', () {
      test('应该按正确顺序初始化模块', () async {
        // Arrange
        registry.registerModules([moduleB, moduleA]); // 故意颠倒顺序

        // Act
        await registry.initializeModules();

        // Assert
        expect(moduleA.onInitializeCalled, isTrue);
        expect(moduleA.onRegisterServicesCalled, isTrue);
        expect(moduleB.onInitializeCalled, isTrue);
        expect(moduleB.onRegisterServicesCalled, isTrue);
        expect(moduleA.isInitialized, isTrue);
        expect(moduleB.isInitialized, isTrue);
      });

      test('应该只初始化启用的模块', () async {
        // Arrange
        final config = {
          'features': {
            'test_module_a': true,
            'test_module_b': false, // 禁用模块B
          }
        };
        await FeatureConfig.initialize(config);
        
        registry.registerModules([moduleA, moduleB]);

        // Act
        await registry.initializeModules();

        // Assert
        expect(moduleA.isInitialized, isTrue);
        expect(moduleB.isInitialized, isFalse);
      });

      test('应该处理初始化失败的模块', () async {
        // Arrange
        final failingModule = TestFailingModule();
        final config = {
          'features': {
            'test_failing_module': true,
          }
        };
        await FeatureConfig.initialize(config);
        
        registry.registerModule(failingModule);

        // Act & Assert
        expect(
          () async => await registry.initializeModules(),
          throwsA(isA<Exception>()),
        );
      });

      test('应该检测循环依赖', () async {
        // Arrange
        final moduleC = TestModuleA();
        moduleC.dependencies.add('test_module_b');
        moduleB.dependencies.add('test_module_c');
        
        final config = {
          'features': {
            'test_module_a': true,
            'test_module_b': true,
            'test_module_c': true,
          }
        };
        await FeatureConfig.initialize(config);
        
        registry.registerModules([moduleA, moduleB, moduleC]);

        // Act & Assert
        expect(
          () async => await registry.initializeModules(),
          throwsA(isA<StateError>()),
        );
      });
    });

    group('模块销毁测试', () {
      test('应该按相反顺序销毁模块', () async {
        // Arrange
        registry.registerModules([moduleA, moduleB]);
        await registry.initializeModules();

        // Act
        await registry.disposeModules();

        // Assert
        expect(moduleA.onUnregisterServicesCalled, isTrue);
        expect(moduleA.onDisposeCalled, isTrue);
        expect(moduleB.onUnregisterServicesCalled, isTrue);
        expect(moduleB.onDisposeCalled, isTrue);
        expect(moduleA.isInitialized, isFalse);
        expect(moduleB.isInitialized, isFalse);
      });

      test('应该只销毁已初始化的模块', () async {
        // Arrange
        registry.registerModule(moduleA);
        // 不初始化模块

        // Act
        await registry.disposeModules();

        // Assert
        expect(moduleA.onUnregisterServicesCalled, isFalse);
        expect(moduleA.onDisposeCalled, isFalse);
      });
    });

    group('模块刷新测试', () {
      test('应该能够动态启用模块', () async {
        // Arrange
        final config = {
          'features': {
            'test_module_a': false, // 初始禁用
          }
        };
        await FeatureConfig.initialize(config);
        
        registry.registerModule(moduleA);
        await registry.initializeModules();
        
        expect(moduleA.isInitialized, isFalse);

        // 动态启用功能
        final newConfig = {
          'features': {
            'test_module_a': true,
          }
        };
        await FeatureConfig.initialize(newConfig);

        // Act
        await registry.refreshModules();

        // Assert
        expect(moduleA.isInitialized, isTrue);
      });

      test('应该能够动态禁用模块', () async {
        // Arrange
        registry.registerModule(moduleA);
        await registry.initializeModules();
        
        expect(moduleA.isInitialized, isTrue);

        // 动态禁用功能
        final newConfig = {
          'features': {
            'test_module_a': false,
          }
        };
        await FeatureConfig.initialize(newConfig);

        // Act
        await registry.refreshModules();

        // Assert
        expect(moduleA.isInitialized, isFalse);
      });
    });

    group('模块状态查询测试', () {
      test('应该正确返回模块状态摘要', () async {
        // Arrange
        registry.registerModules([moduleA, moduleB]);
        await registry.initializeModules();

        // Act
        final summary = registry.getModuleStatusSummary();

        // Assert
        expect(summary, containsPair('test_module_a', anything));
        expect(summary, containsPair('test_module_b', anything));
        
        final moduleAStatus = summary['test_module_a'] as Map<String, dynamic>;
        expect(moduleAStatus['isRegistered'], isTrue);
        expect(moduleAStatus['isEnabled'], isTrue);
        expect(moduleAStatus['isInitialized'], isTrue);
        expect(moduleAStatus['version'], equals('1.0.0'));
      });

      test('应该正确返回启用的模块列表', () async {
        // Arrange
        final config = {
          'features': {
            'test_module_a': true,
            'test_module_b': false,
          }
        };
        await FeatureConfig.initialize(config);
        
        registry.registerModules([moduleA, moduleB]);

        // Act
        final enabledModules = registry.getEnabledModules();

        // Assert
        expect(enabledModules, hasLength(1));
        expect(enabledModules.first.name, equals('test_module_a'));
      });

      test('应该正确检查模块状态', () async {
        // Arrange
        registry.registerModule(moduleA);
        await registry.initializeModules();

        // Act & Assert
        expect(registry.isModuleRegistered('test_module_a'), isTrue);
        expect(registry.isModuleEnabled('test_module_a'), isTrue);
        expect(registry.isModuleInitialized('test_module_a'), isTrue);
        
        expect(registry.isModuleRegistered('non_existent'), isFalse);
        expect(registry.isModuleEnabled('non_existent'), isFalse);
        expect(registry.isModuleInitialized('non_existent'), isFalse);
      });
    });

    group('错误处理测试', () {
      test('应该处理缺失依赖的模块', () async {
        // Arrange
        final config = {
          'features': {
            'test_module_b': true, // 启用模块B但不启用其依赖模块A
            'test_module_a': false,
          }
        };
        await FeatureConfig.initialize(config);
        
        registry.registerModule(moduleB); // 只注册模块B，不注册其依赖

        // Act & Assert
        expect(
          () async => await registry.initializeModules(),
          throwsA(isA<StateError>()),
        );
      });

      test('应该处理重复初始化', () async {
        // Arrange
        registry.registerModule(moduleA);
        await registry.initializeModules();

        // Act
        await registry.initializeModules(); // 重复初始化

        // Assert
        // 应该不会抛出异常，且模块状态保持正常
        expect(moduleA.isInitialized, isTrue);
      });

      test('应该处理重复销毁', () async {
        // Arrange
        registry.registerModule(moduleA);
        await registry.initializeModules();
        await registry.disposeModules();

        // Act
        await registry.disposeModules(); // 重复销毁

        // Assert
        // 应该不会抛出异常
        expect(moduleA.isInitialized, isFalse);
      });
    });
  });
}
