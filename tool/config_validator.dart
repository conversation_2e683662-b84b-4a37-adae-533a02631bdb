/// 配置验证工具
/// 
/// 用于验证应用配置的正确性和完整性
library;

import 'dart:io';
import 'dart:convert';
import 'package:yaml/yaml.dart';

/// 配置验证器
class ConfigValidator {
  static const String _configPath = 'assets/config';
  
  /// 运行配置验证
  static Future<void> runValidation([String? environment]) async {
    print('🔧 开始配置验证...');
    
    final env = environment ?? 'production';
    final issues = <ValidationIssue>[];
    
    try {
      // 1. 验证基础配置文件
      print('验证基础配置文件...');
      issues.addAll(await _validateBaseConfig());
      
      // 2. 验证环境配置文件
      print('验证环境配置文件...');
      issues.addAll(await _validateEnvironmentConfig(env));
      
      // 3. 验证功能配置
      print('验证功能配置...');
      issues.addAll(await _validateFeatureConfig(env));
      
      // 4. 验证安全配置
      print('验证安全配置...');
      issues.addAll(await _validateSecurityConfig(env));
      
      // 5. 验证依赖关系
      print('验证依赖关系...');
      issues.addAll(await _validateDependencies(env));
      
      // 6. 验证构建配置
      print('验证构建配置...');
      issues.addAll(await _validateBuildConfig());
      
      // 7. 生成验证报告
      await _generateValidationReport(issues, env);
      
      // 8. 评估验证结果
      _evaluateValidationResults(issues);
      
    } catch (e) {
      print('❌ 配置验证过程中发生错误: $e');
      exit(1);
    }
  }
  
  /// 验证基础配置文件
  static Future<List<ValidationIssue>> _validateBaseConfig() async {
    final issues = <ValidationIssue>[];
    
    // 检查基础配置文件是否存在
    final baseConfigFile = File('$_configPath/app_config.yaml');
    if (!await baseConfigFile.exists()) {
      issues.add(ValidationIssue(
        type: ValidationIssueType.missingFile,
        severity: ValidationSeverity.critical,
        file: baseConfigFile.path,
        description: '基础配置文件不存在',
        recommendation: '创建 app_config.yaml 文件',
      ));
      return issues;
    }
    
    // 解析配置文件
    try {
      final content = await baseConfigFile.readAsString();
      final config = loadYaml(content) as Map<String, dynamic>;
      
      // 验证必要的配置项
      final requiredKeys = ['app', 'features', 'defaults'];
      for (final key in requiredKeys) {
        if (!config.containsKey(key)) {
          issues.add(ValidationIssue(
            type: ValidationIssueType.missingConfig,
            severity: ValidationSeverity.high,
            file: baseConfigFile.path,
            description: '缺少必要的配置项: $key',
            recommendation: '添加 $key 配置项',
          ));
        }
      }
      
      // 验证应用配置
      if (config.containsKey('app')) {
        final appConfig = config['app'] as Map<String, dynamic>;
        final requiredAppKeys = ['name', 'version'];
        for (final key in requiredAppKeys) {
          if (!appConfig.containsKey(key)) {
            issues.add(ValidationIssue(
              type: ValidationIssueType.missingConfig,
              severity: ValidationSeverity.medium,
              file: baseConfigFile.path,
              description: '缺少应用配置项: app.$key',
              recommendation: '添加 app.$key 配置项',
            ));
          }
        }
      }
      
    } catch (e) {
      issues.add(ValidationIssue(
        type: ValidationIssueType.invalidFormat,
        severity: ValidationSeverity.critical,
        file: baseConfigFile.path,
        description: '配置文件格式错误: $e',
        recommendation: '检查 YAML 格式是否正确',
      ));
    }
    
    return issues;
  }
  
  /// 验证环境配置文件
  static Future<List<ValidationIssue>> _validateEnvironmentConfig(String environment) async {
    final issues = <ValidationIssue>[];
    
    final envConfigFile = File('$_configPath/app_config.$environment.yaml');
    if (!await envConfigFile.exists()) {
      issues.add(ValidationIssue(
        type: ValidationIssueType.missingFile,
        severity: ValidationSeverity.high,
        file: envConfigFile.path,
        description: '环境配置文件不存在: $environment',
        recommendation: '创建 app_config.$environment.yaml 文件',
      ));
      return issues;
    }
    
    try {
      final content = await envConfigFile.readAsString();
      final config = loadYaml(content) as Map<String, dynamic>;
      
      // 验证生产环境的特殊要求
      if (environment == 'production') {
        await _validateProductionConfig(config, envConfigFile.path, issues);
      }
      
    } catch (e) {
      issues.add(ValidationIssue(
        type: ValidationIssueType.invalidFormat,
        severity: ValidationSeverity.critical,
        file: envConfigFile.path,
        description: '环境配置文件格式错误: $e',
        recommendation: '检查 YAML 格式是否正确',
      ));
    }
    
    return issues;
  }
  
  /// 验证生产环境配置
  static Future<void> _validateProductionConfig(
    Map<String, dynamic> config,
    String filePath,
    List<ValidationIssue> issues,
  ) async {
    // 验证调试模式必须关闭
    if (config['app']?['debug'] == true) {
      issues.add(ValidationIssue(
        type: ValidationIssueType.securityRisk,
        severity: ValidationSeverity.critical,
        file: filePath,
        description: '生产环境不应启用调试模式',
        recommendation: '设置 app.debug: false',
      ));
    }
    
    // 验证开发工具必须禁用
    final features = config['features'] as Map<String, dynamic>?;
    if (features != null) {
      if (features['dev_tools'] == true) {
        issues.add(ValidationIssue(
          type: ValidationIssueType.securityRisk,
          severity: ValidationSeverity.high,
          file: filePath,
          description: '生产环境不应启用开发工具',
          recommendation: '设置 features.dev_tools: false',
        ));
      }
      
      if (features['debug_features'] == true) {
        issues.add(ValidationIssue(
          type: ValidationIssueType.securityRisk,
          severity: ValidationSeverity.high,
          file: filePath,
          description: '生产环境不应启用调试功能',
          recommendation: '设置 features.debug_features: false',
        ));
      }
    }
    
    // 验证安全配置
    final security = config['security'] as Map<String, dynamic>?;
    if (security != null) {
      final requiredSecurityFeatures = [
        'encryption_enabled',
        'certificate_pinning',
        'root_detection',
        'debug_protection',
      ];
      
      for (final feature in requiredSecurityFeatures) {
        if (security[feature] != true) {
          issues.add(ValidationIssue(
            type: ValidationIssueType.securityRisk,
            severity: ValidationSeverity.high,
            file: filePath,
            description: '生产环境应启用安全功能: $feature',
            recommendation: '设置 security.$feature: true',
          ));
        }
      }
    }
  }
  
  /// 验证功能配置
  static Future<List<ValidationIssue>> _validateFeatureConfig(String environment) async {
    final issues = <ValidationIssue>[];
    
    // 这里可以添加功能配置的验证逻辑
    // 例如验证功能依赖关系、冲突检测等
    
    return issues;
  }
  
  /// 验证安全配置
  static Future<List<ValidationIssue>> _validateSecurityConfig(String environment) async {
    final issues = <ValidationIssue>[];
    
    // 检查签名配置
    if (environment == 'production') {
      final keyPropertiesFile = File('android/key.properties');
      if (!await keyPropertiesFile.exists()) {
        issues.add(ValidationIssue(
          type: ValidationIssueType.missingFile,
          severity: ValidationSeverity.high,
          file: keyPropertiesFile.path,
          description: '生产环境缺少Android签名配置',
          recommendation: '创建 android/key.properties 文件',
        ));
      }
    }
    
    return issues;
  }
  
  /// 验证依赖关系
  static Future<List<ValidationIssue>> _validateDependencies(String environment) async {
    final issues = <ValidationIssue>[];
    
    // 检查 pubspec.yaml
    final pubspecFile = File('pubspec.yaml');
    if (await pubspecFile.exists()) {
      final content = await pubspecFile.readAsString();
      final pubspec = loadYaml(content) as Map<String, dynamic>;
      
      // 验证必要的依赖
      final dependencies = pubspec['dependencies'] as Map<String, dynamic>?;
      if (dependencies != null) {
        final requiredDeps = [
          'flutter',
          'get_it',
          'dio',
          'hive',
        ];
        
        for (final dep in requiredDeps) {
          if (!dependencies.containsKey(dep)) {
            issues.add(ValidationIssue(
              type: ValidationIssueType.missingDependency,
              severity: ValidationSeverity.medium,
              file: pubspecFile.path,
              description: '缺少必要的依赖: $dep',
              recommendation: '添加 $dep 依赖',
            ));
          }
        }
      }
    }
    
    return issues;
  }
  
  /// 验证构建配置
  static Future<List<ValidationIssue>> _validateBuildConfig() async {
    final issues = <ValidationIssue>[];
    
    // 检查 Android 构建配置
    final androidBuildFile = File('android/app/build.gradle.kts');
    if (await androidBuildFile.exists()) {
      final content = await androidBuildFile.readAsString();
      
      // 检查是否配置了多环境
      if (!content.contains('productFlavors')) {
        issues.add(ValidationIssue(
          type: ValidationIssueType.missingConfig,
          severity: ValidationSeverity.medium,
          file: androidBuildFile.path,
          description: 'Android构建配置缺少多环境支持',
          recommendation: '添加 productFlavors 配置',
        ));
      }
    }
    
    return issues;
  }
  
  /// 生成验证报告
  static Future<void> _generateValidationReport(
    List<ValidationIssue> issues,
    String environment,
  ) async {
    final report = {
      'timestamp': DateTime.now().toIso8601String(),
      'environment': environment,
      'total_issues': issues.length,
      'critical_issues': issues.where((i) => i.severity == ValidationSeverity.critical).length,
      'high_issues': issues.where((i) => i.severity == ValidationSeverity.high).length,
      'medium_issues': issues.where((i) => i.severity == ValidationSeverity.medium).length,
      'low_issues': issues.where((i) => i.severity == ValidationSeverity.low).length,
      'issues': issues.map((i) => i.toJson()).toList(),
    };
    
    final reportFile = File('config_validation_report.json');
    await reportFile.writeAsString(jsonEncode(report));
    
    print('📊 配置验证报告已生成: ${reportFile.path}');
  }
  
  /// 评估验证结果
  static void _evaluateValidationResults(List<ValidationIssue> issues) {
    final criticalCount = issues.where((i) => i.severity == ValidationSeverity.critical).length;
    final highCount = issues.where((i) => i.severity == ValidationSeverity.high).length;
    final mediumCount = issues.where((i) => i.severity == ValidationSeverity.medium).length;
    final lowCount = issues.where((i) => i.severity == ValidationSeverity.low).length;
    
    print('\n📋 验证结果摘要:');
    print('  严重问题: $criticalCount');
    print('  高危问题: $highCount');
    print('  中危问题: $mediumCount');
    print('  低危问题: $lowCount');
    print('  总计问题: ${issues.length}');
    
    if (criticalCount > 0) {
      print('\n❌ 配置验证失败: 发现严重配置问题');
      print('请修复所有严重问题后重新运行验证');
      exit(1);
    } else if (highCount > 0) {
      print('\n⚠️  配置验证警告: 发现高危配置问题');
      print('建议修复高危问题以确保应用安全');
      exit(1);
    } else {
      print('\n✅ 配置验证通过');
    }
  }
}

/// 验证问题
class ValidationIssue {
  final ValidationIssueType type;
  final ValidationSeverity severity;
  final String file;
  final String description;
  final String recommendation;
  
  const ValidationIssue({
    required this.type,
    required this.severity,
    required this.file,
    required this.description,
    required this.recommendation,
  });
  
  Map<String, dynamic> toJson() => {
    'type': type.toString().split('.').last,
    'severity': severity.toString().split('.').last,
    'file': file,
    'description': description,
    'recommendation': recommendation,
  };
}

/// 验证问题类型
enum ValidationIssueType {
  missingFile,
  missingConfig,
  invalidFormat,
  securityRisk,
  missingDependency,
}

/// 验证严重程度
enum ValidationSeverity {
  low,
  medium,
  high,
  critical,
}

/// 主函数
void main(List<String> args) async {
  String? environment;
  
  // 解析命令行参数
  for (int i = 0; i < args.length; i++) {
    if (args[i] == '--environment' && i + 1 < args.length) {
      environment = args[i + 1];
      break;
    }
  }
  
  await ConfigValidator.runValidation(environment);
}
