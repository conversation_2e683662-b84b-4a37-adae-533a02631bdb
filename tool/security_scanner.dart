/// 安全扫描工具
/// 
/// 用于扫描代码中的安全漏洞和潜在风险
library;

import 'dart:io';
import 'dart:convert';

/// 安全扫描器
class SecurityScanner {
  static const String _outputFile = 'security_report.json';
  
  /// 运行安全扫描
  static Future<void> runSecurityScan() async {
    print('🔍 开始安全扫描...');
    
    final results = <SecurityIssue>[];
    
    try {
      // 1. 扫描硬编码敏感信息
      print('扫描硬编码敏感信息...');
      results.addAll(await _scanHardcodedSecrets());
      
      // 2. 扫描不安全的网络配置
      print('扫描网络安全配置...');
      results.addAll(await _scanNetworkSecurity());
      
      // 3. 扫描权限配置
      print('扫描权限配置...');
      results.addAll(await _scanPermissions());
      
      // 4. 扫描依赖漏洞
      print('扫描依赖漏洞...');
      results.addAll(await _scanDependencyVulnerabilities());
      
      // 5. 扫描代码质量问题
      print('扫描代码质量问题...');
      results.addAll(await _scanCodeQuality());
      
      // 6. 生成报告
      await _generateSecurityReport(results);
      
      // 7. 检查扫描结果
      _evaluateScanResults(results);
      
    } catch (e) {
      print('❌ 安全扫描过程中发生错误: $e');
      exit(1);
    }
  }
  
  /// 扫描硬编码敏感信息
  static Future<List<SecurityIssue>> _scanHardcodedSecrets() async {
    final issues = <SecurityIssue>[];
    
    // 定义敏感信息模式
    final patterns = [
      RegExp(r'password\s*[=:]\s*["\'][^"\']{3,}["\']', caseSensitive: false),
      RegExp(r'api[_-]?key\s*[=:]\s*["\'][^"\']{10,}["\']', caseSensitive: false),
      RegExp(r'secret\s*[=:]\s*["\'][^"\']{10,}["\']', caseSensitive: false),
      RegExp(r'token\s*[=:]\s*["\'][^"\']{10,}["\']', caseSensitive: false),
      RegExp(r'private[_-]?key\s*[=:]\s*["\'][^"\']{10,}["\']', caseSensitive: false),
      RegExp(r'access[_-]?key\s*[=:]\s*["\'][^"\']{10,}["\']', caseSensitive: false),
    ];
    
    // 扫描Dart文件
    await for (final file in Directory('lib').list(recursive: true)) {
      if (file is File && file.path.endsWith('.dart')) {
        final content = await file.readAsString();
        
        for (final pattern in patterns) {
          final matches = pattern.allMatches(content);
          for (final match in matches) {
            // 排除明显的示例代码
            final matchText = match.group(0)!.toLowerCase();
            if (_isExampleCode(matchText)) continue;
            
            issues.add(SecurityIssue(
              type: SecurityIssueType.hardcodedSecret,
              severity: SecuritySeverity.high,
              file: file.path,
              line: _getLineNumber(content, match.start),
              description: '发现潜在的硬编码敏感信息: ${match.group(0)}',
              recommendation: '将敏感信息移至环境变量或安全配置文件中',
            ));
          }
        }
      }
    }
    
    return issues;
  }
  
  /// 扫描网络安全配置
  static Future<List<SecurityIssue>> _scanNetworkSecurity() async {
    final issues = <SecurityIssue>[];
    
    // 检查HTTP使用
    final httpPattern = RegExp(r'http://[^\s"\']+');
    
    await for (final file in Directory('lib').list(recursive: true)) {
      if (file is File && file.path.endsWith('.dart')) {
        final content = await file.readAsString();
        final matches = httpPattern.allMatches(content);
        
        for (final match in matches) {
          final url = match.group(0)!;
          // 排除localhost和示例URL
          if (url.contains('localhost') || url.contains('example.com')) continue;
          
          issues.add(SecurityIssue(
            type: SecurityIssueType.insecureNetwork,
            severity: SecuritySeverity.medium,
            file: file.path,
            line: _getLineNumber(content, match.start),
            description: '发现不安全的HTTP URL: $url',
            recommendation: '使用HTTPS替代HTTP',
          ));
        }
      }
    }
    
    // 检查证书验证
    final certPattern = RegExp(r'badCertificateCallback.*return\s+true', multiLine: true);
    
    await for (final file in Directory('lib').list(recursive: true)) {
      if (file is File && file.path.endsWith('.dart')) {
        final content = await file.readAsString();
        final matches = certPattern.allMatches(content);
        
        for (final match in matches) {
          issues.add(SecurityIssue(
            type: SecurityIssueType.insecureNetwork,
            severity: SecuritySeverity.high,
            file: file.path,
            line: _getLineNumber(content, match.start),
            description: '发现禁用证书验证的代码',
            recommendation: '在生产环境中启用严格的证书验证',
          ));
        }
      }
    }
    
    return issues;
  }
  
  /// 扫描权限配置
  static Future<List<SecurityIssue>> _scanPermissions() async {
    final issues = <SecurityIssue>[];
    
    // 检查Android权限
    final androidManifest = File('android/app/src/main/AndroidManifest.xml');
    if (await androidManifest.exists()) {
      final content = await androidManifest.readAsString();
      
      // 检查危险权限
      final dangerousPermissions = [
        'android.permission.CAMERA',
        'android.permission.RECORD_AUDIO',
        'android.permission.ACCESS_FINE_LOCATION',
        'android.permission.READ_EXTERNAL_STORAGE',
        'android.permission.WRITE_EXTERNAL_STORAGE',
      ];
      
      for (final permission in dangerousPermissions) {
        if (content.contains(permission)) {
          issues.add(SecurityIssue(
            type: SecurityIssueType.excessivePermission,
            severity: SecuritySeverity.medium,
            file: androidManifest.path,
            line: _getLineNumber(content, content.indexOf(permission)),
            description: '使用了敏感权限: $permission',
            recommendation: '确保权限使用是必要的，并在代码中进行运行时权限检查',
          ));
        }
      }
    }
    
    // 检查iOS权限
    final iosInfoPlist = File('ios/Runner/Info.plist');
    if (await iosInfoPlist.exists()) {
      final content = await iosInfoPlist.readAsString();
      
      // 检查敏感权限
      final sensitiveKeys = [
        'NSCameraUsageDescription',
        'NSMicrophoneUsageDescription',
        'NSLocationWhenInUseUsageDescription',
        'NSLocationAlwaysUsageDescription',
        'NSPhotoLibraryUsageDescription',
      ];
      
      for (final key in sensitiveKeys) {
        if (content.contains(key)) {
          issues.add(SecurityIssue(
            type: SecurityIssueType.excessivePermission,
            severity: SecuritySeverity.low,
            file: iosInfoPlist.path,
            line: _getLineNumber(content, content.indexOf(key)),
            description: '使用了敏感权限: $key',
            recommendation: '确保权限描述清晰，并且权限使用是必要的',
          ));
        }
      }
    }
    
    return issues;
  }
  
  /// 扫描依赖漏洞
  static Future<List<SecurityIssue>> _scanDependencyVulnerabilities() async {
    final issues = <SecurityIssue>[];
    
    // 检查pubspec.yaml中的依赖
    final pubspecFile = File('pubspec.yaml');
    if (await pubspecFile.exists()) {
      final content = await pubspecFile.readAsString();
      
      // 检查已知有漏洞的包版本（示例）
      final vulnerablePackages = {
        'http': ['<0.13.0'],
        'dio': ['<4.0.0'],
      };
      
      for (final entry in vulnerablePackages.entries) {
        final packageName = entry.key;
        final vulnerableVersions = entry.value;
        
        final packagePattern = RegExp('$packageName:\\s*([^\\n]+)');
        final match = packagePattern.firstMatch(content);
        
        if (match != null) {
          final versionSpec = match.group(1)!.trim();
          
          // 简单的版本检查（实际应该使用更复杂的版本比较）
          for (final vulnerableVersion in vulnerableVersions) {
            if (versionSpec.contains(vulnerableVersion.replaceAll('<', '').replaceAll('>', ''))) {
              issues.add(SecurityIssue(
                type: SecurityIssueType.vulnerableDependency,
                severity: SecuritySeverity.medium,
                file: pubspecFile.path,
                line: _getLineNumber(content, match.start),
                description: '发现可能存在漏洞的依赖: $packageName $versionSpec',
                recommendation: '更新到最新的安全版本',
              ));
            }
          }
        }
      }
    }
    
    return issues;
  }
  
  /// 扫描代码质量问题
  static Future<List<SecurityIssue>> _scanCodeQuality() async {
    final issues = <SecurityIssue>[];
    
    // 检查调试代码
    final debugPatterns = [
      RegExp(r'print\s*\(', caseSensitive: false),
      RegExp(r'debugPrint\s*\(', caseSensitive: false),
      RegExp(r'console\.log\s*\(', caseSensitive: false),
    ];
    
    await for (final file in Directory('lib').list(recursive: true)) {
      if (file is File && file.path.endsWith('.dart')) {
        final content = await file.readAsString();
        
        for (final pattern in debugPatterns) {
          final matches = pattern.allMatches(content);
          for (final match in matches) {
            issues.add(SecurityIssue(
              type: SecurityIssueType.debugCode,
              severity: SecuritySeverity.low,
              file: file.path,
              line: _getLineNumber(content, match.start),
              description: '发现调试代码: ${match.group(0)}',
              recommendation: '在生产版本中移除调试代码',
            ));
          }
        }
      }
    }
    
    return issues;
  }
  
  /// 判断是否为示例代码
  static bool _isExampleCode(String text) {
    final exampleKeywords = [
      'example',
      'test',
      'demo',
      'sample',
      'placeholder',
      'your_',
      'replace_',
    ];
    
    return exampleKeywords.any((keyword) => text.contains(keyword));
  }
  
  /// 获取行号
  static int _getLineNumber(String content, int position) {
    return content.substring(0, position).split('\n').length;
  }
  
  /// 生成安全报告
  static Future<void> _generateSecurityReport(List<SecurityIssue> issues) async {
    final report = {
      'timestamp': DateTime.now().toIso8601String(),
      'total_issues': issues.length,
      'high_severity': issues.where((i) => i.severity == SecuritySeverity.high).length,
      'medium_severity': issues.where((i) => i.severity == SecuritySeverity.medium).length,
      'low_severity': issues.where((i) => i.severity == SecuritySeverity.low).length,
      'issues_by_type': _groupIssuesByType(issues),
      'issues': issues.map((i) => i.toJson()).toList(),
    };
    
    final reportFile = File(_outputFile);
    await reportFile.writeAsString(jsonEncode(report));
    
    print('📊 安全报告已生成: $_outputFile');
  }
  
  /// 按类型分组问题
  static Map<String, int> _groupIssuesByType(List<SecurityIssue> issues) {
    final grouped = <String, int>{};
    
    for (final issue in issues) {
      final type = issue.type.toString().split('.').last;
      grouped[type] = (grouped[type] ?? 0) + 1;
    }
    
    return grouped;
  }
  
  /// 评估扫描结果
  static void _evaluateScanResults(List<SecurityIssue> issues) {
    final highSeverityCount = issues.where((i) => i.severity == SecuritySeverity.high).length;
    final mediumSeverityCount = issues.where((i) => i.severity == SecuritySeverity.medium).length;
    final lowSeverityCount = issues.where((i) => i.severity == SecuritySeverity.low).length;
    
    print('\n📋 扫描结果摘要:');
    print('  高危问题: $highSeverityCount');
    print('  中危问题: $mediumSeverityCount');
    print('  低危问题: $lowSeverityCount');
    print('  总计问题: ${issues.length}');
    
    if (highSeverityCount > 0) {
      print('\n❌ 安全扫描失败: 发现高危安全问题');
      print('请修复所有高危问题后重新运行扫描');
      exit(1);
    } else if (mediumSeverityCount > 5) {
      print('\n⚠️  安全扫描警告: 发现较多中危问题');
      print('建议修复中危问题以提高安全性');
      exit(1);
    } else {
      print('\n✅ 安全扫描通过');
    }
  }
}

/// 安全问题
class SecurityIssue {
  final SecurityIssueType type;
  final SecuritySeverity severity;
  final String file;
  final int line;
  final String description;
  final String recommendation;
  
  const SecurityIssue({
    required this.type,
    required this.severity,
    required this.file,
    required this.line,
    required this.description,
    required this.recommendation,
  });
  
  Map<String, dynamic> toJson() => {
    'type': type.toString().split('.').last,
    'severity': severity.toString().split('.').last,
    'file': file,
    'line': line,
    'description': description,
    'recommendation': recommendation,
  };
}

/// 安全问题类型
enum SecurityIssueType {
  hardcodedSecret,
  insecureNetwork,
  excessivePermission,
  vulnerableDependency,
  debugCode,
}

/// 安全严重程度
enum SecuritySeverity {
  low,
  medium,
  high,
  critical,
}

/// 主函数
void main(List<String> args) async {
  await SecurityScanner.runSecurityScan();
}
