# 开发者文档

## 项目概述

本项目是一个基于 Flutter 的企业级应用模板，采用模块化架构设计，支持功能的灵活配置和扩展。项目遵循 Clean Architecture 原则，提供了完整的开发、测试、部署解决方案。

### 技术栈

**核心框架**
- Flutter 3.16.0+
- Dart 3.2.0+

**状态管理**
- GetX 4.6.5+
- BLoC 8.1.0+

**网络通信**
- Dio 5.3.0+
- Retrofit 4.0.0+

**本地存储**
- Hive 2.2.3+
- SQLite 3.0+
- Shared Preferences

**依赖注入**
- GetIt 7.6.0+
- Injectable 2.1.2+

**其他重要依赖**
- Firebase (推送通知、分析)
- Flutter Localizations (国际化)
- Flutter Cache Manager (缓存管理)

### 架构设计

#### Clean Architecture 三层架构

项目采用 Clean Architecture 三层架构：

**Presentation Layer (表现层)**
- UI 组件、页面、状态管理
- 负责用户界面和用户交互
- 使用 BLoC 模式管理状态

**Domain Layer (领域层)**
- 业务逻辑、用例、实体
- 包含核心业务规则
- 独立于外部框架

**Data Layer (数据层)**
- 数据源、仓库实现、模型
- 处理数据获取和存储
- 支持本地和远程数据源

#### 模块化架构

基于配置驱动的模块化架构，支持：
- 功能模块的独立开发
- 运行时功能开关
- 零侵入式集成
- 构建时优化

### 项目结构

```
lib/
├── core/                   # 核心基础设施
│   ├── config/            # 配置管理
│   │   ├── app_config.dart
│   │   ├── environment_config.dart
│   │   ├── feature_config.dart
│   │   └── production_config.dart
│   ├── di/                # 依赖注入
│   │   ├── injection.dart
│   │   └── modules/
│   ├── network/           # 网络层
│   │   ├── api_client.dart
│   │   ├── interceptors/
│   │   └── models/
│   ├── database/          # 数据库
│   │   ├── database_service.dart
│   │   └── entities/
│   ├── security/          # 安全模块
│   │   ├── data_protection.dart
│   │   └── encryption/
│   ├── performance/       # 性能优化
│   │   ├── app_startup_optimizer.dart
│   │   ├── memory_optimizer.dart
│   │   └── network_optimizer.dart
│   ├── logging/           # 日志系统
│   │   └── logger.dart
│   └── utils/             # 工具类
├── features/              # 功能模块
│   ├── auth/              # 认证模块
│   │   ├── data/
│   │   ├── domain/
│   │   └── presentation/
│   ├── home/              # 主页模块
│   └── profile/           # 个人资料模块
├── shared/                # 共享组件
│   ├── widgets/           # 通用组件
│   ├── themes/            # 主题配置
│   └── constants/         # 常量定义
└── main.dart              # 应用入口
```

### 开发指南

#### 环境搭建

**1. 安装 Flutter SDK**
```bash
# 下载并安装 Flutter 3.16.0+
flutter --version
```

**2. 克隆项目代码**
```bash
git clone <repository-url>
cd flutter_enterprise_app
```

**3. 安装依赖**
```bash
flutter pub get
```

**4. 代码生成**
```bash
dart run build_runner build --delete-conflicting-outputs
```

**5. 配置开发环境**
```bash
# 复制配置模板
cp assets/config/app_config.yaml assets/config/app_config.dev.yaml
# 编辑开发环境配置
```

#### 代码规范

**命名规范**
- 文件名：`snake_case.dart`
- 类名：`PascalCase`
- 变量/方法：`camelCase`
- 常量：`UPPER_SNAKE_CASE`
- 私有成员：以 `_` 开头

**代码风格**
- 遵循 Dart 官方代码规范
- 使用 `dart format` 格式化代码
- 使用 `dart analyze` 检查代码质量
- 编写有意义的注释

**示例代码**
```dart
/// 用户服务接口
abstract class IUserService {
  /// 获取用户信息
  Future<User> getUserInfo(String userId);
  
  /// 更新用户信息
  Future<void> updateUserInfo(User user);
}

/// 用户服务实现
class UserService implements IUserService {
  final IUserRepository _userRepository;
  
  const UserService(this._userRepository);
  
  @override
  Future<User> getUserInfo(String userId) async {
    try {
      return await _userRepository.getUserById(userId);
    } catch (e) {
      Logger.e('获取用户信息失败: $e');
      rethrow;
    }
  }
  
  @override
  Future<void> updateUserInfo(User user) async {
    try {
      await _userRepository.updateUser(user);
      Logger.i('用户信息更新成功: ${user.id}');
    } catch (e) {
      Logger.e('更新用户信息失败: $e');
      rethrow;
    }
  }
}
```

#### 功能开发流程

**1. 创建功能分支**
```bash
git checkout -b feature/new-feature
```

**2. 实现功能代码**
- 按照 Clean Architecture 创建层级结构
- 实现数据层、领域层、表现层
- 添加依赖注入配置

**3. 编写测试用例**
```bash
# 单元测试
flutter test test/unit/

# 集成测试
flutter test integration_test/
```

**4. 更新配置文件**
- 在 `app_config.yaml` 中添加功能配置
- 更新功能常量定义
- 配置条件依赖注入

**5. 提交代码审查**
```bash
git add .
git commit -m "feat: add new feature"
git push origin feature/new-feature
```

### API 文档

#### 认证 API

**登录**
```dart
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}

// 响应
{
  "success": true,
  "data": {
    "token": "jwt_token_here",
    "user": {
      "id": "user_id",
      "email": "<EMAIL>",
      "name": "User Name"
    }
  }
}
```

**注册**
```dart
POST /api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "name": "User Name"
}

// 响应
{
  "success": true,
  "message": "注册成功"
}
```

**刷新令牌**
```dart
POST /api/auth/refresh
Authorization: Bearer <refresh_token>

// 响应
{
  "success": true,
  "data": {
    "token": "new_jwt_token",
    "refresh_token": "new_refresh_token"
  }
}
```

#### 用户 API

**获取用户信息**
```dart
GET /api/user/profile
Authorization: Bearer <token>

// 响应
{
  "success": true,
  "data": {
    "id": "user_id",
    "email": "<EMAIL>",
    "name": "User Name",
    "avatar": "avatar_url",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

**更新用户信息**
```dart
PUT /api/user/profile
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "New Name",
  "avatar": "new_avatar_url"
}

// 响应
{
  "success": true,
  "message": "更新成功"
}
```

### 测试指南

#### 单元测试

**测试结构**
```
test/
├── unit/                  # 单元测试
│   ├── core/
│   ├── features/
│   └── shared/
├── widget/                # Widget测试
└── integration/           # 集成测试
```

**测试示例**
```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

void main() {
  group('UserService Tests', () {
    late UserService userService;
    late MockUserRepository mockRepository;
    
    setUp(() {
      mockRepository = MockUserRepository();
      userService = UserService(mockRepository);
    });
    
    test('should return user when getUserInfo is called', () async {
      // Arrange
      const userId = 'test_user_id';
      final expectedUser = User(id: userId, name: 'Test User');
      when(mockRepository.getUserById(userId))
          .thenAnswer((_) async => expectedUser);
      
      // Act
      final result = await userService.getUserInfo(userId);
      
      // Assert
      expect(result, equals(expectedUser));
      verify(mockRepository.getUserById(userId)).called(1);
    });
  });
}
```

#### 集成测试

**测试示例**
```dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:enterprise_flutter/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  
  group('App Integration Tests', () {
    testWidgets('login flow test', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();
      
      // 测试登录流程
      await tester.enterText(
        find.byKey(const Key('email_field')), 
        '<EMAIL>'
      );
      await tester.enterText(
        find.byKey(const Key('password_field')), 
        'password123'
      );
      await tester.tap(find.byKey(const Key('login_button')));
      await tester.pumpAndSettle();
      
      // 验证登录成功
      expect(find.byType(HomePage), findsOneWidget);
    });
  });
}
```

### 部署指南

#### 开发环境

**运行开发版本**
```bash
# 运行开发环境
flutter run --flavor development --dart-define=ENVIRONMENT=development

# 热重载开发
flutter run --hot
```

#### 测试环境

**构建测试版本**
```bash
# Android
flutter build apk --flavor staging --dart-define=ENVIRONMENT=staging

# iOS
flutter build ios --flavor staging --dart-define=ENVIRONMENT=staging
```

#### 生产环境

**Android 生产构建**
```bash
# 构建 APK
flutter build apk --release --flavor production --dart-define=ENVIRONMENT=production

# 构建 App Bundle (推荐)
flutter build appbundle --release --flavor production --dart-define=ENVIRONMENT=production
```

**iOS 生产构建**
```bash
# 构建 iOS
flutter build ios --release --flavor production --dart-define=ENVIRONMENT=production

# 在 Xcode 中进行签名和导出
open ios/Runner.xcworkspace
```

**使用构建脚本**
```bash
# 使用自动化构建脚本
./scripts/build_production.sh

# 设置签名配置
./scripts/setup_signing.sh
```

### 配置管理

#### 环境配置

**开发环境配置**
```yaml
# assets/config/app_config.dev.yaml
app:
  name: "Flutter Enterprise App (Dev)"
  debug: true

api:
  base_url: "https://api.dev.company.com"
  timeout: 10000
  enable_logging: true

features:
  dev_tools: true
  debug_features: true
  analytics: false
```

**生产环境配置**
```yaml
# assets/config/app_config.prod.yaml
app:
  name: "Enterprise Flutter App"
  debug: false

api:
  base_url: "https://api.production.company.com"
  timeout: 30000
  enable_logging: false

security:
  encryption_enabled: true
  certificate_pinning: true

features:
  dev_tools: false
  debug_features: false
  analytics: true
```

#### 功能配置

**启用/禁用功能**
```yaml
features:
  authentication: true      # 认证功能
  analytics: true          # 分析统计
  push_notifications: true # 推送通知
  offline_support: true    # 离线支持
  dev_tools: false        # 开发工具
```

### 性能优化

#### 启动优化

**使用启动优化器**
```dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 启动优化
  await AppStartupOptimizer.optimizeStartup();

  runApp(MyApp());
}
```

#### 内存优化

**启用内存监控**
```dart
void initializeApp() {
  // 启动内存监控
  MemoryOptimizer.startMemoryMonitoring();

  // 应用退出时停止监控
  WidgetsBinding.instance.addObserver(AppLifecycleObserver());
}
```

#### 网络优化

**配置网络优化**
```dart
final dio = Dio();
NetworkOptimizer.configureOptimizedDio(dio);
```

### 安全指南

#### 数据保护

**启用数据保护**
```dart
void initializeSecurity() async {
  await DataProtection.enableDataProtection();
}
```

#### 安全扫描

**运行安全扫描**
```bash
dart run tool/security_scanner.dart
```

### 故障排除

#### 常见问题

**1. 构建失败**
- 检查 Flutter 版本：`flutter --version`
- 清理构建缓存：`flutter clean`
- 重新获取依赖：`flutter pub get`
- 重新生成代码：`dart run build_runner build`

**2. 代码生成失败**
```bash
# 清理生成的代码
dart run build_runner clean

# 重新生成
dart run build_runner build --delete-conflicting-outputs
```

**3. 依赖冲突**
```bash
# 查看依赖树
flutter pub deps

# 解决冲突
flutter pub upgrade
```

**4. 测试失败**
- 检查测试环境配置
- 更新测试数据
- 检查模拟服务状态

---

**文档版本**: 1.0.0
**更新日期**: 2024年
**维护者**: 开发团队
