# Android 运行指南

## 环境要求

### 必需软件
- **Flutter SDK 3.16.0+**
- **Dart SDK 3.2.0+**
- **Android Studio** 或 **VS Code**
- **Android SDK** (API 21+)
- **Java JDK 11+**

### 推荐配置
- **RAM**: 8GB 或更多
- **存储**: 至少 10GB 可用空间
- **Android 设备**: Android 5.0+ 或模拟器

## 快速开始

### 1. 环境检查

```bash
# 检查 Flutter 环境
flutter doctor

# 检查 Flutter 版本
flutter --version

# 检查连接的设备
flutter devices
```

### 2. 项目设置

```bash
# 克隆项目
git clone <repository-url>
cd flutter_template

# 获取依赖
flutter pub get

# 代码生成（如果需要）
dart run build_runner build --delete-conflicting-outputs
```

### 3. 运行应用

#### 自动化运行脚本（推荐）
```bash
# 使用自动化脚本，会检查环境并提供选择
./scripts/run_android.sh
```

#### 手动运行步骤

**步骤1：检查环境**
```bash
# 检查Flutter环境
flutter doctor

# 检查连接的设备
flutter devices
```

**步骤2：获取依赖**
```bash
# 获取项目依赖
flutter pub get
```

**步骤3：选择运行方式**

#### 方式一：简化版本（推荐首次运行）
```bash
# 运行简化版本，确保基本功能正常
flutter run lib/main_simple.dart
```

#### 方式二：完整版本
```bash
# 运行完整版本（需要所有依赖正确配置）
flutter run lib/main.dart

# 或指定环境
flutter run --dart-define=ENVIRONMENT=dev
```

#### 方式三：Web版本
```bash
# 在浏览器中运行
flutter run -d chrome
```

#### 方式四：构建APK
```bash
# 构建调试版APK
flutter build apk --debug

# 构建发布版APK
flutter build apk --release

# 安装到设备
adb install build/app/outputs/flutter-apk/app-debug.apk
```

## 故障排除

### 常见问题

#### 1. Flutter 未安装或不在 PATH 中

**症状**: `bash: flutter: command not found`

**解决方案**:
1. **macOS 用户**:
   ```bash
   # 使用 Homebrew 安装
   brew install --cask flutter

   # 或手动安装
   # 下载 Flutter SDK: https://flutter.dev/docs/get-started/install/macos
   # 解压到 /Users/<USER>/flutter
   # 添加到 PATH: export PATH="$PATH:/Users/<USER>/flutter/bin"
   ```

2. **Linux 用户**:
   ```bash
   # 使用 snap 安装
   sudo snap install flutter --classic

   # 或手动安装
   # 下载 Flutter SDK: https://flutter.dev/docs/get-started/install/linux
   ```

3. **Windows 用户**:
   - 下载 Flutter SDK: https://flutter.dev/docs/get-started/install/windows
   - 解压到 C:\flutter
   - 添加 C:\flutter\bin 到系统 PATH

4. **验证安装**:
   ```bash
   flutter doctor
   flutter --version
   ```

#### 2. Android Gradle 构建错误

**症状**: `Build failed with an exception` 或 Kotlin 语法错误

**解决方案**:
1. **检查 build.gradle.kts 语法**:
   ```bash
   # 项目已修复了 Kotlin DSL 语法问题
   # 如果仍有问题，可以使用简化版本
   ```

2. **清理项目**:
   ```bash
   flutter clean
   flutter pub get
   ```

3. **重新构建**:
   ```bash
   flutter run
   ```

#### 3. Android SDK 未配置

**症状**: `Android SDK not found`

**解决方案**:
1. 安装 Android Studio
2. 通过 Android Studio 安装 Android SDK
3. 设置 ANDROID_HOME 环境变量:
   ```bash
   # macOS/Linux
   export ANDROID_HOME=$HOME/Library/Android/sdk
   export PATH=$PATH:$ANDROID_HOME/tools:$ANDROID_HOME/platform-tools

   # Windows
   set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
   set PATH=%PATH%;%ANDROID_HOME%\tools;%ANDROID_HOME%\platform-tools
   ```
4. 运行 `flutter doctor --android-licenses`

#### 3. 依赖冲突

**症状**: 构建失败，依赖版本冲突

**解决方案**:
```bash
# 清理项目
flutter clean

# 重新获取依赖
flutter pub get

# 如果仍有问题，删除 pubspec.lock 后重试
rm pubspec.lock
flutter pub get
```

#### 4. 代码生成失败

**症状**: `build_runner` 执行失败

**解决方案**:
```bash
# 清理生成的代码
dart run build_runner clean

# 重新生成
dart run build_runner build --delete-conflicting-outputs
```

#### 5. 设备连接问题

**症状**: `No devices found`

**解决方案**:
1. 确保 USB 调试已启用
2. 检查 USB 连接
3. 重启 ADB: `adb kill-server && adb start-server`
4. 使用模拟器: `flutter emulators --launch <emulator_id>`

### 性能优化

#### 1. 启用 R8 代码压缩
在 `android/app/build.gradle.kts` 中：
```kotlin
buildTypes {
    release {
        minifyEnabled = true
        shrinkResources = true
        proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
    }
}
```

#### 2. 启用多 dex 支持
```kotlin
defaultConfig {
    multiDexEnabled = true
}
```

#### 3. 优化构建时间
```bash
# 使用并行构建
flutter build apk --release --split-per-abi

# 只构建特定架构
flutter build apk --release --target-platform android-arm64
```

## 项目结构说明

### 核心文件
- `lib/main.dart` - 完整版应用入口
- `lib/main_simple.dart` - 简化版应用入口
- `lib/core/` - 核心基础设施
- `lib/features/` - 功能模块
- `lib/shared/` - 共享组件

### 配置文件
- `assets/config/app_config.yaml` - 基础配置
- `assets/config/app_config.dev.yaml` - 开发环境配置
- `assets/config/app_config.prod.yaml` - 生产环境配置

### Android 配置
- `android/app/build.gradle.kts` - 构建配置
- `android/app/src/main/AndroidManifest.xml` - 应用清单
- `android/app/proguard-rules.pro` - 代码混淆规则

## 开发建议

### 1. 使用简化版本开始
如果遇到复杂的依赖问题，建议先使用 `main_simple.dart` 确保基本功能正常。

### 2. 渐进式启用功能
通过修改配置文件逐步启用更多功能：
```yaml
# assets/config/app_config.yaml
features:
  authentication: true
  analytics: false  # 先禁用复杂功能
  push_notifications: false
```

### 3. 调试技巧
```bash
# 查看详细日志
flutter run --verbose

# 热重载
r

# 热重启
R

# 退出
q
```

### 4. 性能分析
```bash
# 性能分析
flutter run --profile

# 构建分析
flutter build apk --analyze-size
```

## 部署指南

### 1. 生成签名密钥
```bash
keytool -genkey -v -keystore android/keystore/release.keystore \
        -alias release -keyalg RSA -keysize 2048 -validity 10000
```

### 2. 配置签名
复制 `android/key.properties.template` 为 `android/key.properties` 并填入信息。

### 3. 构建发布版本
```bash
# 构建 APK
flutter build apk --release --flavor production

# 构建 App Bundle
flutter build appbundle --release --flavor production
```

## 支持与帮助

### 官方资源
- [Flutter 官方文档](https://flutter.dev/docs)
- [Dart 语言指南](https://dart.dev/guides)
- [Android 开发者文档](https://developer.android.com)

### 社区资源
- [Flutter 中文网](https://flutter.cn)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/flutter)
- [GitHub Issues](https://github.com/flutter/flutter/issues)

### 项目相关
- 查看 `docs/developer_guide.md` 了解详细开发指南
- 查看 `docs/user_manual.md` 了解用户使用说明
- 遇到问题请查看项目的 Issues 或创建新的 Issue

---

**注意**: 如果在运行过程中遇到任何问题，请先尝试使用 `lib/main_simple.dart` 版本，这个版本使用了简化的依赖配置，更容易成功运行。
