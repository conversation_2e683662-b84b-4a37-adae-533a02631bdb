name: flutter_enterprise_app
description: 企业级Flutter应用统一架构方案
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: '>=3.10.0'

dependencies:
  flutter:
    sdk: flutter

  # 依赖注入
  get_it: ^7.6.4

  # 网络请求
  dio: ^5.3.2

  # 数据持久化
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  flutter_secure_storage: ^9.0.0

  # 网络状态
  connectivity_plus: ^4.0.2

  # 日志
  logger: ^2.0.2+1

  # 配置管理
  yaml: ^3.1.2

  # UI组件
  cupertino_icons: ^1.0.8

dev_dependencies:
  flutter_test:
    sdk: flutter

  # 代码质量
  flutter_lints: ^3.0.1

flutter:
  uses-material-design: true

  # 资源文件
  # assets:
  #   - assets/config/
  #   - assets/images/
  #   - assets/translations/

  # 字体配置（暂时注释掉，等添加字体文件后再启用）
  # fonts:
  #   - family: Roboto
  #     fonts:
  #       - asset: assets/fonts/Roboto-Regular.ttf
  #       - asset: assets/fonts/Roboto-Bold.ttf
  #         weight: 700
