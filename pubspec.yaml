name: flutter_enterprise_app
description: 企业级Flutter应用统一架构方案
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: '>=3.10.0'

dependencies:
  flutter:
    sdk: flutter

  # 状态管理
  flutter_bloc: ^8.1.3

  # 依赖注入
  get_it: ^7.6.4
  injectable: ^2.3.2

  # 网络请求
  dio: ^5.3.2
  retrofit: ^4.0.3
  connectivity_plus: ^4.0.2
  pretty_dio_logger: ^1.3.1

  # 数据持久化
  drift: ^2.13.2
  sqlite3_flutter_libs: ^0.5.15
  path_provider: ^2.1.1
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  flutter_secure_storage: ^9.0.0

  # 路由导航
  go_router: ^12.1.1

  # 配置管理
  yaml: ^3.1.2

  # 工具类
  equatable: ^2.0.5
  dartz: ^0.10.1
  logger: ^2.0.2+1
  json_annotation: ^4.8.1

  # UI组件
  cupertino_icons: ^1.0.8

  # 国际化
  flutter_localizations:
    sdk: flutter
  intl: ^0.19.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # 代码生成
  build_runner: ^2.4.7
  injectable_generator: ^2.4.1
  retrofit_generator: ^8.0.4
  drift_dev: ^2.13.2
  hive_generator: ^2.0.1
  json_annotation: ^4.8.1
  json_serializable: ^6.7.1

  # 测试工具
  bloc_test: ^9.1.5
  mocktail: ^1.0.1
  mockito: ^5.4.2

  # 代码质量
  very_good_analysis: ^5.1.0

flutter:
  uses-material-design: true

  # 资源文件
  assets:
    - assets/config/
    - assets/images/
    - assets/translations/
