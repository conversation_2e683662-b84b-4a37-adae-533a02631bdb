# Android 签名配置模板
# 复制此文件为 key.properties 并填入实际的签名信息
# 注意：key.properties 文件应该被添加到 .gitignore 中，不要提交到版本控制

# 密钥库文件路径（相对于android目录）
storeFile=../keystore/release.keystore

# 密钥库密码
storePassword=your_store_password_here

# 密钥别名
keyAlias=your_key_alias_here

# 密钥密码
keyPassword=your_key_password_here

# 生产环境签名配置说明：
# 1. 生成密钥库：
#    keytool -genkey -v -keystore release.keystore -alias your_key_alias -keyalg RSA -keysize 2048 -validity 10000
# 
# 2. 将密钥库文件放在 android/keystore/ 目录下
# 
# 3. 复制此模板文件为 key.properties 并填入实际信息
# 
# 4. 确保 key.properties 和 keystore/ 目录被添加到 .gitignore
# 
# 5. 构建生产版本：
#    flutter build apk --release --flavor production
#    flutter build appbundle --release --flavor production
