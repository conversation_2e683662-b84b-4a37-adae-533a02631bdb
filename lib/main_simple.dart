import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'core/di/simple_injection.dart';
import 'core/config/feature_config.dart';

/// 简化的应用程序入口点
///
/// 使用简化的依赖注入配置，确保应用能够正常启动
void main() async {
  // 确保Flutter绑定初始化
  WidgetsFlutterBinding.ensureInitialized();

  // 确定运行环境
  const environment = String.fromEnvironment('ENVIRONMENT', defaultValue: 'dev');

  try {
    // 初始化简化的依赖注入
    await configureSimpleDependencies(environment: environment);

    if (kDebugMode) {
      print('🚀 Flutter企业级应用启动成功');
      print('📦 环境: $environment');
      if (isRegistered<FeatureConfig>()) {
        print('🔧 已启用功能: ${get<FeatureConfig>().getEnabledFeatures()}');
      }
    }
  } catch (e, stackTrace) {
    if (kDebugMode) {
      print('❌ 应用初始化失败: $e');
      print('📍 堆栈跟踪: $stackTrace');
    }
    // 即使初始化失败也要启动应用，避免白屏
  }

  runApp(const FlutterEnterpriseApp());
}

/// Flutter企业级应用主类
class FlutterEnterpriseApp extends StatelessWidget {
  const FlutterEnterpriseApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Flutter企业级应用',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
      ),
      home: const HomePage(),
      debugShowCheckedModeBanner: false,
    );
  }
}

/// 主页
class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Flutter企业级应用'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: const Padding(
        padding: EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _WelcomeSection(),
              SizedBox(height: 24),
              _ArchitectureSection(),
              SizedBox(height: 24),
              _FeatureSection(),
              SizedBox(height: 24),
              _StatusSection(),
            ],
          ),
        ),
      ),
    );
  }
}

/// 欢迎部分
class _WelcomeSection extends StatelessWidget {
  const _WelcomeSection();

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.rocket_launch,
                  color: Theme.of(context).primaryColor,
                  size: 32,
                ),
                const SizedBox(width: 12),
                Text(
                  '欢迎使用Flutter企业级应用',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              '这是一个基于Clean Architecture的企业级Flutter应用模板，'
              '采用模块化设计，支持功能的灵活配置和扩展。',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }
}

/// 架构部分
class _ArchitectureSection extends StatelessWidget {
  const _ArchitectureSection();

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '🏗️ 架构特性',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            const _FeatureItem(
              icon: Icons.layers,
              title: 'Clean Architecture',
              description: '三层架构：表现层、领域层、数据层',
            ),
            const _FeatureItem(
              icon: Icons.extension,
              title: '模块化设计',
              description: '功能模块独立开发，支持按需加载',
            ),
            const _FeatureItem(
              icon: Icons.settings,
              title: '配置驱动',
              description: '基于配置文件的功能开关和环境管理',
            ),
            const _FeatureItem(
              icon: Icons.security,
              title: '企业级安全',
              description: '多层安全防护和数据保护',
            ),
          ],
        ),
      ),
    );
  }
}

/// 功能部分
class _FeatureSection extends StatelessWidget {
  const _FeatureSection();

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '⚡ 核心功能',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            const _FeatureItem(
              icon: Icons.login,
              title: '用户认证',
              description: '完整的登录、注册、密码重置流程',
            ),
            const _FeatureItem(
              icon: Icons.network_check,
              title: '网络通信',
              description: '统一的网络请求处理和缓存机制',
            ),
            const _FeatureItem(
              icon: Icons.storage,
              title: '数据持久化',
              description: '支持本地和远程数据的双向同步',
            ),
            const _FeatureItem(
              icon: Icons.palette,
              title: 'UI/UX',
              description: '多主题支持和国际化',
            ),
          ],
        ),
      ),
    );
  }
}

/// 状态部分
class _StatusSection extends StatelessWidget {
  const _StatusSection();

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '📊 应用状态',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _StatusItem(
              label: '环境',
              value: const String.fromEnvironment('ENVIRONMENT', defaultValue: 'dev'),
              color: Colors.blue,
            ),
            _StatusItem(
              label: '版本',
              value: '1.0.0',
              color: Colors.green,
            ),
            _StatusItem(
              label: '状态',
              value: '运行中',
              color: Colors.orange,
            ),
          ],
        ),
      ),
    );
  }
}

/// 功能项组件
class _FeatureItem extends StatelessWidget {
  const _FeatureItem({
    required this.icon,
    required this.title,
    required this.description,
  });

  final IconData icon;
  final String title;
  final String description;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: Theme.of(context).primaryColor,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// 状态项组件
class _StatusItem extends StatelessWidget {
  const _StatusItem({
    required this.label,
    required this.value,
    required this.color,
  });

  final String label;
  final String value;
  final Color color;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: color.withOpacity(0.3)),
            ),
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
