import 'package:equatable/equatable.dart';
import 'encryption_key.dart';

/// 安全级别
enum SecurityLevel {
  /// 低级别
  low,
  
  /// 中级别
  medium,
  
  /// 高级别
  high,
  
  /// 极高级别
  critical,
}

/// 认证方式
enum AuthenticationMethod {
  /// 密码
  password,
  
  /// PIN码
  pin,
  
  /// 生物识别
  biometric,
  
  /// 双因素认证
  twoFactor,
  
  /// 多因素认证
  multiFactor,
  
  /// 证书认证
  certificate,
  
  /// 令牌认证
  token,
}

/// 数据分类
enum DataClassification {
  /// 公开
  public,
  
  /// 内部
  internal,
  
  /// 机密
  confidential,
  
  /// 绝密
  secret,
  
  /// 顶级机密
  topSecret,
}

/// 安全配置实体
/// 
/// **功能依赖**: 需要启用 security 模块
/// **配置项**: FEATURE_SECURITY
class SecurityConfig extends Equatable {
  /// 安全级别
  final SecurityLevel securityLevel;
  
  /// 是否启用加密
  final bool encryptionEnabled;
  
  /// 默认加密算法
  final EncryptionAlgorithm defaultEncryptionAlgorithm;
  
  /// 是否启用传输加密
  final bool transportEncryptionEnabled;
  
  /// 是否启用静态数据加密
  final bool dataAtRestEncryptionEnabled;
  
  /// 是否启用端到端加密
  final bool endToEndEncryptionEnabled;
  
  /// 认证方式
  final List<AuthenticationMethod> authenticationMethods;
  
  /// 是否启用生物识别
  final bool biometricEnabled;
  
  /// 是否启用双因素认证
  final bool twoFactorEnabled;
  
  /// 密码策略
  final PasswordPolicy passwordPolicy;
  
  /// 会话超时时间（秒）
  final int sessionTimeout;
  
  /// 最大登录尝试次数
  final int maxLoginAttempts;
  
  /// 账户锁定时间（秒）
  final int accountLockoutDuration;
  
  /// 是否启用审计日志
  final bool auditLoggingEnabled;
  
  /// 审计日志级别
  final String auditLogLevel;
  
  /// 是否启用入侵检测
  final bool intrusionDetectionEnabled;
  
  /// 是否启用防篡改
  final bool tamperProtectionEnabled;
  
  /// 是否启用证书固定
  final bool certificatePinningEnabled;
  
  /// 信任的证书指纹
  final List<String> trustedCertificateFingerprints;
  
  /// 是否启用网络安全
  final bool networkSecurityEnabled;
  
  /// 允许的网络协议
  final List<String> allowedNetworkProtocols;
  
  /// 是否启用代码混淆
  final bool codeObfuscationEnabled;
  
  /// 是否启用根检测
  final bool rootDetectionEnabled;
  
  /// 是否启用调试检测
  final bool debugDetectionEnabled;
  
  /// 是否启用模拟器检测
  final bool emulatorDetectionEnabled;
  
  /// 是否启用屏幕录制保护
  final bool screenRecordingProtectionEnabled;
  
  /// 是否启用截屏保护
  final bool screenshotProtectionEnabled;
  
  /// 数据分类策略
  final Map<String, DataClassification> dataClassificationPolicy;
  
  /// 密钥轮换间隔（天）
  final int keyRotationInterval;
  
  /// 是否启用密钥托管
  final bool keyEscrowEnabled;
  
  /// 备份加密密钥
  final List<String> backupEncryptionKeys;
  
  /// 安全策略版本
  final String policyVersion;
  
  /// 合规要求
  final List<String> complianceRequirements;
  
  /// 自定义安全配置
  final Map<String, dynamic> customSecurityConfig;

  const SecurityConfig({
    this.securityLevel = SecurityLevel.medium,
    this.encryptionEnabled = true,
    this.defaultEncryptionAlgorithm = EncryptionAlgorithm.aes256gcm,
    this.transportEncryptionEnabled = true,
    this.dataAtRestEncryptionEnabled = true,
    this.endToEndEncryptionEnabled = false,
    this.authenticationMethods = const [AuthenticationMethod.password],
    this.biometricEnabled = false,
    this.twoFactorEnabled = false,
    this.passwordPolicy = const PasswordPolicy(),
    this.sessionTimeout = 3600, // 1小时
    this.maxLoginAttempts = 5,
    this.accountLockoutDuration = 900, // 15分钟
    this.auditLoggingEnabled = true,
    this.auditLogLevel = 'INFO',
    this.intrusionDetectionEnabled = false,
    this.tamperProtectionEnabled = false,
    this.certificatePinningEnabled = false,
    this.trustedCertificateFingerprints = const [],
    this.networkSecurityEnabled = true,
    this.allowedNetworkProtocols = const ['HTTPS', 'WSS'],
    this.codeObfuscationEnabled = false,
    this.rootDetectionEnabled = false,
    this.debugDetectionEnabled = false,
    this.emulatorDetectionEnabled = false,
    this.screenRecordingProtectionEnabled = false,
    this.screenshotProtectionEnabled = false,
    this.dataClassificationPolicy = const {},
    this.keyRotationInterval = 90, // 90天
    this.keyEscrowEnabled = false,
    this.backupEncryptionKeys = const [],
    this.policyVersion = '1.0',
    this.complianceRequirements = const [],
    this.customSecurityConfig = const {},
  });

  /// 创建低安全级别配置
  factory SecurityConfig.lowSecurity() {
    return const SecurityConfig(
      securityLevel: SecurityLevel.low,
      encryptionEnabled: false,
      transportEncryptionEnabled: true,
      dataAtRestEncryptionEnabled: false,
      authenticationMethods: [AuthenticationMethod.password],
      sessionTimeout: 7200, // 2小时
      maxLoginAttempts: 10,
      auditLoggingEnabled: false,
      keyRotationInterval: 365, // 1年
    );
  }

  /// 创建高安全级别配置
  factory SecurityConfig.highSecurity() {
    return const SecurityConfig(
      securityLevel: SecurityLevel.high,
      encryptionEnabled: true,
      defaultEncryptionAlgorithm: EncryptionAlgorithm.aes256gcm,
      transportEncryptionEnabled: true,
      dataAtRestEncryptionEnabled: true,
      endToEndEncryptionEnabled: true,
      authenticationMethods: [
        AuthenticationMethod.password,
        AuthenticationMethod.biometric,
        AuthenticationMethod.twoFactor,
      ],
      biometricEnabled: true,
      twoFactorEnabled: true,
      passwordPolicy: PasswordPolicy.strong(),
      sessionTimeout: 1800, // 30分钟
      maxLoginAttempts: 3,
      accountLockoutDuration: 1800, // 30分钟
      auditLoggingEnabled: true,
      auditLogLevel: 'DEBUG',
      intrusionDetectionEnabled: true,
      tamperProtectionEnabled: true,
      certificatePinningEnabled: true,
      networkSecurityEnabled: true,
      rootDetectionEnabled: true,
      debugDetectionEnabled: true,
      emulatorDetectionEnabled: true,
      screenRecordingProtectionEnabled: true,
      screenshotProtectionEnabled: true,
      keyRotationInterval: 30, // 30天
    );
  }

  /// 创建企业级配置
  factory SecurityConfig.enterprise() {
    return const SecurityConfig(
      securityLevel: SecurityLevel.critical,
      encryptionEnabled: true,
      defaultEncryptionAlgorithm: EncryptionAlgorithm.aes256gcm,
      transportEncryptionEnabled: true,
      dataAtRestEncryptionEnabled: true,
      endToEndEncryptionEnabled: true,
      authenticationMethods: [
        AuthenticationMethod.certificate,
        AuthenticationMethod.multiFactor,
      ],
      biometricEnabled: true,
      twoFactorEnabled: true,
      passwordPolicy: PasswordPolicy.enterprise(),
      sessionTimeout: 900, // 15分钟
      maxLoginAttempts: 3,
      accountLockoutDuration: 3600, // 1小时
      auditLoggingEnabled: true,
      auditLogLevel: 'TRACE',
      intrusionDetectionEnabled: true,
      tamperProtectionEnabled: true,
      certificatePinningEnabled: true,
      networkSecurityEnabled: true,
      codeObfuscationEnabled: true,
      rootDetectionEnabled: true,
      debugDetectionEnabled: true,
      emulatorDetectionEnabled: true,
      screenRecordingProtectionEnabled: true,
      screenshotProtectionEnabled: true,
      keyRotationInterval: 7, // 7天
      keyEscrowEnabled: true,
      complianceRequirements: ['SOX', 'GDPR', 'HIPAA', 'PCI-DSS'],
    );
  }

  /// 获取数据分类
  DataClassification getDataClassification(String dataType) {
    return dataClassificationPolicy[dataType] ?? DataClassification.internal;
  }

  /// 检查是否需要加密
  bool shouldEncrypt(String dataType) {
    if (!encryptionEnabled) return false;
    
    final classification = getDataClassification(dataType);
    switch (classification) {
      case DataClassification.public:
        return false;
      case DataClassification.internal:
        return securityLevel.index >= SecurityLevel.medium.index;
      case DataClassification.confidential:
        return true;
      case DataClassification.secret:
      case DataClassification.topSecret:
        return true;
    }
  }

  /// 检查是否需要强认证
  bool requiresStrongAuthentication() {
    return securityLevel.index >= SecurityLevel.high.index ||
           authenticationMethods.contains(AuthenticationMethod.multiFactor);
  }

  /// 检查是否启用生物识别
  bool isBiometricRequired() {
    return biometricEnabled && 
           authenticationMethods.contains(AuthenticationMethod.biometric);
  }

  /// 检查是否需要双因素认证
  bool isTwoFactorRequired() {
    return twoFactorEnabled && 
           authenticationMethods.contains(AuthenticationMethod.twoFactor);
  }

  /// 复制并修改配置
  SecurityConfig copyWith({
    SecurityLevel? securityLevel,
    bool? encryptionEnabled,
    EncryptionAlgorithm? defaultEncryptionAlgorithm,
    bool? transportEncryptionEnabled,
    bool? dataAtRestEncryptionEnabled,
    bool? endToEndEncryptionEnabled,
    List<AuthenticationMethod>? authenticationMethods,
    bool? biometricEnabled,
    bool? twoFactorEnabled,
    PasswordPolicy? passwordPolicy,
    int? sessionTimeout,
    int? maxLoginAttempts,
    int? accountLockoutDuration,
    bool? auditLoggingEnabled,
    String? auditLogLevel,
    bool? intrusionDetectionEnabled,
    bool? tamperProtectionEnabled,
    bool? certificatePinningEnabled,
    List<String>? trustedCertificateFingerprints,
    bool? networkSecurityEnabled,
    List<String>? allowedNetworkProtocols,
    bool? codeObfuscationEnabled,
    bool? rootDetectionEnabled,
    bool? debugDetectionEnabled,
    bool? emulatorDetectionEnabled,
    bool? screenRecordingProtectionEnabled,
    bool? screenshotProtectionEnabled,
    Map<String, DataClassification>? dataClassificationPolicy,
    int? keyRotationInterval,
    bool? keyEscrowEnabled,
    List<String>? backupEncryptionKeys,
    String? policyVersion,
    List<String>? complianceRequirements,
    Map<String, dynamic>? customSecurityConfig,
  }) {
    return SecurityConfig(
      securityLevel: securityLevel ?? this.securityLevel,
      encryptionEnabled: encryptionEnabled ?? this.encryptionEnabled,
      defaultEncryptionAlgorithm: defaultEncryptionAlgorithm ?? this.defaultEncryptionAlgorithm,
      transportEncryptionEnabled: transportEncryptionEnabled ?? this.transportEncryptionEnabled,
      dataAtRestEncryptionEnabled: dataAtRestEncryptionEnabled ?? this.dataAtRestEncryptionEnabled,
      endToEndEncryptionEnabled: endToEndEncryptionEnabled ?? this.endToEndEncryptionEnabled,
      authenticationMethods: authenticationMethods ?? this.authenticationMethods,
      biometricEnabled: biometricEnabled ?? this.biometricEnabled,
      twoFactorEnabled: twoFactorEnabled ?? this.twoFactorEnabled,
      passwordPolicy: passwordPolicy ?? this.passwordPolicy,
      sessionTimeout: sessionTimeout ?? this.sessionTimeout,
      maxLoginAttempts: maxLoginAttempts ?? this.maxLoginAttempts,
      accountLockoutDuration: accountLockoutDuration ?? this.accountLockoutDuration,
      auditLoggingEnabled: auditLoggingEnabled ?? this.auditLoggingEnabled,
      auditLogLevel: auditLogLevel ?? this.auditLogLevel,
      intrusionDetectionEnabled: intrusionDetectionEnabled ?? this.intrusionDetectionEnabled,
      tamperProtectionEnabled: tamperProtectionEnabled ?? this.tamperProtectionEnabled,
      certificatePinningEnabled: certificatePinningEnabled ?? this.certificatePinningEnabled,
      trustedCertificateFingerprints: trustedCertificateFingerprints ?? this.trustedCertificateFingerprints,
      networkSecurityEnabled: networkSecurityEnabled ?? this.networkSecurityEnabled,
      allowedNetworkProtocols: allowedNetworkProtocols ?? this.allowedNetworkProtocols,
      codeObfuscationEnabled: codeObfuscationEnabled ?? this.codeObfuscationEnabled,
      rootDetectionEnabled: rootDetectionEnabled ?? this.rootDetectionEnabled,
      debugDetectionEnabled: debugDetectionEnabled ?? this.debugDetectionEnabled,
      emulatorDetectionEnabled: emulatorDetectionEnabled ?? this.emulatorDetectionEnabled,
      screenRecordingProtectionEnabled: screenRecordingProtectionEnabled ?? this.screenRecordingProtectionEnabled,
      screenshotProtectionEnabled: screenshotProtectionEnabled ?? this.screenshotProtectionEnabled,
      dataClassificationPolicy: dataClassificationPolicy ?? this.dataClassificationPolicy,
      keyRotationInterval: keyRotationInterval ?? this.keyRotationInterval,
      keyEscrowEnabled: keyEscrowEnabled ?? this.keyEscrowEnabled,
      backupEncryptionKeys: backupEncryptionKeys ?? this.backupEncryptionKeys,
      policyVersion: policyVersion ?? this.policyVersion,
      complianceRequirements: complianceRequirements ?? this.complianceRequirements,
      customSecurityConfig: customSecurityConfig ?? this.customSecurityConfig,
    );
  }

  /// 转换为 JSON
  Map<String, dynamic> toJson() {
    return {
      'securityLevel': securityLevel.name,
      'encryptionEnabled': encryptionEnabled,
      'defaultEncryptionAlgorithm': defaultEncryptionAlgorithm.name,
      'transportEncryptionEnabled': transportEncryptionEnabled,
      'dataAtRestEncryptionEnabled': dataAtRestEncryptionEnabled,
      'endToEndEncryptionEnabled': endToEndEncryptionEnabled,
      'authenticationMethods': authenticationMethods.map((e) => e.name).toList(),
      'biometricEnabled': biometricEnabled,
      'twoFactorEnabled': twoFactorEnabled,
      'passwordPolicy': passwordPolicy.toJson(),
      'sessionTimeout': sessionTimeout,
      'maxLoginAttempts': maxLoginAttempts,
      'accountLockoutDuration': accountLockoutDuration,
      'auditLoggingEnabled': auditLoggingEnabled,
      'auditLogLevel': auditLogLevel,
      'intrusionDetectionEnabled': intrusionDetectionEnabled,
      'tamperProtectionEnabled': tamperProtectionEnabled,
      'certificatePinningEnabled': certificatePinningEnabled,
      'trustedCertificateFingerprints': trustedCertificateFingerprints,
      'networkSecurityEnabled': networkSecurityEnabled,
      'allowedNetworkProtocols': allowedNetworkProtocols,
      'codeObfuscationEnabled': codeObfuscationEnabled,
      'rootDetectionEnabled': rootDetectionEnabled,
      'debugDetectionEnabled': debugDetectionEnabled,
      'emulatorDetectionEnabled': emulatorDetectionEnabled,
      'screenRecordingProtectionEnabled': screenRecordingProtectionEnabled,
      'screenshotProtectionEnabled': screenshotProtectionEnabled,
      'dataClassificationPolicy': dataClassificationPolicy.map((key, value) => MapEntry(key, value.name)),
      'keyRotationInterval': keyRotationInterval,
      'keyEscrowEnabled': keyEscrowEnabled,
      'backupEncryptionKeys': backupEncryptionKeys,
      'policyVersion': policyVersion,
      'complianceRequirements': complianceRequirements,
      'customSecurityConfig': customSecurityConfig,
    };
  }

  /// 从 JSON 创建
  factory SecurityConfig.fromJson(Map<String, dynamic> json) {
    return SecurityConfig(
      securityLevel: SecurityLevel.values.firstWhere(
        (e) => e.name == json['securityLevel'],
        orElse: () => SecurityLevel.medium,
      ),
      encryptionEnabled: json['encryptionEnabled'] as bool? ?? true,
      defaultEncryptionAlgorithm: EncryptionAlgorithm.values.firstWhere(
        (e) => e.name == json['defaultEncryptionAlgorithm'],
        orElse: () => EncryptionAlgorithm.aes256gcm,
      ),
      transportEncryptionEnabled: json['transportEncryptionEnabled'] as bool? ?? true,
      dataAtRestEncryptionEnabled: json['dataAtRestEncryptionEnabled'] as bool? ?? true,
      endToEndEncryptionEnabled: json['endToEndEncryptionEnabled'] as bool? ?? false,
      authenticationMethods: json['authenticationMethods'] != null
          ? (json['authenticationMethods'] as List)
              .map((e) => AuthenticationMethod.values.firstWhere(
                    (method) => method.name == e,
                    orElse: () => AuthenticationMethod.password,
                  ))
              .toList()
          : [AuthenticationMethod.password],
      biometricEnabled: json['biometricEnabled'] as bool? ?? false,
      twoFactorEnabled: json['twoFactorEnabled'] as bool? ?? false,
      passwordPolicy: json['passwordPolicy'] != null
          ? PasswordPolicy.fromJson(json['passwordPolicy'] as Map<String, dynamic>)
          : const PasswordPolicy(),
      sessionTimeout: json['sessionTimeout'] as int? ?? 3600,
      maxLoginAttempts: json['maxLoginAttempts'] as int? ?? 5,
      accountLockoutDuration: json['accountLockoutDuration'] as int? ?? 900,
      auditLoggingEnabled: json['auditLoggingEnabled'] as bool? ?? true,
      auditLogLevel: json['auditLogLevel'] as String? ?? 'INFO',
      intrusionDetectionEnabled: json['intrusionDetectionEnabled'] as bool? ?? false,
      tamperProtectionEnabled: json['tamperProtectionEnabled'] as bool? ?? false,
      certificatePinningEnabled: json['certificatePinningEnabled'] as bool? ?? false,
      trustedCertificateFingerprints: json['trustedCertificateFingerprints'] != null
          ? List<String>.from(json['trustedCertificateFingerprints'] as List)
          : [],
      networkSecurityEnabled: json['networkSecurityEnabled'] as bool? ?? true,
      allowedNetworkProtocols: json['allowedNetworkProtocols'] != null
          ? List<String>.from(json['allowedNetworkProtocols'] as List)
          : ['HTTPS', 'WSS'],
      codeObfuscationEnabled: json['codeObfuscationEnabled'] as bool? ?? false,
      rootDetectionEnabled: json['rootDetectionEnabled'] as bool? ?? false,
      debugDetectionEnabled: json['debugDetectionEnabled'] as bool? ?? false,
      emulatorDetectionEnabled: json['emulatorDetectionEnabled'] as bool? ?? false,
      screenRecordingProtectionEnabled: json['screenRecordingProtectionEnabled'] as bool? ?? false,
      screenshotProtectionEnabled: json['screenshotProtectionEnabled'] as bool? ?? false,
      dataClassificationPolicy: json['dataClassificationPolicy'] != null
          ? Map<String, DataClassification>.fromEntries(
              (json['dataClassificationPolicy'] as Map<String, dynamic>).entries.map(
                (entry) => MapEntry(
                  entry.key,
                  DataClassification.values.firstWhere(
                    (e) => e.name == entry.value,
                    orElse: () => DataClassification.internal,
                  ),
                ),
              ),
            )
          : {},
      keyRotationInterval: json['keyRotationInterval'] as int? ?? 90,
      keyEscrowEnabled: json['keyEscrowEnabled'] as bool? ?? false,
      backupEncryptionKeys: json['backupEncryptionKeys'] != null
          ? List<String>.from(json['backupEncryptionKeys'] as List)
          : [],
      policyVersion: json['policyVersion'] as String? ?? '1.0',
      complianceRequirements: json['complianceRequirements'] != null
          ? List<String>.from(json['complianceRequirements'] as List)
          : [],
      customSecurityConfig: json['customSecurityConfig'] as Map<String, dynamic>? ?? {},
    );
  }

  @override
  List<Object?> get props => [
        securityLevel,
        encryptionEnabled,
        defaultEncryptionAlgorithm,
        transportEncryptionEnabled,
        dataAtRestEncryptionEnabled,
        endToEndEncryptionEnabled,
        authenticationMethods,
        biometricEnabled,
        twoFactorEnabled,
        passwordPolicy,
        sessionTimeout,
        maxLoginAttempts,
        accountLockoutDuration,
        auditLoggingEnabled,
        auditLogLevel,
        intrusionDetectionEnabled,
        tamperProtectionEnabled,
        certificatePinningEnabled,
        trustedCertificateFingerprints,
        networkSecurityEnabled,
        allowedNetworkProtocols,
        codeObfuscationEnabled,
        rootDetectionEnabled,
        debugDetectionEnabled,
        emulatorDetectionEnabled,
        screenRecordingProtectionEnabled,
        screenshotProtectionEnabled,
        dataClassificationPolicy,
        keyRotationInterval,
        keyEscrowEnabled,
        backupEncryptionKeys,
        policyVersion,
        complianceRequirements,
        customSecurityConfig,
      ];

  @override
  String toString() {
    return 'SecurityConfig(level: $securityLevel, encryption: $encryptionEnabled)';
  }
}

/// 密码策略
class PasswordPolicy extends Equatable {
  /// 最小长度
  final int minLength;
  
  /// 最大长度
  final int maxLength;
  
  /// 是否需要大写字母
  final bool requireUppercase;
  
  /// 是否需要小写字母
  final bool requireLowercase;
  
  /// 是否需要数字
  final bool requireNumbers;
  
  /// 是否需要特殊字符
  final bool requireSpecialChars;
  
  /// 密码历史记录数量
  final int passwordHistory;
  
  /// 密码过期天数
  final int passwordExpirationDays;
  
  /// 最大重复字符数
  final int maxRepeatingChars;

  const PasswordPolicy({
    this.minLength = 8,
    this.maxLength = 128,
    this.requireUppercase = false,
    this.requireLowercase = false,
    this.requireNumbers = false,
    this.requireSpecialChars = false,
    this.passwordHistory = 0,
    this.passwordExpirationDays = 0,
    this.maxRepeatingChars = 3,
  });

  /// 强密码策略
  const PasswordPolicy.strong()
      : minLength = 12,
        maxLength = 128,
        requireUppercase = true,
        requireLowercase = true,
        requireNumbers = true,
        requireSpecialChars = true,
        passwordHistory = 5,
        passwordExpirationDays = 90,
        maxRepeatingChars = 2;

  /// 企业级密码策略
  const PasswordPolicy.enterprise()
      : minLength = 16,
        maxLength = 128,
        requireUppercase = true,
        requireLowercase = true,
        requireNumbers = true,
        requireSpecialChars = true,
        passwordHistory = 10,
        passwordExpirationDays = 30,
        maxRepeatingChars = 1;

  Map<String, dynamic> toJson() {
    return {
      'minLength': minLength,
      'maxLength': maxLength,
      'requireUppercase': requireUppercase,
      'requireLowercase': requireLowercase,
      'requireNumbers': requireNumbers,
      'requireSpecialChars': requireSpecialChars,
      'passwordHistory': passwordHistory,
      'passwordExpirationDays': passwordExpirationDays,
      'maxRepeatingChars': maxRepeatingChars,
    };
  }

  factory PasswordPolicy.fromJson(Map<String, dynamic> json) {
    return PasswordPolicy(
      minLength: json['minLength'] as int? ?? 8,
      maxLength: json['maxLength'] as int? ?? 128,
      requireUppercase: json['requireUppercase'] as bool? ?? false,
      requireLowercase: json['requireLowercase'] as bool? ?? false,
      requireNumbers: json['requireNumbers'] as bool? ?? false,
      requireSpecialChars: json['requireSpecialChars'] as bool? ?? false,
      passwordHistory: json['passwordHistory'] as int? ?? 0,
      passwordExpirationDays: json['passwordExpirationDays'] as int? ?? 0,
      maxRepeatingChars: json['maxRepeatingChars'] as int? ?? 3,
    );
  }

  @override
  List<Object?> get props => [
        minLength,
        maxLength,
        requireUppercase,
        requireLowercase,
        requireNumbers,
        requireSpecialChars,
        passwordHistory,
        passwordExpirationDays,
        maxRepeatingChars,
      ];
}
