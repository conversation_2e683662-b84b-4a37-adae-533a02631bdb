import 'package:equatable/equatable.dart';

/// 加密算法类型
enum EncryptionAlgorithm {
  /// AES-256-GCM
  aes256gcm,
  
  /// AES-256-CBC
  aes256cbc,
  
  /// ChaCha20-Poly1305
  chacha20poly1305,
  
  /// RSA-2048
  rsa2048,
  
  /// RSA-4096
  rsa4096,
  
  /// ECDSA-P256
  ecdsaP256,
  
  /// ECDSA-P384
  ecdsaP384,
}

/// 密钥类型
enum KeyType {
  /// 对称密钥
  symmetric,
  
  /// 非对称密钥（公钥）
  asymmetricPublic,
  
  /// 非对称密钥（私钥）
  asymmetricPrivate,
  
  /// 密钥交换密钥
  keyExchange,
  
  /// 签名密钥
  signing,
  
  /// 验证密钥
  verification,
}

/// 密钥状态
enum KeyStatus {
  /// 活跃
  active,
  
  /// 已撤销
  revoked,
  
  /// 已过期
  expired,
  
  /// 已轮换
  rotated,
  
  /// 已删除
  deleted,
}

/// 加密密钥实体
/// 
/// **功能依赖**: 需要启用 security 模块
/// **配置项**: FEATURE_SECURITY
class EncryptionKey extends Equatable {
  /// 密钥ID
  final String keyId;
  
  /// 密钥别名
  final String alias;
  
  /// 密钥类型
  final KeyType keyType;
  
  /// 加密算法
  final EncryptionAlgorithm algorithm;
  
  /// 密钥状态
  final KeyStatus status;
  
  /// 密钥数据（加密存储）
  final String keyData;
  
  /// 公钥数据（如果是非对称密钥）
  final String? publicKeyData;
  
  /// 密钥长度（位）
  final int keyLength;
  
  /// 创建时间
  final DateTime createdAt;
  
  /// 过期时间
  final DateTime? expiresAt;
  
  /// 最后使用时间
  final DateTime? lastUsedAt;
  
  /// 使用次数
  final int usageCount;
  
  /// 最大使用次数
  final int? maxUsageCount;
  
  /// 密钥版本
  final int version;
  
  /// 父密钥ID（用于密钥轮换）
  final String? parentKeyId;
  
  /// 子密钥ID列表
  final List<String>? childKeyIds;
  
  /// 密钥用途
  final List<String> purposes;
  
  /// 密钥标签
  final Map<String, String> tags;
  
  /// 是否可导出
  final bool exportable;
  
  /// 是否在硬件安全模块中
  final bool inHSM;
  
  /// 创建者ID
  final String? createdBy;
  
  /// 密钥校验和
  final String? checksum;

  const EncryptionKey({
    required this.keyId,
    required this.alias,
    required this.keyType,
    required this.algorithm,
    this.status = KeyStatus.active,
    required this.keyData,
    this.publicKeyData,
    required this.keyLength,
    required this.createdAt,
    this.expiresAt,
    this.lastUsedAt,
    this.usageCount = 0,
    this.maxUsageCount,
    this.version = 1,
    this.parentKeyId,
    this.childKeyIds,
    this.purposes = const [],
    this.tags = const {},
    this.exportable = false,
    this.inHSM = false,
    this.createdBy,
    this.checksum,
  });

  /// 创建AES-256对称密钥
  factory EncryptionKey.aes256({
    required String keyId,
    required String alias,
    required String keyData,
    List<String> purposes = const ['encryption', 'decryption'],
    Map<String, String> tags = const {},
    DateTime? expiresAt,
    String? createdBy,
  }) {
    return EncryptionKey(
      keyId: keyId,
      alias: alias,
      keyType: KeyType.symmetric,
      algorithm: EncryptionAlgorithm.aes256gcm,
      keyData: keyData,
      keyLength: 256,
      createdAt: DateTime.now(),
      expiresAt: expiresAt,
      purposes: purposes,
      tags: tags,
      createdBy: createdBy,
    );
  }

  /// 创建RSA非对称密钥对
  factory EncryptionKey.rsa({
    required String keyId,
    required String alias,
    required String privateKeyData,
    required String publicKeyData,
    int keyLength = 2048,
    List<String> purposes = const ['encryption', 'decryption', 'signing'],
    Map<String, String> tags = const {},
    DateTime? expiresAt,
    String? createdBy,
  }) {
    return EncryptionKey(
      keyId: keyId,
      alias: alias,
      keyType: KeyType.asymmetricPrivate,
      algorithm: keyLength == 2048 
          ? EncryptionAlgorithm.rsa2048 
          : EncryptionAlgorithm.rsa4096,
      keyData: privateKeyData,
      publicKeyData: publicKeyData,
      keyLength: keyLength,
      createdAt: DateTime.now(),
      expiresAt: expiresAt,
      purposes: purposes,
      tags: tags,
      createdBy: createdBy,
    );
  }

  /// 创建ECDSA签名密钥
  factory EncryptionKey.ecdsa({
    required String keyId,
    required String alias,
    required String privateKeyData,
    required String publicKeyData,
    String curve = 'P-256',
    List<String> purposes = const ['signing', 'verification'],
    Map<String, String> tags = const {},
    DateTime? expiresAt,
    String? createdBy,
  }) {
    return EncryptionKey(
      keyId: keyId,
      alias: alias,
      keyType: KeyType.signing,
      algorithm: curve == 'P-256' 
          ? EncryptionAlgorithm.ecdsaP256 
          : EncryptionAlgorithm.ecdsaP384,
      keyData: privateKeyData,
      publicKeyData: publicKeyData,
      keyLength: curve == 'P-256' ? 256 : 384,
      createdAt: DateTime.now(),
      expiresAt: expiresAt,
      purposes: purposes,
      tags: tags,
      createdBy: createdBy,
    );
  }

  /// 是否为对称密钥
  bool get isSymmetric => keyType == KeyType.symmetric;

  /// 是否为非对称密钥
  bool get isAsymmetric => 
      keyType == KeyType.asymmetricPublic || 
      keyType == KeyType.asymmetricPrivate;

  /// 是否为签名密钥
  bool get isSigningKey => 
      keyType == KeyType.signing || 
      purposes.contains('signing');

  /// 是否已过期
  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  /// 是否已撤销
  bool get isRevoked => status == KeyStatus.revoked;

  /// 是否活跃
  bool get isActive => status == KeyStatus.active && !isExpired;

  /// 是否可用于加密
  bool get canEncrypt => 
      isActive && purposes.contains('encryption');

  /// 是否可用于解密
  bool get canDecrypt => 
      isActive && purposes.contains('decryption');

  /// 是否可用于签名
  bool get canSign => 
      isActive && purposes.contains('signing');

  /// 是否可用于验证
  bool get canVerify => 
      isActive && purposes.contains('verification');

  /// 是否达到使用限制
  bool get isUsageLimitReached {
    if (maxUsageCount == null) return false;
    return usageCount >= maxUsageCount!;
  }

  /// 记录使用
  EncryptionKey recordUsage() {
    return copyWith(
      usageCount: usageCount + 1,
      lastUsedAt: DateTime.now(),
    );
  }

  /// 撤销密钥
  EncryptionKey revoke() {
    return copyWith(status: KeyStatus.revoked);
  }

  /// 标记为已过期
  EncryptionKey markExpired() {
    return copyWith(status: KeyStatus.expired);
  }

  /// 轮换密钥
  EncryptionKey rotate(String newKeyId) {
    return copyWith(
      status: KeyStatus.rotated,
      childKeyIds: [...(childKeyIds ?? []), newKeyId],
    );
  }

  /// 复制并修改密钥
  EncryptionKey copyWith({
    String? keyId,
    String? alias,
    KeyType? keyType,
    EncryptionAlgorithm? algorithm,
    KeyStatus? status,
    String? keyData,
    String? publicKeyData,
    int? keyLength,
    DateTime? createdAt,
    DateTime? expiresAt,
    DateTime? lastUsedAt,
    int? usageCount,
    int? maxUsageCount,
    int? version,
    String? parentKeyId,
    List<String>? childKeyIds,
    List<String>? purposes,
    Map<String, String>? tags,
    bool? exportable,
    bool? inHSM,
    String? createdBy,
    String? checksum,
  }) {
    return EncryptionKey(
      keyId: keyId ?? this.keyId,
      alias: alias ?? this.alias,
      keyType: keyType ?? this.keyType,
      algorithm: algorithm ?? this.algorithm,
      status: status ?? this.status,
      keyData: keyData ?? this.keyData,
      publicKeyData: publicKeyData ?? this.publicKeyData,
      keyLength: keyLength ?? this.keyLength,
      createdAt: createdAt ?? this.createdAt,
      expiresAt: expiresAt ?? this.expiresAt,
      lastUsedAt: lastUsedAt ?? this.lastUsedAt,
      usageCount: usageCount ?? this.usageCount,
      maxUsageCount: maxUsageCount ?? this.maxUsageCount,
      version: version ?? this.version,
      parentKeyId: parentKeyId ?? this.parentKeyId,
      childKeyIds: childKeyIds ?? this.childKeyIds,
      purposes: purposes ?? this.purposes,
      tags: tags ?? this.tags,
      exportable: exportable ?? this.exportable,
      inHSM: inHSM ?? this.inHSM,
      createdBy: createdBy ?? this.createdBy,
      checksum: checksum ?? this.checksum,
    );
  }

  /// 转换为 JSON（不包含敏感数据）
  Map<String, dynamic> toJson({bool includeSensitiveData = false}) {
    final json = {
      'keyId': keyId,
      'alias': alias,
      'keyType': keyType.name,
      'algorithm': algorithm.name,
      'status': status.name,
      'keyLength': keyLength,
      'createdAt': createdAt.toIso8601String(),
      'expiresAt': expiresAt?.toIso8601String(),
      'lastUsedAt': lastUsedAt?.toIso8601String(),
      'usageCount': usageCount,
      'maxUsageCount': maxUsageCount,
      'version': version,
      'parentKeyId': parentKeyId,
      'childKeyIds': childKeyIds,
      'purposes': purposes,
      'tags': tags,
      'exportable': exportable,
      'inHSM': inHSM,
      'createdBy': createdBy,
      'checksum': checksum,
    };

    if (includeSensitiveData) {
      json['keyData'] = keyData;
      json['publicKeyData'] = publicKeyData;
    }

    return json;
  }

  /// 从 JSON 创建（需要敏感数据）
  factory EncryptionKey.fromJson(Map<String, dynamic> json) {
    return EncryptionKey(
      keyId: json['keyId'] as String,
      alias: json['alias'] as String,
      keyType: KeyType.values.firstWhere(
        (e) => e.name == json['keyType'],
        orElse: () => KeyType.symmetric,
      ),
      algorithm: EncryptionAlgorithm.values.firstWhere(
        (e) => e.name == json['algorithm'],
        orElse: () => EncryptionAlgorithm.aes256gcm,
      ),
      status: KeyStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => KeyStatus.active,
      ),
      keyData: json['keyData'] as String,
      publicKeyData: json['publicKeyData'] as String?,
      keyLength: json['keyLength'] as int,
      createdAt: DateTime.parse(json['createdAt'] as String),
      expiresAt: json['expiresAt'] != null
          ? DateTime.parse(json['expiresAt'] as String)
          : null,
      lastUsedAt: json['lastUsedAt'] != null
          ? DateTime.parse(json['lastUsedAt'] as String)
          : null,
      usageCount: json['usageCount'] as int? ?? 0,
      maxUsageCount: json['maxUsageCount'] as int?,
      version: json['version'] as int? ?? 1,
      parentKeyId: json['parentKeyId'] as String?,
      childKeyIds: json['childKeyIds'] != null
          ? List<String>.from(json['childKeyIds'] as List)
          : null,
      purposes: json['purposes'] != null
          ? List<String>.from(json['purposes'] as List)
          : [],
      tags: json['tags'] != null
          ? Map<String, String>.from(json['tags'] as Map)
          : {},
      exportable: json['exportable'] as bool? ?? false,
      inHSM: json['inHSM'] as bool? ?? false,
      createdBy: json['createdBy'] as String?,
      checksum: json['checksum'] as String?,
    );
  }

  @override
  List<Object?> get props => [
        keyId,
        alias,
        keyType,
        algorithm,
        status,
        keyData,
        publicKeyData,
        keyLength,
        createdAt,
        expiresAt,
        lastUsedAt,
        usageCount,
        maxUsageCount,
        version,
        parentKeyId,
        childKeyIds,
        purposes,
        tags,
        exportable,
        inHSM,
        createdBy,
        checksum,
      ];

  @override
  String toString() {
    return 'EncryptionKey(id: $keyId, alias: $alias, type: $keyType, algorithm: $algorithm, status: $status)';
  }
}
