import 'package:equatable/equatable.dart';

/// 同步策略
enum SyncStrategy {
  /// 立即同步
  immediate,
  
  /// 批量同步
  batch,
  
  /// 定时同步
  scheduled,
  
  /// 手动同步
  manual,
  
  /// 智能同步
  intelligent,
}

/// 冲突解决策略
enum ConflictResolutionStrategy {
  /// 客户端优先
  clientWins,
  
  /// 服务器优先
  serverWins,
  
  /// 最新时间优先
  lastWriteWins,
  
  /// 手动解决
  manual,
  
  /// 合并数据
  merge,
}

/// 网络策略
enum NetworkStrategy {
  /// 仅WiFi
  wifiOnly,
  
  /// 任何网络
  anyNetwork,
  
  /// 根据数据类型
  dataTypeDependent,
  
  /// 用户选择
  userChoice,
}

/// 离线配置实体
/// 
/// **功能依赖**: 需要启用 offline 模块
/// **配置项**: FEATURE_OFFLINE
class OfflineConfig extends Equatable {
  /// 是否启用离线模式
  final bool offlineEnabled;
  
  /// 同步策略
  final SyncStrategy syncStrategy;
  
  /// 冲突解决策略
  final ConflictResolutionStrategy conflictResolutionStrategy;
  
  /// 网络策略
  final NetworkStrategy networkStrategy;
  
  /// 自动同步间隔（秒）
  final int autoSyncInterval;
  
  /// 批量同步大小
  final int batchSyncSize;
  
  /// 最大重试次数
  final int maxRetries;
  
  /// 重试间隔（秒）
  final int retryInterval;
  
  /// 数据过期时间（秒）
  final int dataExpirationTime;
  
  /// 最大离线数据大小（MB）
  final int maxOfflineDataSize;
  
  /// 最大离线数据条数
  final int maxOfflineDataCount;
  
  /// 是否压缩数据
  final bool compressData;
  
  /// 是否加密数据
  final bool encryptData;
  
  /// 是否启用增量同步
  final bool enableIncrementalSync;
  
  /// 是否启用后台同步
  final bool enableBackgroundSync;
  
  /// 是否在应用启动时同步
  final bool syncOnAppStart;
  
  /// 是否在网络恢复时同步
  final bool syncOnNetworkRestore;
  
  /// 优先同步的数据类型
  final List<String> priorityDataTypes;
  
  /// 禁用离线的数据类型
  final List<String> disabledDataTypes;
  
  /// 按数据类型的配置
  final Map<String, DataTypeConfig> dataTypeConfigs;
  
  /// 自定义配置
  final Map<String, dynamic> customConfig;

  const OfflineConfig({
    this.offlineEnabled = true,
    this.syncStrategy = SyncStrategy.intelligent,
    this.conflictResolutionStrategy = ConflictResolutionStrategy.lastWriteWins,
    this.networkStrategy = NetworkStrategy.anyNetwork,
    this.autoSyncInterval = 300, // 5分钟
    this.batchSyncSize = 50,
    this.maxRetries = 3,
    this.retryInterval = 30,
    this.dataExpirationTime = 86400, // 24小时
    this.maxOfflineDataSize = 100, // 100MB
    this.maxOfflineDataCount = 10000,
    this.compressData = true,
    this.encryptData = false,
    this.enableIncrementalSync = true,
    this.enableBackgroundSync = true,
    this.syncOnAppStart = true,
    this.syncOnNetworkRestore = true,
    this.priorityDataTypes = const [],
    this.disabledDataTypes = const [],
    this.dataTypeConfigs = const {},
    this.customConfig = const {},
  });

  /// 创建默认配置
  factory OfflineConfig.defaultConfig() {
    return const OfflineConfig(
      offlineEnabled: true,
      syncStrategy: SyncStrategy.intelligent,
      conflictResolutionStrategy: ConflictResolutionStrategy.lastWriteWins,
      networkStrategy: NetworkStrategy.anyNetwork,
      autoSyncInterval: 300,
      batchSyncSize: 50,
      maxRetries: 3,
      retryInterval: 30,
      dataExpirationTime: 86400,
      maxOfflineDataSize: 100,
      maxOfflineDataCount: 10000,
      compressData: true,
      encryptData: false,
      enableIncrementalSync: true,
      enableBackgroundSync: true,
      syncOnAppStart: true,
      syncOnNetworkRestore: true,
    );
  }

  /// 创建高性能配置
  factory OfflineConfig.highPerformance() {
    return const OfflineConfig(
      offlineEnabled: true,
      syncStrategy: SyncStrategy.batch,
      conflictResolutionStrategy: ConflictResolutionStrategy.clientWins,
      networkStrategy: NetworkStrategy.wifiOnly,
      autoSyncInterval: 600, // 10分钟
      batchSyncSize: 100,
      maxRetries: 5,
      retryInterval: 60,
      dataExpirationTime: 172800, // 48小时
      maxOfflineDataSize: 500, // 500MB
      maxOfflineDataCount: 50000,
      compressData: true,
      encryptData: true,
      enableIncrementalSync: true,
      enableBackgroundSync: true,
      syncOnAppStart: false,
      syncOnNetworkRestore: true,
    );
  }

  /// 创建低功耗配置
  factory OfflineConfig.lowPower() {
    return const OfflineConfig(
      offlineEnabled: true,
      syncStrategy: SyncStrategy.manual,
      conflictResolutionStrategy: ConflictResolutionStrategy.serverWins,
      networkStrategy: NetworkStrategy.wifiOnly,
      autoSyncInterval: 1800, // 30分钟
      batchSyncSize: 20,
      maxRetries: 2,
      retryInterval: 120,
      dataExpirationTime: 43200, // 12小时
      maxOfflineDataSize: 50, // 50MB
      maxOfflineDataCount: 5000,
      compressData: true,
      encryptData: false,
      enableIncrementalSync: false,
      enableBackgroundSync: false,
      syncOnAppStart: false,
      syncOnNetworkRestore: false,
    );
  }

  /// 获取数据类型配置
  DataTypeConfig? getDataTypeConfig(String dataType) {
    return dataTypeConfigs[dataType];
  }

  /// 检查数据类型是否启用离线
  bool isDataTypeOfflineEnabled(String dataType) {
    if (disabledDataTypes.contains(dataType)) return false;
    
    final config = getDataTypeConfig(dataType);
    return config?.offlineEnabled ?? offlineEnabled;
  }

  /// 检查数据类型是否为优先级
  bool isDataTypePriority(String dataType) {
    return priorityDataTypes.contains(dataType);
  }

  /// 获取数据类型的同步策略
  SyncStrategy getDataTypeSyncStrategy(String dataType) {
    final config = getDataTypeConfig(dataType);
    return config?.syncStrategy ?? syncStrategy;
  }

  /// 获取数据类型的冲突解决策略
  ConflictResolutionStrategy getDataTypeConflictStrategy(String dataType) {
    final config = getDataTypeConfig(dataType);
    return config?.conflictResolutionStrategy ?? conflictResolutionStrategy;
  }

  /// 复制并修改配置
  OfflineConfig copyWith({
    bool? offlineEnabled,
    SyncStrategy? syncStrategy,
    ConflictResolutionStrategy? conflictResolutionStrategy,
    NetworkStrategy? networkStrategy,
    int? autoSyncInterval,
    int? batchSyncSize,
    int? maxRetries,
    int? retryInterval,
    int? dataExpirationTime,
    int? maxOfflineDataSize,
    int? maxOfflineDataCount,
    bool? compressData,
    bool? encryptData,
    bool? enableIncrementalSync,
    bool? enableBackgroundSync,
    bool? syncOnAppStart,
    bool? syncOnNetworkRestore,
    List<String>? priorityDataTypes,
    List<String>? disabledDataTypes,
    Map<String, DataTypeConfig>? dataTypeConfigs,
    Map<String, dynamic>? customConfig,
  }) {
    return OfflineConfig(
      offlineEnabled: offlineEnabled ?? this.offlineEnabled,
      syncStrategy: syncStrategy ?? this.syncStrategy,
      conflictResolutionStrategy: conflictResolutionStrategy ?? this.conflictResolutionStrategy,
      networkStrategy: networkStrategy ?? this.networkStrategy,
      autoSyncInterval: autoSyncInterval ?? this.autoSyncInterval,
      batchSyncSize: batchSyncSize ?? this.batchSyncSize,
      maxRetries: maxRetries ?? this.maxRetries,
      retryInterval: retryInterval ?? this.retryInterval,
      dataExpirationTime: dataExpirationTime ?? this.dataExpirationTime,
      maxOfflineDataSize: maxOfflineDataSize ?? this.maxOfflineDataSize,
      maxOfflineDataCount: maxOfflineDataCount ?? this.maxOfflineDataCount,
      compressData: compressData ?? this.compressData,
      encryptData: encryptData ?? this.encryptData,
      enableIncrementalSync: enableIncrementalSync ?? this.enableIncrementalSync,
      enableBackgroundSync: enableBackgroundSync ?? this.enableBackgroundSync,
      syncOnAppStart: syncOnAppStart ?? this.syncOnAppStart,
      syncOnNetworkRestore: syncOnNetworkRestore ?? this.syncOnNetworkRestore,
      priorityDataTypes: priorityDataTypes ?? this.priorityDataTypes,
      disabledDataTypes: disabledDataTypes ?? this.disabledDataTypes,
      dataTypeConfigs: dataTypeConfigs ?? this.dataTypeConfigs,
      customConfig: customConfig ?? this.customConfig,
    );
  }

  /// 转换为 JSON
  Map<String, dynamic> toJson() {
    return {
      'offlineEnabled': offlineEnabled,
      'syncStrategy': syncStrategy.name,
      'conflictResolutionStrategy': conflictResolutionStrategy.name,
      'networkStrategy': networkStrategy.name,
      'autoSyncInterval': autoSyncInterval,
      'batchSyncSize': batchSyncSize,
      'maxRetries': maxRetries,
      'retryInterval': retryInterval,
      'dataExpirationTime': dataExpirationTime,
      'maxOfflineDataSize': maxOfflineDataSize,
      'maxOfflineDataCount': maxOfflineDataCount,
      'compressData': compressData,
      'encryptData': encryptData,
      'enableIncrementalSync': enableIncrementalSync,
      'enableBackgroundSync': enableBackgroundSync,
      'syncOnAppStart': syncOnAppStart,
      'syncOnNetworkRestore': syncOnNetworkRestore,
      'priorityDataTypes': priorityDataTypes,
      'disabledDataTypes': disabledDataTypes,
      'dataTypeConfigs': dataTypeConfigs.map((key, value) => MapEntry(key, value.toJson())),
      'customConfig': customConfig,
    };
  }

  /// 从 JSON 创建
  factory OfflineConfig.fromJson(Map<String, dynamic> json) {
    return OfflineConfig(
      offlineEnabled: json['offlineEnabled'] as bool? ?? true,
      syncStrategy: SyncStrategy.values.firstWhere(
        (e) => e.name == json['syncStrategy'],
        orElse: () => SyncStrategy.intelligent,
      ),
      conflictResolutionStrategy: ConflictResolutionStrategy.values.firstWhere(
        (e) => e.name == json['conflictResolutionStrategy'],
        orElse: () => ConflictResolutionStrategy.lastWriteWins,
      ),
      networkStrategy: NetworkStrategy.values.firstWhere(
        (e) => e.name == json['networkStrategy'],
        orElse: () => NetworkStrategy.anyNetwork,
      ),
      autoSyncInterval: json['autoSyncInterval'] as int? ?? 300,
      batchSyncSize: json['batchSyncSize'] as int? ?? 50,
      maxRetries: json['maxRetries'] as int? ?? 3,
      retryInterval: json['retryInterval'] as int? ?? 30,
      dataExpirationTime: json['dataExpirationTime'] as int? ?? 86400,
      maxOfflineDataSize: json['maxOfflineDataSize'] as int? ?? 100,
      maxOfflineDataCount: json['maxOfflineDataCount'] as int? ?? 10000,
      compressData: json['compressData'] as bool? ?? true,
      encryptData: json['encryptData'] as bool? ?? false,
      enableIncrementalSync: json['enableIncrementalSync'] as bool? ?? true,
      enableBackgroundSync: json['enableBackgroundSync'] as bool? ?? true,
      syncOnAppStart: json['syncOnAppStart'] as bool? ?? true,
      syncOnNetworkRestore: json['syncOnNetworkRestore'] as bool? ?? true,
      priorityDataTypes: json['priorityDataTypes'] != null
          ? List<String>.from(json['priorityDataTypes'] as List)
          : [],
      disabledDataTypes: json['disabledDataTypes'] != null
          ? List<String>.from(json['disabledDataTypes'] as List)
          : [],
      dataTypeConfigs: json['dataTypeConfigs'] != null
          ? Map<String, DataTypeConfig>.fromEntries(
              (json['dataTypeConfigs'] as Map<String, dynamic>).entries.map(
                (entry) => MapEntry(
                  entry.key,
                  DataTypeConfig.fromJson(entry.value as Map<String, dynamic>),
                ),
              ),
            )
          : {},
      customConfig: json['customConfig'] as Map<String, dynamic>? ?? {},
    );
  }

  @override
  List<Object?> get props => [
        offlineEnabled,
        syncStrategy,
        conflictResolutionStrategy,
        networkStrategy,
        autoSyncInterval,
        batchSyncSize,
        maxRetries,
        retryInterval,
        dataExpirationTime,
        maxOfflineDataSize,
        maxOfflineDataCount,
        compressData,
        encryptData,
        enableIncrementalSync,
        enableBackgroundSync,
        syncOnAppStart,
        syncOnNetworkRestore,
        priorityDataTypes,
        disabledDataTypes,
        dataTypeConfigs,
        customConfig,
      ];

  @override
  String toString() {
    return 'OfflineConfig(enabled: $offlineEnabled, strategy: $syncStrategy)';
  }
}

/// 数据类型配置
class DataTypeConfig extends Equatable {
  /// 是否启用离线
  final bool offlineEnabled;
  
  /// 同步策略
  final SyncStrategy syncStrategy;
  
  /// 冲突解决策略
  final ConflictResolutionStrategy conflictResolutionStrategy;
  
  /// 最大重试次数
  final int maxRetries;
  
  /// 数据过期时间（秒）
  final int dataExpirationTime;
  
  /// 是否压缩数据
  final bool compressData;
  
  /// 是否加密数据
  final bool encryptData;
  
  /// 优先级
  final int priority;

  const DataTypeConfig({
    this.offlineEnabled = true,
    this.syncStrategy = SyncStrategy.intelligent,
    this.conflictResolutionStrategy = ConflictResolutionStrategy.lastWriteWins,
    this.maxRetries = 3,
    this.dataExpirationTime = 86400,
    this.compressData = true,
    this.encryptData = false,
    this.priority = 0,
  });

  DataTypeConfig copyWith({
    bool? offlineEnabled,
    SyncStrategy? syncStrategy,
    ConflictResolutionStrategy? conflictResolutionStrategy,
    int? maxRetries,
    int? dataExpirationTime,
    bool? compressData,
    bool? encryptData,
    int? priority,
  }) {
    return DataTypeConfig(
      offlineEnabled: offlineEnabled ?? this.offlineEnabled,
      syncStrategy: syncStrategy ?? this.syncStrategy,
      conflictResolutionStrategy: conflictResolutionStrategy ?? this.conflictResolutionStrategy,
      maxRetries: maxRetries ?? this.maxRetries,
      dataExpirationTime: dataExpirationTime ?? this.dataExpirationTime,
      compressData: compressData ?? this.compressData,
      encryptData: encryptData ?? this.encryptData,
      priority: priority ?? this.priority,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'offlineEnabled': offlineEnabled,
      'syncStrategy': syncStrategy.name,
      'conflictResolutionStrategy': conflictResolutionStrategy.name,
      'maxRetries': maxRetries,
      'dataExpirationTime': dataExpirationTime,
      'compressData': compressData,
      'encryptData': encryptData,
      'priority': priority,
    };
  }

  factory DataTypeConfig.fromJson(Map<String, dynamic> json) {
    return DataTypeConfig(
      offlineEnabled: json['offlineEnabled'] as bool? ?? true,
      syncStrategy: SyncStrategy.values.firstWhere(
        (e) => e.name == json['syncStrategy'],
        orElse: () => SyncStrategy.intelligent,
      ),
      conflictResolutionStrategy: ConflictResolutionStrategy.values.firstWhere(
        (e) => e.name == json['conflictResolutionStrategy'],
        orElse: () => ConflictResolutionStrategy.lastWriteWins,
      ),
      maxRetries: json['maxRetries'] as int? ?? 3,
      dataExpirationTime: json['dataExpirationTime'] as int? ?? 86400,
      compressData: json['compressData'] as bool? ?? true,
      encryptData: json['encryptData'] as bool? ?? false,
      priority: json['priority'] as int? ?? 0,
    );
  }

  @override
  List<Object?> get props => [
        offlineEnabled,
        syncStrategy,
        conflictResolutionStrategy,
        maxRetries,
        dataExpirationTime,
        compressData,
        encryptData,
        priority,
      ];
}
