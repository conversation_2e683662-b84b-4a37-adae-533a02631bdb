import 'package:equatable/equatable.dart';

/// 离线数据操作类型
enum OfflineOperationType {
  /// 创建
  create,
  
  /// 更新
  update,
  
  /// 删除
  delete,
  
  /// 同步
  sync,
}

/// 离线数据状态
enum OfflineDataStatus {
  /// 待同步
  pending,
  
  /// 同步中
  syncing,
  
  /// 已同步
  synced,
  
  /// 同步失败
  failed,
  
  /// 冲突
  conflict,
  
  /// 已过期
  expired,
}

/// 离线数据实体
/// 
/// **功能依赖**: 需要启用 offline 模块
/// **配置项**: FEATURE_OFFLINE
class OfflineData extends Equatable {
  /// 数据ID
  final String id;
  
  /// 数据类型
  final String dataType;
  
  /// 操作类型
  final OfflineOperationType operationType;
  
  /// 数据状态
  final OfflineDataStatus status;
  
  /// 数据内容
  final Map<String, dynamic> data;
  
  /// 元数据
  final Map<String, dynamic> metadata;
  
  /// 创建时间
  final DateTime createdAt;
  
  /// 更新时间
  final DateTime updatedAt;
  
  /// 最后同步时间
  final DateTime? lastSyncAt;
  
  /// 版本号
  final int version;
  
  /// 服务器版本号
  final int? serverVersion;
  
  /// 用户ID
  final String? userId;
  
  /// 设备ID
  final String? deviceId;
  
  /// 优先级
  final int priority;
  
  /// 重试次数
  final int retryCount;
  
  /// 最大重试次数
  final int maxRetries;
  
  /// 错误信息
  final String? errorMessage;
  
  /// 冲突数据
  final Map<String, dynamic>? conflictData;
  
  /// 过期时间
  final DateTime? expiresAt;
  
  /// 是否需要网络
  final bool requiresNetwork;
  
  /// 依赖的数据ID
  final List<String>? dependencies;

  const OfflineData({
    required this.id,
    required this.dataType,
    required this.operationType,
    this.status = OfflineDataStatus.pending,
    required this.data,
    this.metadata = const {},
    required this.createdAt,
    required this.updatedAt,
    this.lastSyncAt,
    this.version = 1,
    this.serverVersion,
    this.userId,
    this.deviceId,
    this.priority = 0,
    this.retryCount = 0,
    this.maxRetries = 3,
    this.errorMessage,
    this.conflictData,
    this.expiresAt,
    this.requiresNetwork = true,
    this.dependencies,
  });

  /// 创建新的离线数据
  factory OfflineData.create({
    required String id,
    required String dataType,
    required Map<String, dynamic> data,
    String? userId,
    String? deviceId,
    Map<String, dynamic>? metadata,
    int priority = 0,
    bool requiresNetwork = true,
    List<String>? dependencies,
  }) {
    final now = DateTime.now();
    return OfflineData(
      id: id,
      dataType: dataType,
      operationType: OfflineOperationType.create,
      data: data,
      metadata: metadata ?? {},
      createdAt: now,
      updatedAt: now,
      userId: userId,
      deviceId: deviceId,
      priority: priority,
      requiresNetwork: requiresNetwork,
      dependencies: dependencies,
    );
  }

  /// 创建更新操作
  factory OfflineData.update({
    required String id,
    required String dataType,
    required Map<String, dynamic> data,
    String? userId,
    String? deviceId,
    Map<String, dynamic>? metadata,
    int version = 1,
    int priority = 0,
    bool requiresNetwork = true,
    List<String>? dependencies,
  }) {
    final now = DateTime.now();
    return OfflineData(
      id: id,
      dataType: dataType,
      operationType: OfflineOperationType.update,
      data: data,
      metadata: metadata ?? {},
      createdAt: now,
      updatedAt: now,
      version: version,
      userId: userId,
      deviceId: deviceId,
      priority: priority,
      requiresNetwork: requiresNetwork,
      dependencies: dependencies,
    );
  }

  /// 创建删除操作
  factory OfflineData.delete({
    required String id,
    required String dataType,
    String? userId,
    String? deviceId,
    Map<String, dynamic>? metadata,
    int priority = 0,
    bool requiresNetwork = true,
  }) {
    final now = DateTime.now();
    return OfflineData(
      id: id,
      dataType: dataType,
      operationType: OfflineOperationType.delete,
      data: {'deleted': true},
      metadata: metadata ?? {},
      createdAt: now,
      updatedAt: now,
      userId: userId,
      deviceId: deviceId,
      priority: priority,
      requiresNetwork: requiresNetwork,
    );
  }

  /// 是否待同步
  bool get isPending => status == OfflineDataStatus.pending;

  /// 是否同步中
  bool get isSyncing => status == OfflineDataStatus.syncing;

  /// 是否已同步
  bool get isSynced => status == OfflineDataStatus.synced;

  /// 是否同步失败
  bool get isFailed => status == OfflineDataStatus.failed;

  /// 是否有冲突
  bool get hasConflict => status == OfflineDataStatus.conflict;

  /// 是否已过期
  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  /// 是否可以重试
  bool get canRetry => retryCount < maxRetries && !isExpired;

  /// 是否为高优先级
  bool get isHighPriority => priority > 0;

  /// 获取数据大小（字节）
  int get dataSize {
    final jsonString = data.toString();
    return jsonString.length;
  }

  /// 标记为同步中
  OfflineData markAsSyncing() {
    return copyWith(
      status: OfflineDataStatus.syncing,
      updatedAt: DateTime.now(),
    );
  }

  /// 标记为已同步
  OfflineData markAsSynced({
    int? serverVersion,
    DateTime? syncTime,
  }) {
    return copyWith(
      status: OfflineDataStatus.synced,
      serverVersion: serverVersion,
      lastSyncAt: syncTime ?? DateTime.now(),
      updatedAt: DateTime.now(),
      errorMessage: null,
    );
  }

  /// 标记为同步失败
  OfflineData markAsFailed({
    String? errorMessage,
    bool incrementRetry = true,
  }) {
    return copyWith(
      status: OfflineDataStatus.failed,
      errorMessage: errorMessage,
      retryCount: incrementRetry ? retryCount + 1 : retryCount,
      updatedAt: DateTime.now(),
    );
  }

  /// 标记为冲突
  OfflineData markAsConflict({
    Map<String, dynamic>? conflictData,
    int? serverVersion,
  }) {
    return copyWith(
      status: OfflineDataStatus.conflict,
      conflictData: conflictData,
      serverVersion: serverVersion,
      updatedAt: DateTime.now(),
    );
  }

  /// 解决冲突
  OfflineData resolveConflict({
    required Map<String, dynamic> resolvedData,
    int? newVersion,
  }) {
    return copyWith(
      status: OfflineDataStatus.pending,
      data: resolvedData,
      version: newVersion ?? version + 1,
      conflictData: null,
      updatedAt: DateTime.now(),
    );
  }

  /// 复制并修改数据
  OfflineData copyWith({
    String? id,
    String? dataType,
    OfflineOperationType? operationType,
    OfflineDataStatus? status,
    Map<String, dynamic>? data,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastSyncAt,
    int? version,
    int? serverVersion,
    String? userId,
    String? deviceId,
    int? priority,
    int? retryCount,
    int? maxRetries,
    String? errorMessage,
    Map<String, dynamic>? conflictData,
    DateTime? expiresAt,
    bool? requiresNetwork,
    List<String>? dependencies,
  }) {
    return OfflineData(
      id: id ?? this.id,
      dataType: dataType ?? this.dataType,
      operationType: operationType ?? this.operationType,
      status: status ?? this.status,
      data: data ?? this.data,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastSyncAt: lastSyncAt ?? this.lastSyncAt,
      version: version ?? this.version,
      serverVersion: serverVersion ?? this.serverVersion,
      userId: userId ?? this.userId,
      deviceId: deviceId ?? this.deviceId,
      priority: priority ?? this.priority,
      retryCount: retryCount ?? this.retryCount,
      maxRetries: maxRetries ?? this.maxRetries,
      errorMessage: errorMessage ?? this.errorMessage,
      conflictData: conflictData ?? this.conflictData,
      expiresAt: expiresAt ?? this.expiresAt,
      requiresNetwork: requiresNetwork ?? this.requiresNetwork,
      dependencies: dependencies ?? this.dependencies,
    );
  }

  /// 转换为 JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'dataType': dataType,
      'operationType': operationType.name,
      'status': status.name,
      'data': data,
      'metadata': metadata,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'lastSyncAt': lastSyncAt?.toIso8601String(),
      'version': version,
      'serverVersion': serverVersion,
      'userId': userId,
      'deviceId': deviceId,
      'priority': priority,
      'retryCount': retryCount,
      'maxRetries': maxRetries,
      'errorMessage': errorMessage,
      'conflictData': conflictData,
      'expiresAt': expiresAt?.toIso8601String(),
      'requiresNetwork': requiresNetwork,
      'dependencies': dependencies,
    };
  }

  /// 从 JSON 创建
  factory OfflineData.fromJson(Map<String, dynamic> json) {
    return OfflineData(
      id: json['id'] as String,
      dataType: json['dataType'] as String,
      operationType: OfflineOperationType.values.firstWhere(
        (e) => e.name == json['operationType'],
        orElse: () => OfflineOperationType.sync,
      ),
      status: OfflineDataStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => OfflineDataStatus.pending,
      ),
      data: json['data'] as Map<String, dynamic>,
      metadata: json['metadata'] as Map<String, dynamic>? ?? {},
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      lastSyncAt: json['lastSyncAt'] != null
          ? DateTime.parse(json['lastSyncAt'] as String)
          : null,
      version: json['version'] as int? ?? 1,
      serverVersion: json['serverVersion'] as int?,
      userId: json['userId'] as String?,
      deviceId: json['deviceId'] as String?,
      priority: json['priority'] as int? ?? 0,
      retryCount: json['retryCount'] as int? ?? 0,
      maxRetries: json['maxRetries'] as int? ?? 3,
      errorMessage: json['errorMessage'] as String?,
      conflictData: json['conflictData'] as Map<String, dynamic>?,
      expiresAt: json['expiresAt'] != null
          ? DateTime.parse(json['expiresAt'] as String)
          : null,
      requiresNetwork: json['requiresNetwork'] as bool? ?? true,
      dependencies: json['dependencies'] != null
          ? List<String>.from(json['dependencies'] as List)
          : null,
    );
  }

  @override
  List<Object?> get props => [
        id,
        dataType,
        operationType,
        status,
        data,
        metadata,
        createdAt,
        updatedAt,
        lastSyncAt,
        version,
        serverVersion,
        userId,
        deviceId,
        priority,
        retryCount,
        maxRetries,
        errorMessage,
        conflictData,
        expiresAt,
        requiresNetwork,
        dependencies,
      ];

  @override
  String toString() {
    return 'OfflineData(id: $id, type: $dataType, operation: $operationType, status: $status)';
  }
}
