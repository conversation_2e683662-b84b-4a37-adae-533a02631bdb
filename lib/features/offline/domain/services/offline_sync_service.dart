import '../entities/offline_data.dart';
import '../entities/offline_config.dart';

/// 离线同步服务接口
/// 
/// **功能依赖**: 需要启用 offline 模块
/// **配置项**: FEATURE_OFFLINE
abstract class IOfflineSyncService {
  /// 初始化同步服务
  Future<void> initialize({
    OfflineConfig? config,
  });
  
  /// 开始同步
  Future<void> startSync();
  
  /// 停止同步
  Future<void> stopSync();
  
  /// 暂停同步
  Future<void> pauseSync();
  
  /// 恢复同步
  Future<void> resumeSync();
  
  /// 手动触发同步
  Future<void> triggerSync({
    String? dataType,
    bool forceSync = false,
  });
  
  /// 同步指定数据
  Future<void> syncData(OfflineData data);
  
  /// 批量同步数据
  Future<void> syncDataBatch(List<OfflineData> dataList);
  
  /// 同步指定类型的数据
  Future<void> syncDataType(String dataType);
  
  /// 增量同步
  Future<void> incrementalSync({
    String? dataType,
    DateTime? since,
  });
  
  /// 全量同步
  Future<void> fullSync({
    String? dataType,
  });
  
  /// 检查同步状态
  Future<bool> isSyncing();
  
  /// 获取同步进度
  Future<Map<String, dynamic>> getSyncProgress();
  
  /// 获取同步统计
  Future<Map<String, dynamic>> getSyncStats();
  
  /// 获取最后同步时间
  Future<DateTime?> getLastSyncTime({
    String? dataType,
  });
  
  /// 设置同步监听器
  void setSyncListener(SyncListener listener);
  
  /// 移除同步监听器
  void removeSyncListener();
  
  /// 解决数据冲突
  Future<void> resolveConflict({
    required String dataId,
    required ConflictResolutionStrategy strategy,
    Map<String, dynamic>? resolvedData,
  });
  
  /// 批量解决冲突
  Future<void> resolveConflictsBatch({
    required List<String> dataIds,
    required ConflictResolutionStrategy strategy,
  });
  
  /// 获取冲突数据
  Future<List<OfflineData>> getConflictData({
    String? dataType,
  });
  
  /// 重试失败的同步
  Future<void> retryFailedSync({
    String? dataType,
    List<String>? dataIds,
  });
  
  /// 取消同步
  Future<void> cancelSync({
    String? dataType,
    List<String>? dataIds,
  });
  
  /// 清理同步队列
  Future<void> clearSyncQueue();
  
  /// 获取同步队列大小
  Future<int> getSyncQueueSize();
  
  /// 设置网络状态监听
  void setNetworkStatusListener(NetworkStatusListener listener);
  
  /// 处理网络状态变化
  Future<void> handleNetworkStatusChange(bool isConnected);
  
  /// 检查是否可以同步
  Future<bool> canSync({
    String? dataType,
    bool checkNetwork = true,
    bool checkConfig = true,
  });
  
  /// 估算同步时间
  Future<Duration> estimateSyncTime({
    String? dataType,
    int? dataCount,
  });
  
  /// 估算同步数据量
  Future<int> estimateSyncDataSize({
    String? dataType,
  });
  
  /// 优化同步性能
  Future<void> optimizeSyncPerformance();
  
  /// 获取同步配置
  Future<OfflineConfig> getSyncConfig();
  
  /// 更新同步配置
  Future<void> updateSyncConfig(OfflineConfig config);
  
  /// 重置同步状态
  Future<void> resetSyncState();
  
  /// 导出同步日志
  Future<List<Map<String, dynamic>>> exportSyncLogs({
    DateTime? startTime,
    DateTime? endTime,
  });
  
  /// 清理同步日志
  Future<void> clearSyncLogs({
    DateTime? before,
  });
}

/// 同步监听器
abstract class SyncListener {
  /// 同步开始
  void onSyncStarted();
  
  /// 同步进度更新
  void onSyncProgress(double progress, String? message);
  
  /// 同步完成
  void onSyncCompleted(Map<String, dynamic> result);
  
  /// 同步失败
  void onSyncFailed(String error);
  
  /// 同步暂停
  void onSyncPaused();
  
  /// 同步恢复
  void onSyncResumed();
  
  /// 数据冲突
  void onConflictDetected(OfflineData data);
  
  /// 冲突解决
  void onConflictResolved(String dataId);
}

/// 网络状态监听器
abstract class NetworkStatusListener {
  /// 网络连接
  void onNetworkConnected();
  
  /// 网络断开
  void onNetworkDisconnected();
  
  /// 网络类型变化
  void onNetworkTypeChanged(String networkType);
}
