import '../entities/offline_data.dart';
import '../entities/offline_config.dart';

/// 离线数据仓库接口
/// 
/// **功能依赖**: 需要启用 offline 模块
/// **配置项**: FEATURE_OFFLINE
abstract class IOfflineRepository {
  /// 保存离线数据
  Future<void> saveOfflineData(OfflineData data);
  
  /// 批量保存离线数据
  Future<void> saveOfflineDataBatch(List<OfflineData> dataList);
  
  /// 获取离线数据
  Future<OfflineData?> getOfflineData(String id);
  
  /// 获取指定类型的离线数据
  Future<List<OfflineData>> getOfflineDataByType(
    String dataType, {
    OfflineDataStatus? status,
    int? limit,
    int? offset,
  });
  
  /// 获取待同步的数据
  Future<List<OfflineData>> getPendingSyncData({
    String? dataType,
    int? limit,
    bool priorityFirst = true,
  });
  
  /// 获取同步失败的数据
  Future<List<OfflineData>> getFailedSyncData({
    String? dataType,
    int? limit,
  });
  
  /// 获取有冲突的数据
  Future<List<OfflineData>> getConflictData({
    String? dataType,
    int? limit,
  });
  
  /// 更新离线数据状态
  Future<void> updateOfflineDataStatus(
    String id,
    OfflineDataStatus status, {
    String? errorMessage,
    Map<String, dynamic>? conflictData,
    int? serverVersion,
    DateTime? syncTime,
  });
  
  /// 删除离线数据
  Future<void> deleteOfflineData(String id);
  
  /// 批量删除离线数据
  Future<void> deleteOfflineDataBatch(List<String> ids);
  
  /// 清理过期数据
  Future<void> cleanupExpiredData(DateTime before);
  
  /// 清理已同步数据
  Future<void> cleanupSyncedData({
    DateTime? before,
    int? keepCount,
  });
  
  /// 获取离线数据统计
  Future<Map<String, dynamic>> getOfflineDataStats();
  
  /// 获取数据存储大小
  Future<int> getDataStorageSize();
  
  /// 压缩离线数据
  Future<void> compressOfflineData();
  
  /// 获取离线配置
  Future<OfflineConfig> getOfflineConfig();
  
  /// 更新离线配置
  Future<void> updateOfflineConfig(OfflineConfig config);
  
  /// 重置离线配置为默认值
  Future<void> resetOfflineConfig();
  
  /// 导出离线数据
  Future<Map<String, dynamic>> exportOfflineData({
    String? dataType,
    OfflineDataStatus? status,
    DateTime? startTime,
    DateTime? endTime,
  });
  
  /// 导入离线数据
  Future<void> importOfflineData(Map<String, dynamic> data);
  
  /// 备份离线数据
  Future<String> backupOfflineData();
  
  /// 恢复离线数据
  Future<void> restoreOfflineData(String backupData);
  
  /// 验证数据完整性
  Future<bool> validateDataIntegrity();
  
  /// 修复损坏的数据
  Future<void> repairCorruptedData();
}
