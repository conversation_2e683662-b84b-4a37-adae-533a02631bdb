import 'package:flutter/material.dart';
import '../entities/app_theme.dart';

/// 主题仓库接口
/// 
/// **功能依赖**: 需要启用 theming 模块
/// **配置项**: FEATURE_THEMING
abstract class IThemeRepository {
  /// 获取当前主题模式
  Future<ThemeMode> getThemeMode();
  
  /// 设置主题模式
  Future<void> setThemeMode(ThemeMode mode);
  
  /// 获取当前主题ID
  Future<String> getCurrentThemeId();
  
  /// 设置当前主题ID
  Future<void> setCurrentThemeId(String themeId);
  
  /// 获取所有可用主题
  Future<List<AppTheme>> getAvailableThemes();
  
  /// 获取主题配置
  Future<AppTheme?> getTheme(String themeId);
  
  /// 保存自定义主题
  Future<void> saveCustomTheme(AppTheme theme);
  
  /// 删除自定义主题
  Future<void> deleteCustomTheme(String themeId);
  
  /// 导入主题
  Future<void> importTheme(Map<String, dynamic> themeData);
  
  /// 导出主题
  Future<Map<String, dynamic>> exportTheme(String themeId);
  
  /// 重置为默认主题
  Future<void> resetToDefault();
  
  /// 获取主题变更流
  Stream<ThemeMode> get themeModeStream;
  
  /// 获取主题ID变更流
  Stream<String> get themeIdStream;
}
