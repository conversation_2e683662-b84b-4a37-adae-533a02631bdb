import 'package:flutter/material.dart';
import 'package:equatable/equatable.dart';

/// 应用主题实体
/// 
/// **功能依赖**: 需要启用 theming 模块
/// **配置项**: FEATURE_THEMING
class AppTheme extends Equatable {
  /// 主题ID
  final String id;
  
  /// 主题名称
  final String name;
  
  /// 主题描述
  final String? description;
  
  /// 是否为内置主题
  final bool isBuiltIn;
  
  /// 是否为深色主题
  final bool isDark;
  
  /// 主色调
  final Color primaryColor;
  
  /// 次要颜色
  final Color? secondaryColor;
  
  /// 背景颜色
  final Color? backgroundColor;
  
  /// 表面颜色
  final Color? surfaceColor;
  
  /// 错误颜色
  final Color? errorColor;
  
  /// 字体家族
  final String? fontFamily;
  
  /// 创建时间
  final DateTime createdAt;
  
  /// 更新时间
  final DateTime updatedAt;
  
  /// 扩展属性
  final Map<String, dynamic>? extensions;

  const AppTheme({
    required this.id,
    required this.name,
    this.description,
    required this.isBuiltIn,
    required this.isDark,
    required this.primaryColor,
    this.secondaryColor,
    this.backgroundColor,
    this.surfaceColor,
    this.errorColor,
    this.fontFamily,
    required this.createdAt,
    required this.updatedAt,
    this.extensions,
  });

  /// 创建默认浅色主题
  factory AppTheme.defaultLight() {
    final now = DateTime.now();
    return AppTheme(
      id: 'default_light',
      name: '默认浅色',
      description: '系统默认浅色主题',
      isBuiltIn: true,
      isDark: false,
      primaryColor: const Color(0xFF1976D2),
      secondaryColor: const Color(0xFF03DAC6),
      backgroundColor: const Color(0xFFFAFAFA),
      surfaceColor: const Color(0xFFFFFFFF),
      errorColor: const Color(0xFFB00020),
      createdAt: now,
      updatedAt: now,
    );
  }

  /// 创建默认深色主题
  factory AppTheme.defaultDark() {
    final now = DateTime.now();
    return AppTheme(
      id: 'default_dark',
      name: '默认深色',
      description: '系统默认深色主题',
      isBuiltIn: true,
      isDark: true,
      primaryColor: const Color(0xFF90CAF9),
      secondaryColor: const Color(0xFF4DB6AC),
      backgroundColor: const Color(0xFF121212),
      surfaceColor: const Color(0xFF1E1E1E),
      errorColor: const Color(0xFFCF6679),
      createdAt: now,
      updatedAt: now,
    );
  }

  /// 从颜色种子创建主题
  factory AppTheme.fromSeed({
    required String id,
    required String name,
    String? description,
    required Color seedColor,
    required bool isDark,
    String? fontFamily,
    Map<String, dynamic>? extensions,
  }) {
    final now = DateTime.now();
    final colorScheme = ColorScheme.fromSeed(
      seedColor: seedColor,
      brightness: isDark ? Brightness.dark : Brightness.light,
    );
    
    return AppTheme(
      id: id,
      name: name,
      description: description,
      isBuiltIn: false,
      isDark: isDark,
      primaryColor: colorScheme.primary,
      secondaryColor: colorScheme.secondary,
      backgroundColor: colorScheme.background,
      surfaceColor: colorScheme.surface,
      errorColor: colorScheme.error,
      fontFamily: fontFamily,
      createdAt: now,
      updatedAt: now,
      extensions: extensions,
    );
  }

  /// 转换为 Flutter ThemeData
  ThemeData toThemeData() {
    final colorScheme = ColorScheme.fromSeed(
      seedColor: primaryColor,
      brightness: isDark ? Brightness.dark : Brightness.light,
      secondary: secondaryColor,
      background: backgroundColor,
      surface: surfaceColor,
      error: errorColor,
    );

    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      fontFamily: fontFamily,
      brightness: isDark ? Brightness.dark : Brightness.light,
    );
  }

  /// 复制并修改主题
  AppTheme copyWith({
    String? id,
    String? name,
    String? description,
    bool? isBuiltIn,
    bool? isDark,
    Color? primaryColor,
    Color? secondaryColor,
    Color? backgroundColor,
    Color? surfaceColor,
    Color? errorColor,
    String? fontFamily,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? extensions,
  }) {
    return AppTheme(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      isBuiltIn: isBuiltIn ?? this.isBuiltIn,
      isDark: isDark ?? this.isDark,
      primaryColor: primaryColor ?? this.primaryColor,
      secondaryColor: secondaryColor ?? this.secondaryColor,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      surfaceColor: surfaceColor ?? this.surfaceColor,
      errorColor: errorColor ?? this.errorColor,
      fontFamily: fontFamily ?? this.fontFamily,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      extensions: extensions ?? this.extensions,
    );
  }

  /// 转换为 JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'isBuiltIn': isBuiltIn,
      'isDark': isDark,
      'primaryColor': primaryColor.value,
      'secondaryColor': secondaryColor?.value,
      'backgroundColor': backgroundColor?.value,
      'surfaceColor': surfaceColor?.value,
      'errorColor': errorColor?.value,
      'fontFamily': fontFamily,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'extensions': extensions,
    };
  }

  /// 从 JSON 创建
  factory AppTheme.fromJson(Map<String, dynamic> json) {
    return AppTheme(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      isBuiltIn: json['isBuiltIn'] as bool? ?? false,
      isDark: json['isDark'] as bool,
      primaryColor: Color(json['primaryColor'] as int),
      secondaryColor: json['secondaryColor'] != null 
          ? Color(json['secondaryColor'] as int) 
          : null,
      backgroundColor: json['backgroundColor'] != null 
          ? Color(json['backgroundColor'] as int) 
          : null,
      surfaceColor: json['surfaceColor'] != null 
          ? Color(json['surfaceColor'] as int) 
          : null,
      errorColor: json['errorColor'] != null 
          ? Color(json['errorColor'] as int) 
          : null,
      fontFamily: json['fontFamily'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      extensions: json['extensions'] as Map<String, dynamic>?,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        isBuiltIn,
        isDark,
        primaryColor,
        secondaryColor,
        backgroundColor,
        surfaceColor,
        errorColor,
        fontFamily,
        createdAt,
        updatedAt,
        extensions,
      ];

  @override
  String toString() {
    return 'AppTheme(id: $id, name: $name, isDark: $isDark, primaryColor: $primaryColor)';
  }
}

/// 主题预设
class ThemePresets {
  ThemePresets._();

  /// 获取所有内置主题
  static List<AppTheme> getBuiltInThemes() {
    return [
      AppTheme.defaultLight(),
      AppTheme.defaultDark(),
      _createBlueTheme(),
      _createGreenTheme(),
      _createPurpleTheme(),
      _createOrangeTheme(),
      _createRedTheme(),
      _createTealTheme(),
    ];
  }

  static AppTheme _createBlueTheme() {
    final now = DateTime.now();
    return AppTheme(
      id: 'blue_theme',
      name: '蓝色主题',
      description: '清新的蓝色主题',
      isBuiltIn: true,
      isDark: false,
      primaryColor: Colors.blue,
      secondaryColor: Colors.lightBlue,
      createdAt: now,
      updatedAt: now,
    );
  }

  static AppTheme _createGreenTheme() {
    final now = DateTime.now();
    return AppTheme(
      id: 'green_theme',
      name: '绿色主题',
      description: '自然的绿色主题',
      isBuiltIn: true,
      isDark: false,
      primaryColor: Colors.green,
      secondaryColor: Colors.lightGreen,
      createdAt: now,
      updatedAt: now,
    );
  }

  static AppTheme _createPurpleTheme() {
    final now = DateTime.now();
    return AppTheme(
      id: 'purple_theme',
      name: '紫色主题',
      description: '优雅的紫色主题',
      isBuiltIn: true,
      isDark: false,
      primaryColor: Colors.purple,
      secondaryColor: Colors.deepPurple,
      createdAt: now,
      updatedAt: now,
    );
  }

  static AppTheme _createOrangeTheme() {
    final now = DateTime.now();
    return AppTheme(
      id: 'orange_theme',
      name: '橙色主题',
      description: '温暖的橙色主题',
      isBuiltIn: true,
      isDark: false,
      primaryColor: Colors.orange,
      secondaryColor: Colors.deepOrange,
      createdAt: now,
      updatedAt: now,
    );
  }

  static AppTheme _createRedTheme() {
    final now = DateTime.now();
    return AppTheme(
      id: 'red_theme',
      name: '红色主题',
      description: '热情的红色主题',
      isBuiltIn: true,
      isDark: false,
      primaryColor: Colors.red,
      secondaryColor: Colors.pink,
      createdAt: now,
      updatedAt: now,
    );
  }

  static AppTheme _createTealTheme() {
    final now = DateTime.now();
    return AppTheme(
      id: 'teal_theme',
      name: '青色主题',
      description: '清雅的青色主题',
      isBuiltIn: true,
      isDark: false,
      primaryColor: Colors.teal,
      secondaryColor: Colors.cyan,
      createdAt: now,
      updatedAt: now,
    );
  }
}
