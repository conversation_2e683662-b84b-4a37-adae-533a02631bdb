import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import '../../../../core/database/database_service.dart';
import '../../domain/entities/app_theme.dart';
import '../../domain/repositories/theme_repository.dart';

/// 主题仓库实现
/// 
/// **功能依赖**: 需要启用 theming 模块
/// **配置项**: FEATURE_THEMING
@Injectable(as: IThemeRepository)
class ThemeRepositoryImpl implements IThemeRepository {
  final IDatabaseService _databaseService;
  
  // 流控制器
  final StreamController<ThemeMode> _themeModeController = StreamController.broadcast();
  final StreamController<String> _themeIdController = StreamController.broadcast();
  
  // 缓存
  ThemeMode? _cachedThemeMode;
  String? _cachedThemeId;
  List<AppTheme>? _cachedThemes;
  
  // 存储键
  static const String _themeModeKey = 'theme_mode';
  static const String _themeIdKey = 'current_theme_id';
  static const String _customThemesKey = 'custom_themes';

  ThemeRepositoryImpl(this._databaseService);

  @override
  Stream<ThemeMode> get themeModeStream => _themeModeController.stream;

  @override
  Stream<String> get themeIdStream => _themeIdController.stream;

  @override
  Future<ThemeMode> getThemeMode() async {
    if (_cachedThemeMode != null) {
      return _cachedThemeMode!;
    }

    try {
      final modeIndex = await _databaseService.get<int>(_themeModeKey);
      if (modeIndex != null && modeIndex >= 0 && modeIndex < ThemeMode.values.length) {
        _cachedThemeMode = ThemeMode.values[modeIndex];
      } else {
        _cachedThemeMode = ThemeMode.system;
      }
    } catch (e) {
      _cachedThemeMode = ThemeMode.system;
    }

    return _cachedThemeMode!;
  }

  @override
  Future<void> setThemeMode(ThemeMode mode) async {
    try {
      await _databaseService.set(_themeModeKey, mode.index);
      _cachedThemeMode = mode;
      _themeModeController.add(mode);
    } catch (e) {
      throw Exception('设置主题模式失败: $e');
    }
  }

  @override
  Future<String> getCurrentThemeId() async {
    if (_cachedThemeId != null) {
      return _cachedThemeId!;
    }

    try {
      _cachedThemeId = await _databaseService.get<String>(_themeIdKey) ?? 'default_light';
    } catch (e) {
      _cachedThemeId = 'default_light';
    }

    return _cachedThemeId!;
  }

  @override
  Future<void> setCurrentThemeId(String themeId) async {
    try {
      await _databaseService.set(_themeIdKey, themeId);
      _cachedThemeId = themeId;
      _themeIdController.add(themeId);
    } catch (e) {
      throw Exception('设置当前主题失败: $e');
    }
  }

  @override
  Future<List<AppTheme>> getAvailableThemes() async {
    if (_cachedThemes != null) {
      return _cachedThemes!;
    }

    try {
      // 获取内置主题
      final builtInThemes = ThemePresets.getBuiltInThemes();
      
      // 获取自定义主题
      final customThemes = await _getCustomThemes();
      
      _cachedThemes = [...builtInThemes, ...customThemes];
      return _cachedThemes!;
    } catch (e) {
      throw Exception('获取可用主题失败: $e');
    }
  }

  @override
  Future<AppTheme?> getTheme(String themeId) async {
    try {
      final themes = await getAvailableThemes();
      return themes.firstWhere(
        (theme) => theme.id == themeId,
        orElse: () => ThemePresets.getBuiltInThemes().first,
      );
    } catch (e) {
      return null;
    }
  }

  @override
  Future<void> saveCustomTheme(AppTheme theme) async {
    try {
      final customThemes = await _getCustomThemes();
      
      // 检查是否已存在
      final existingIndex = customThemes.indexWhere((t) => t.id == theme.id);
      if (existingIndex >= 0) {
        customThemes[existingIndex] = theme.copyWith(updatedAt: DateTime.now());
      } else {
        customThemes.add(theme);
      }
      
      await _saveCustomThemes(customThemes);
      _cachedThemes = null; // 清除缓存
    } catch (e) {
      throw Exception('保存自定义主题失败: $e');
    }
  }

  @override
  Future<void> deleteCustomTheme(String themeId) async {
    try {
      final customThemes = await _getCustomThemes();
      customThemes.removeWhere((theme) => theme.id == themeId);
      
      await _saveCustomThemes(customThemes);
      _cachedThemes = null; // 清除缓存
      
      // 如果删除的是当前主题，切换到默认主题
      final currentThemeId = await getCurrentThemeId();
      if (currentThemeId == themeId) {
        await setCurrentThemeId('default_light');
      }
    } catch (e) {
      throw Exception('删除自定义主题失败: $e');
    }
  }

  @override
  Future<void> importTheme(Map<String, dynamic> themeData) async {
    try {
      final theme = AppTheme.fromJson(themeData);
      await saveCustomTheme(theme);
    } catch (e) {
      throw Exception('导入主题失败: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> exportTheme(String themeId) async {
    try {
      final theme = await getTheme(themeId);
      if (theme == null) {
        throw Exception('主题不存在: $themeId');
      }
      return theme.toJson();
    } catch (e) {
      throw Exception('导出主题失败: $e');
    }
  }

  @override
  Future<void> resetToDefault() async {
    try {
      await setThemeMode(ThemeMode.system);
      await setCurrentThemeId('default_light');
      
      // 清除所有自定义主题
      await _databaseService.delete(_customThemesKey);
      _cachedThemes = null;
    } catch (e) {
      throw Exception('重置主题失败: $e');
    }
  }

  /// 获取自定义主题列表
  Future<List<AppTheme>> _getCustomThemes() async {
    try {
      final themesJson = await _databaseService.get<String>(_customThemesKey);
      if (themesJson == null) return [];
      
      final List<dynamic> themesList = json.decode(themesJson);
      return themesList
          .map((themeJson) => AppTheme.fromJson(themeJson as Map<String, dynamic>))
          .toList();
    } catch (e) {
      return [];
    }
  }

  /// 保存自定义主题列表
  Future<void> _saveCustomThemes(List<AppTheme> themes) async {
    try {
      final themesJson = json.encode(themes.map((theme) => theme.toJson()).toList());
      await _databaseService.set(_customThemesKey, themesJson);
    } catch (e) {
      throw Exception('保存自定义主题列表失败: $e');
    }
  }

  /// 清理资源
  void dispose() {
    _themeModeController.close();
    _themeIdController.close();
  }
}

/// NoOp主题仓库实现
/// 
/// 当theming模块禁用时使用的空实现
/// 所有主题操作返回默认值，不执行实际主题逻辑
@Injectable(as: IThemeRepository)
@Environment('noop')
class NoOpThemeRepository implements IThemeRepository {
  const NoOpThemeRepository();

  @override
  Future<ThemeMode> getThemeMode() async => ThemeMode.system;

  @override
  Future<void> setThemeMode(ThemeMode mode) async {
    // NoOp: 主题功能未启用
  }

  @override
  Future<String> getCurrentThemeId() async => 'default_light';

  @override
  Future<void> setCurrentThemeId(String themeId) async {
    // NoOp: 主题功能未启用
  }

  @override
  Future<List<AppTheme>> getAvailableThemes() async => [AppTheme.defaultLight()];

  @override
  Future<AppTheme?> getTheme(String themeId) async => AppTheme.defaultLight();

  @override
  Future<void> saveCustomTheme(AppTheme theme) async {
    // NoOp: 主题功能未启用
  }

  @override
  Future<void> deleteCustomTheme(String themeId) async {
    // NoOp: 主题功能未启用
  }

  @override
  Future<void> importTheme(Map<String, dynamic> themeData) async {
    // NoOp: 主题功能未启用
  }

  @override
  Future<Map<String, dynamic>> exportTheme(String themeId) async => {};

  @override
  Future<void> resetToDefault() async {
    // NoOp: 主题功能未启用
  }

  @override
  Stream<ThemeMode> get themeModeStream => Stream.value(ThemeMode.system);

  @override
  Stream<String> get themeIdStream => Stream.value('default_light');
}
