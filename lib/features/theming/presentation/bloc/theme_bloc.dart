import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import '../../domain/entities/app_theme.dart';
import '../../domain/repositories/theme_repository.dart';

// Events
abstract class ThemeEvent extends Equatable {
  const ThemeEvent();

  @override
  List<Object?> get props => [];
}

class ThemeLoadRequested extends ThemeEvent {
  const ThemeLoadRequested();
}

class ThemeModeChanged extends ThemeEvent {
  final ThemeMode themeMode;

  const ThemeModeChanged(this.themeMode);

  @override
  List<Object> get props => [themeMode];
}

class ThemeChanged extends ThemeEvent {
  final String themeId;

  const ThemeChanged(this.themeId);

  @override
  List<Object> get props => [themeId];
}

class CustomThemeSaved extends ThemeEvent {
  final AppTheme theme;

  const CustomThemeSaved(this.theme);

  @override
  List<Object> get props => [theme];
}

class CustomThemeDeleted extends ThemeEvent {
  final String themeId;

  const CustomThemeDeleted(this.themeId);

  @override
  List<Object> get props => [themeId];
}

class ThemeImported extends ThemeEvent {
  final Map<String, dynamic> themeData;

  const ThemeImported(this.themeData);

  @override
  List<Object> get props => [themeData];
}

class ThemeExported extends ThemeEvent {
  final String themeId;

  const ThemeExported(this.themeId);

  @override
  List<Object> get props => [themeId];
}

class ThemeResetRequested extends ThemeEvent {
  const ThemeResetRequested();
}

// States
abstract class ThemeState extends Equatable {
  const ThemeState();

  @override
  List<Object?> get props => [];
}

class ThemeInitial extends ThemeState {
  const ThemeInitial();
}

class ThemeLoading extends ThemeState {
  const ThemeLoading();
}

class ThemeLoaded extends ThemeState {
  final ThemeMode themeMode;
  final String currentThemeId;
  final AppTheme currentTheme;
  final List<AppTheme> availableThemes;

  const ThemeLoaded({
    required this.themeMode,
    required this.currentThemeId,
    required this.currentTheme,
    required this.availableThemes,
  });

  @override
  List<Object> get props => [themeMode, currentThemeId, currentTheme, availableThemes];

  ThemeLoaded copyWith({
    ThemeMode? themeMode,
    String? currentThemeId,
    AppTheme? currentTheme,
    List<AppTheme>? availableThemes,
  }) {
    return ThemeLoaded(
      themeMode: themeMode ?? this.themeMode,
      currentThemeId: currentThemeId ?? this.currentThemeId,
      currentTheme: currentTheme ?? this.currentTheme,
      availableThemes: availableThemes ?? this.availableThemes,
    );
  }
}

class ThemeError extends ThemeState {
  final String message;

  const ThemeError(this.message);

  @override
  List<Object> get props => [message];
}

class ThemeOperationSuccess extends ThemeState {
  final String message;
  final dynamic data;

  const ThemeOperationSuccess(this.message, {this.data});

  @override
  List<Object?> get props => [message, data];
}

// BLoC
@injectable
class ThemeBloc extends Bloc<ThemeEvent, ThemeState> {
  final IThemeRepository _themeRepository;
  StreamSubscription<ThemeMode>? _themeModeSubscription;
  StreamSubscription<String>? _themeIdSubscription;

  ThemeBloc(this._themeRepository) : super(const ThemeInitial()) {
    on<ThemeLoadRequested>(_onThemeLoadRequested);
    on<ThemeModeChanged>(_onThemeModeChanged);
    on<ThemeChanged>(_onThemeChanged);
    on<CustomThemeSaved>(_onCustomThemeSaved);
    on<CustomThemeDeleted>(_onCustomThemeDeleted);
    on<ThemeImported>(_onThemeImported);
    on<ThemeExported>(_onThemeExported);
    on<ThemeResetRequested>(_onThemeResetRequested);

    // 监听主题变化
    _themeModeSubscription = _themeRepository.themeModeStream.listen(
      (themeMode) => add(ThemeModeChanged(themeMode)),
    );

    _themeIdSubscription = _themeRepository.themeIdStream.listen(
      (themeId) => add(ThemeChanged(themeId)),
    );
  }

  Future<void> _onThemeLoadRequested(
    ThemeLoadRequested event,
    Emitter<ThemeState> emit,
  ) async {
    try {
      emit(const ThemeLoading());

      final themeMode = await _themeRepository.getThemeMode();
      final currentThemeId = await _themeRepository.getCurrentThemeId();
      final availableThemes = await _themeRepository.getAvailableThemes();
      final currentTheme = await _themeRepository.getTheme(currentThemeId) 
          ?? AppTheme.defaultLight();

      emit(ThemeLoaded(
        themeMode: themeMode,
        currentThemeId: currentThemeId,
        currentTheme: currentTheme,
        availableThemes: availableThemes,
      ));
    } catch (e) {
      emit(ThemeError('加载主题失败: $e'));
    }
  }

  Future<void> _onThemeModeChanged(
    ThemeModeChanged event,
    Emitter<ThemeState> emit,
  ) async {
    try {
      await _themeRepository.setThemeMode(event.themeMode);

      if (state is ThemeLoaded) {
        final currentState = state as ThemeLoaded;
        emit(currentState.copyWith(themeMode: event.themeMode));
      }
    } catch (e) {
      emit(ThemeError('设置主题模式失败: $e'));
    }
  }

  Future<void> _onThemeChanged(
    ThemeChanged event,
    Emitter<ThemeState> emit,
  ) async {
    try {
      await _themeRepository.setCurrentThemeId(event.themeId);

      if (state is ThemeLoaded) {
        final currentState = state as ThemeLoaded;
        final newTheme = await _themeRepository.getTheme(event.themeId) 
            ?? AppTheme.defaultLight();

        emit(currentState.copyWith(
          currentThemeId: event.themeId,
          currentTheme: newTheme,
        ));
      }
    } catch (e) {
      emit(ThemeError('切换主题失败: $e'));
    }
  }

  Future<void> _onCustomThemeSaved(
    CustomThemeSaved event,
    Emitter<ThemeState> emit,
  ) async {
    try {
      await _themeRepository.saveCustomTheme(event.theme);

      // 重新加载可用主题
      final availableThemes = await _themeRepository.getAvailableThemes();

      if (state is ThemeLoaded) {
        final currentState = state as ThemeLoaded;
        emit(currentState.copyWith(availableThemes: availableThemes));
      }

      emit(const ThemeOperationSuccess('自定义主题保存成功'));
    } catch (e) {
      emit(ThemeError('保存自定义主题失败: $e'));
    }
  }

  Future<void> _onCustomThemeDeleted(
    CustomThemeDeleted event,
    Emitter<ThemeState> emit,
  ) async {
    try {
      await _themeRepository.deleteCustomTheme(event.themeId);

      // 重新加载可用主题
      final availableThemes = await _themeRepository.getAvailableThemes();
      final currentThemeId = await _themeRepository.getCurrentThemeId();
      final currentTheme = await _themeRepository.getTheme(currentThemeId) 
          ?? AppTheme.defaultLight();

      if (state is ThemeLoaded) {
        final currentState = state as ThemeLoaded;
        emit(currentState.copyWith(
          currentThemeId: currentThemeId,
          currentTheme: currentTheme,
          availableThemes: availableThemes,
        ));
      }

      emit(const ThemeOperationSuccess('自定义主题删除成功'));
    } catch (e) {
      emit(ThemeError('删除自定义主题失败: $e'));
    }
  }

  Future<void> _onThemeImported(
    ThemeImported event,
    Emitter<ThemeState> emit,
  ) async {
    try {
      await _themeRepository.importTheme(event.themeData);

      // 重新加载可用主题
      final availableThemes = await _themeRepository.getAvailableThemes();

      if (state is ThemeLoaded) {
        final currentState = state as ThemeLoaded;
        emit(currentState.copyWith(availableThemes: availableThemes));
      }

      emit(const ThemeOperationSuccess('主题导入成功'));
    } catch (e) {
      emit(ThemeError('导入主题失败: $e'));
    }
  }

  Future<void> _onThemeExported(
    ThemeExported event,
    Emitter<ThemeState> emit,
  ) async {
    try {
      final themeData = await _themeRepository.exportTheme(event.themeId);
      emit(ThemeOperationSuccess('主题导出成功', data: themeData));
    } catch (e) {
      emit(ThemeError('导出主题失败: $e'));
    }
  }

  Future<void> _onThemeResetRequested(
    ThemeResetRequested event,
    Emitter<ThemeState> emit,
  ) async {
    try {
      await _themeRepository.resetToDefault();

      // 重新加载主题
      add(const ThemeLoadRequested());

      emit(const ThemeOperationSuccess('主题已重置为默认设置'));
    } catch (e) {
      emit(ThemeError('重置主题失败: $e'));
    }
  }

  @override
  Future<void> close() {
    _themeModeSubscription?.cancel();
    _themeIdSubscription?.cancel();
    return super.close();
  }
}
