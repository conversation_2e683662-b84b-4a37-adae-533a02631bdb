import 'package:flutter/material.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import '../../domain/entities/app_theme.dart';
import '../../../../shared/widgets/buttons/app_button.dart';
import '../../../../shared/widgets/inputs/app_text_field.dart';
import '../../../../shared/widgets/cards/app_card.dart';

/// 动态主题生成器
/// 
/// 允许用户创建和自定义主题
class DynamicThemeGenerator extends StatefulWidget {
  /// 初始主题（用于编辑现有主题）
  final AppTheme? initialTheme;
  
  /// 保存回调
  final void Function(AppTheme theme)? onSave;
  
  /// 预览回调
  final void Function(AppTheme theme)? onPreview;
  
  /// 取消回调
  final VoidCallback? onCancel;

  const DynamicThemeGenerator({
    super.key,
    this.initialTheme,
    this.onSave,
    this.onPreview,
    this.onCancel,
  });

  @override
  State<DynamicThemeGenerator> createState() => _DynamicThemeGeneratorState();
}

class _DynamicThemeGeneratorState extends State<DynamicThemeGenerator> {
  late TextEditingController _nameController;
  late TextEditingController _descriptionController;
  late TextEditingController _fontFamilyController;
  
  late Color _primaryColor;
  late Color _secondaryColor;
  late Color _backgroundColor;
  late Color _surfaceColor;
  late Color _errorColor;
  late bool _isDark;
  
  AppTheme? _previewTheme;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _initializeColors();
    _generatePreviewTheme();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _fontFamilyController.dispose();
    super.dispose();
  }

  void _initializeControllers() {
    _nameController = TextEditingController(
      text: widget.initialTheme?.name ?? '自定义主题',
    );
    _descriptionController = TextEditingController(
      text: widget.initialTheme?.description ?? '',
    );
    _fontFamilyController = TextEditingController(
      text: widget.initialTheme?.fontFamily ?? '',
    );
  }

  void _initializeColors() {
    final theme = widget.initialTheme ?? AppTheme.defaultLight();
    _primaryColor = theme.primaryColor;
    _secondaryColor = theme.secondaryColor ?? Colors.teal;
    _backgroundColor = theme.backgroundColor ?? Colors.white;
    _surfaceColor = theme.surfaceColor ?? Colors.white;
    _errorColor = theme.errorColor ?? Colors.red;
    _isDark = theme.isDark;
  }

  void _generatePreviewTheme() {
    final themeId = widget.initialTheme?.id ?? 'custom_${DateTime.now().millisecondsSinceEpoch}';
    
    _previewTheme = AppTheme(
      id: themeId,
      name: _nameController.text.isNotEmpty ? _nameController.text : '自定义主题',
      description: _descriptionController.text.isNotEmpty ? _descriptionController.text : null,
      isBuiltIn: false,
      isDark: _isDark,
      primaryColor: _primaryColor,
      secondaryColor: _secondaryColor,
      backgroundColor: _backgroundColor,
      surfaceColor: _surfaceColor,
      errorColor: _errorColor,
      fontFamily: _fontFamilyController.text.isNotEmpty ? _fontFamilyController.text : null,
      createdAt: widget.initialTheme?.createdAt ?? DateTime.now(),
      updatedAt: DateTime.now(),
    );
    
    widget.onPreview?.call(_previewTheme!);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.initialTheme != null ? '编辑主题' : '创建主题'),
        actions: [
          TextButton(
            onPressed: _generatePreviewTheme,
            child: const Text('预览'),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildBasicInfo(),
            const SizedBox(height: 24),
            _buildColorSettings(),
            const SizedBox(height: 24),
            _buildFontSettings(),
            const SizedBox(height: 24),
            _buildPreview(),
            const SizedBox(height: 32),
            _buildActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfo() {
    return AppCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '基本信息',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 16),
          AppTextField(
            label: '主题名称',
            controller: _nameController,
            hint: '输入主题名称',
            required: true,
            onChanged: (_) => _generatePreviewTheme(),
          ),
          const SizedBox(height: 16),
          AppTextField.multiline(
            label: '主题描述',
            controller: _descriptionController,
            hint: '输入主题描述（可选）',
            maxLines: 3,
            minLines: 2,
            onChanged: (_) => _generatePreviewTheme(),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Text(
                '深色主题',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const Spacer(),
              Switch(
                value: _isDark,
                onChanged: (value) {
                  setState(() {
                    _isDark = value;
                    // 根据主题模式调整默认颜色
                    if (_isDark) {
                      _backgroundColor = const Color(0xFF121212);
                      _surfaceColor = const Color(0xFF1E1E1E);
                    } else {
                      _backgroundColor = const Color(0xFFFAFAFA);
                      _surfaceColor = const Color(0xFFFFFFFF);
                    }
                  });
                  _generatePreviewTheme();
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildColorSettings() {
    return AppCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '颜色设置',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 16),
          _buildColorPicker('主色调', _primaryColor, (color) {
            setState(() => _primaryColor = color);
            _generatePreviewTheme();
          }),
          const SizedBox(height: 12),
          _buildColorPicker('次要颜色', _secondaryColor, (color) {
            setState(() => _secondaryColor = color);
            _generatePreviewTheme();
          }),
          const SizedBox(height: 12),
          _buildColorPicker('背景颜色', _backgroundColor, (color) {
            setState(() => _backgroundColor = color);
            _generatePreviewTheme();
          }),
          const SizedBox(height: 12),
          _buildColorPicker('表面颜色', _surfaceColor, (color) {
            setState(() => _surfaceColor = color);
            _generatePreviewTheme();
          }),
          const SizedBox(height: 12),
          _buildColorPicker('错误颜色', _errorColor, (color) {
            setState(() => _errorColor = color);
            _generatePreviewTheme();
          }),
        ],
      ),
    );
  }

  Widget _buildColorPicker(String label, Color color, ValueChanged<Color> onChanged) {
    return Row(
      children: [
        Expanded(
          child: Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ),
        GestureDetector(
          onTap: () => _showColorPicker(color, onChanged),
          child: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Theme.of(context).colorScheme.outline,
                width: 1,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _showColorPicker(Color currentColor, ValueChanged<Color> onChanged) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('选择颜色'),
        content: SingleChildScrollView(
          child: ColorPicker(
            pickerColor: currentColor,
            onColorChanged: onChanged,
            pickerAreaHeightPercent: 0.8,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  Widget _buildFontSettings() {
    return AppCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '字体设置',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 16),
          AppTextField(
            label: '字体家族',
            controller: _fontFamilyController,
            hint: '输入字体名称（可选）',
            helperText: '例如：Roboto, Arial, 微软雅黑',
            onChanged: (_) => _generatePreviewTheme(),
          ),
        ],
      ),
    );
  }

  Widget _buildPreview() {
    if (_previewTheme == null) return const SizedBox.shrink();

    return AppCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '主题预览',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 16),
          Theme(
            data: _previewTheme!.toThemeData(),
            child: Builder(
              builder: (context) => Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.background,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outline,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '示例标题',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '这是一段示例文本，用于预览主题效果。',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        ElevatedButton(
                          onPressed: () {},
                          child: const Text('主要按钮'),
                        ),
                        const SizedBox(width: 8),
                        OutlinedButton(
                          onPressed: () {},
                          child: const Text('次要按钮'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActions() {
    return Row(
      children: [
        Expanded(
          child: AppButton.outlined(
            text: '取消',
            onPressed: widget.onCancel ?? () => Navigator.of(context).pop(),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: AppButton.primary(
            text: '保存',
            onPressed: _previewTheme != null 
              ? () => widget.onSave?.call(_previewTheme!)
              : null,
          ),
        ),
      ],
    );
  }
}
