import '../entities/push_notification.dart';
import '../entities/notification_settings.dart';

/// 推送通知仓库接口
/// 
/// **功能依赖**: 需要启用 notifications 模块
/// **配置项**: FEATURE_NOTIFICATIONS
abstract class INotificationRepository {
  /// 发送推送通知
  Future<void> sendNotification(PushNotification notification);
  
  /// 批量发送推送通知
  Future<void> sendNotifications(List<PushNotification> notifications);
  
  /// 计划发送通知
  Future<void> scheduleNotification(PushNotification notification);
  
  /// 取消计划通知
  Future<void> cancelScheduledNotification(String notificationId);
  
  /// 获取通知历史
  Future<List<PushNotification>> getNotificationHistory({
    String? userId,
    NotificationType? type,
    NotificationStatus? status,
    DateTime? startTime,
    DateTime? endTime,
    int? limit,
    int? offset,
  });
  
  /// 获取待发送的通知
  Future<List<PushNotification>> getPendingNotifications({
    int? limit,
  });
  
  /// 获取失败的通知
  Future<List<PushNotification>> getFailedNotifications({
    int? limit,
  });
  
  /// 重试失败的通知
  Future<void> retryFailedNotification(String notificationId);
  
  /// 批量重试失败的通知
  Future<void> retryFailedNotifications(List<String> notificationIds);
  
  /// 更新通知状态
  Future<void> updateNotificationStatus(
    String notificationId,
    NotificationStatus status, {
    DateTime? timestamp,
    String? errorMessage,
  });
  
  /// 删除通知
  Future<void> deleteNotification(String notificationId);
  
  /// 批量删除通知
  Future<void> deleteNotifications(List<String> notificationIds);
  
  /// 获取通知统计
  Future<Map<String, dynamic>> getNotificationStats({
    String? userId,
    DateTime? startTime,
    DateTime? endTime,
  });
  
  /// 获取用户通知设置
  Future<NotificationSettings> getUserNotificationSettings(String userId);
  
  /// 更新用户通知设置
  Future<void> updateUserNotificationSettings(NotificationSettings settings);
  
  /// 注册设备令牌
  Future<void> registerDeviceToken({
    required String userId,
    required String deviceToken,
    String platform = 'fcm',
    Map<String, dynamic>? deviceInfo,
  });
  
  /// 注销设备令牌
  Future<void> unregisterDeviceToken({
    required String userId,
    required String deviceToken,
  });
  
  /// 获取用户设备令牌
  Future<List<String>> getUserDeviceTokens(String userId);
  
  /// 订阅主题
  Future<void> subscribeToTopic({
    required String userId,
    required String topic,
  });
  
  /// 取消订阅主题
  Future<void> unsubscribeFromTopic({
    required String userId,
    required String topic,
  });
  
  /// 获取用户订阅的主题
  Future<List<String>> getUserSubscribedTopics(String userId);
  
  /// 发送主题通知
  Future<void> sendTopicNotification({
    required String topic,
    required PushNotification notification,
  });
  
  /// 创建通知模板
  Future<void> createNotificationTemplate({
    required String templateId,
    required String name,
    required Map<String, dynamic> template,
  });
  
  /// 获取通知模板
  Future<Map<String, dynamic>?> getNotificationTemplate(String templateId);
  
  /// 使用模板发送通知
  Future<void> sendNotificationFromTemplate({
    required String templateId,
    required Map<String, dynamic> variables,
    String? userId,
    List<String>? userGroups,
    List<String>? tags,
  });
  
  /// 获取通知送达率
  Future<Map<String, dynamic>> getDeliveryRate({
    DateTime? startTime,
    DateTime? endTime,
    NotificationType? type,
  });
  
  /// 获取通知点击率
  Future<Map<String, dynamic>> getClickThroughRate({
    DateTime? startTime,
    DateTime? endTime,
    NotificationType? type,
  });
  
  /// 获取用户参与度
  Future<Map<String, dynamic>> getUserEngagement({
    required String userId,
    DateTime? startTime,
    DateTime? endTime,
  });
  
  /// 清理过期通知
  Future<void> cleanupExpiredNotifications(DateTime before);
  
  /// 导出通知数据
  Future<Map<String, dynamic>> exportNotificationData({
    DateTime? startTime,
    DateTime? endTime,
    List<String>? userIds,
    List<NotificationType>? types,
  });
}
