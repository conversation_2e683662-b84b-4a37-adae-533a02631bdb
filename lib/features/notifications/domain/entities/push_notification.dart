import 'package:equatable/equatable.dart';

/// 推送通知类型
enum NotificationType {
  /// 系统通知
  system,
  
  /// 营销通知
  marketing,
  
  /// 交易通知
  transactional,
  
  /// 社交通知
  social,
  
  /// 新闻通知
  news,
  
  /// 提醒通知
  reminder,
  
  /// 警告通知
  warning,
  
  /// 紧急通知
  emergency,
  
  /// 自定义通知
  custom,
}

/// 通知优先级
enum NotificationPriority {
  /// 低优先级
  low,
  
  /// 正常优先级
  normal,
  
  /// 高优先级
  high,
  
  /// 紧急优先级
  urgent,
}

/// 通知状态
enum NotificationStatus {
  /// 待发送
  pending,
  
  /// 已发送
  sent,
  
  /// 已送达
  delivered,
  
  /// 已读
  read,
  
  /// 已点击
  clicked,
  
  /// 已忽略
  dismissed,
  
  /// 发送失败
  failed,
  
  /// 已过期
  expired,
}

/// 推送通知实体
/// 
/// **功能依赖**: 需要启用 notifications 模块
/// **配置项**: FEATURE_NOTIFICATIONS
class PushNotification extends Equatable {
  /// 通知ID
  final String id;
  
  /// 通知类型
  final NotificationType type;
  
  /// 通知优先级
  final NotificationPriority priority;
  
  /// 通知状态
  final NotificationStatus status;
  
  /// 标题
  final String title;
  
  /// 内容
  final String body;
  
  /// 图标
  final String? icon;
  
  /// 图片
  final String? image;
  
  /// 声音
  final String? sound;
  
  /// 徽章数量
  final int? badge;
  
  /// 点击动作
  final String? clickAction;
  
  /// 深度链接
  final String? deepLink;
  
  /// 自定义数据
  final Map<String, dynamic> data;
  
  /// 目标用户ID
  final String? userId;
  
  /// 目标设备令牌
  final String? deviceToken;
  
  /// 目标用户组
  final List<String>? userGroups;
  
  /// 目标标签
  final List<String>? tags;
  
  /// 创建时间
  final DateTime createdAt;
  
  /// 计划发送时间
  final DateTime? scheduledAt;
  
  /// 实际发送时间
  final DateTime? sentAt;
  
  /// 送达时间
  final DateTime? deliveredAt;
  
  /// 阅读时间
  final DateTime? readAt;
  
  /// 点击时间
  final DateTime? clickedAt;
  
  /// 过期时间
  final DateTime? expiresAt;
  
  /// 重试次数
  final int retryCount;
  
  /// 最大重试次数
  final int maxRetries;
  
  /// 错误信息
  final String? errorMessage;
  
  /// 发送渠道
  final List<String> channels;
  
  /// 本地化配置
  final Map<String, Map<String, String>>? localizations;

  const PushNotification({
    required this.id,
    required this.type,
    this.priority = NotificationPriority.normal,
    this.status = NotificationStatus.pending,
    required this.title,
    required this.body,
    this.icon,
    this.image,
    this.sound,
    this.badge,
    this.clickAction,
    this.deepLink,
    this.data = const {},
    this.userId,
    this.deviceToken,
    this.userGroups,
    this.tags,
    required this.createdAt,
    this.scheduledAt,
    this.sentAt,
    this.deliveredAt,
    this.readAt,
    this.clickedAt,
    this.expiresAt,
    this.retryCount = 0,
    this.maxRetries = 3,
    this.errorMessage,
    this.channels = const ['fcm'],
    this.localizations,
  });

  /// 创建系统通知
  factory PushNotification.system({
    required String id,
    required String title,
    required String body,
    String? userId,
    String? deviceToken,
    Map<String, dynamic>? data,
    DateTime? scheduledAt,
  }) {
    return PushNotification(
      id: id,
      type: NotificationType.system,
      priority: NotificationPriority.normal,
      title: title,
      body: body,
      userId: userId,
      deviceToken: deviceToken,
      data: data ?? {},
      createdAt: DateTime.now(),
      scheduledAt: scheduledAt,
      sound: 'default',
    );
  }

  /// 创建营销通知
  factory PushNotification.marketing({
    required String id,
    required String title,
    required String body,
    String? image,
    String? deepLink,
    List<String>? userGroups,
    List<String>? tags,
    Map<String, dynamic>? data,
    DateTime? scheduledAt,
  }) {
    return PushNotification(
      id: id,
      type: NotificationType.marketing,
      priority: NotificationPriority.low,
      title: title,
      body: body,
      image: image,
      deepLink: deepLink,
      userGroups: userGroups,
      tags: tags,
      data: data ?? {},
      createdAt: DateTime.now(),
      scheduledAt: scheduledAt,
    );
  }

  /// 创建交易通知
  factory PushNotification.transactional({
    required String id,
    required String title,
    required String body,
    required String userId,
    String? clickAction,
    Map<String, dynamic>? data,
  }) {
    return PushNotification(
      id: id,
      type: NotificationType.transactional,
      priority: NotificationPriority.high,
      title: title,
      body: body,
      userId: userId,
      clickAction: clickAction,
      data: data ?? {},
      createdAt: DateTime.now(),
      sound: 'default',
      badge: 1,
    );
  }

  /// 创建紧急通知
  factory PushNotification.emergency({
    required String id,
    required String title,
    required String body,
    String? userId,
    List<String>? userGroups,
    Map<String, dynamic>? data,
  }) {
    return PushNotification(
      id: id,
      type: NotificationType.emergency,
      priority: NotificationPriority.urgent,
      title: title,
      body: body,
      userId: userId,
      userGroups: userGroups,
      data: data ?? {},
      createdAt: DateTime.now(),
      sound: 'emergency',
      badge: 1,
      maxRetries: 5,
    );
  }

  /// 是否为即时通知
  bool get isImmediate => scheduledAt == null;

  /// 是否为计划通知
  bool get isScheduled => scheduledAt != null;

  /// 是否已过期
  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  /// 是否可以重试
  bool get canRetry => retryCount < maxRetries && !isExpired;

  /// 是否为高优先级
  bool get isHighPriority => 
      priority == NotificationPriority.high || 
      priority == NotificationPriority.urgent;

  /// 是否为紧急通知
  bool get isUrgent => priority == NotificationPriority.urgent;

  /// 获取本地化标题
  String getLocalizedTitle(String locale) {
    return localizations?[locale]?['title'] ?? title;
  }

  /// 获取本地化内容
  String getLocalizedBody(String locale) {
    return localizations?[locale]?['body'] ?? body;
  }

  /// 标记为已发送
  PushNotification markAsSent({DateTime? sentAt}) {
    return copyWith(
      status: NotificationStatus.sent,
      sentAt: sentAt ?? DateTime.now(),
    );
  }

  /// 标记为已送达
  PushNotification markAsDelivered({DateTime? deliveredAt}) {
    return copyWith(
      status: NotificationStatus.delivered,
      deliveredAt: deliveredAt ?? DateTime.now(),
    );
  }

  /// 标记为已读
  PushNotification markAsRead({DateTime? readAt}) {
    return copyWith(
      status: NotificationStatus.read,
      readAt: readAt ?? DateTime.now(),
    );
  }

  /// 标记为已点击
  PushNotification markAsClicked({DateTime? clickedAt}) {
    return copyWith(
      status: NotificationStatus.clicked,
      clickedAt: clickedAt ?? DateTime.now(),
    );
  }

  /// 标记为发送失败
  PushNotification markAsFailed({
    String? errorMessage,
    bool incrementRetry = true,
  }) {
    return copyWith(
      status: NotificationStatus.failed,
      errorMessage: errorMessage,
      retryCount: incrementRetry ? retryCount + 1 : retryCount,
    );
  }

  /// 复制并修改通知
  PushNotification copyWith({
    String? id,
    NotificationType? type,
    NotificationPriority? priority,
    NotificationStatus? status,
    String? title,
    String? body,
    String? icon,
    String? image,
    String? sound,
    int? badge,
    String? clickAction,
    String? deepLink,
    Map<String, dynamic>? data,
    String? userId,
    String? deviceToken,
    List<String>? userGroups,
    List<String>? tags,
    DateTime? createdAt,
    DateTime? scheduledAt,
    DateTime? sentAt,
    DateTime? deliveredAt,
    DateTime? readAt,
    DateTime? clickedAt,
    DateTime? expiresAt,
    int? retryCount,
    int? maxRetries,
    String? errorMessage,
    List<String>? channels,
    Map<String, Map<String, String>>? localizations,
  }) {
    return PushNotification(
      id: id ?? this.id,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      status: status ?? this.status,
      title: title ?? this.title,
      body: body ?? this.body,
      icon: icon ?? this.icon,
      image: image ?? this.image,
      sound: sound ?? this.sound,
      badge: badge ?? this.badge,
      clickAction: clickAction ?? this.clickAction,
      deepLink: deepLink ?? this.deepLink,
      data: data ?? this.data,
      userId: userId ?? this.userId,
      deviceToken: deviceToken ?? this.deviceToken,
      userGroups: userGroups ?? this.userGroups,
      tags: tags ?? this.tags,
      createdAt: createdAt ?? this.createdAt,
      scheduledAt: scheduledAt ?? this.scheduledAt,
      sentAt: sentAt ?? this.sentAt,
      deliveredAt: deliveredAt ?? this.deliveredAt,
      readAt: readAt ?? this.readAt,
      clickedAt: clickedAt ?? this.clickedAt,
      expiresAt: expiresAt ?? this.expiresAt,
      retryCount: retryCount ?? this.retryCount,
      maxRetries: maxRetries ?? this.maxRetries,
      errorMessage: errorMessage ?? this.errorMessage,
      channels: channels ?? this.channels,
      localizations: localizations ?? this.localizations,
    );
  }

  /// 转换为 JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'priority': priority.name,
      'status': status.name,
      'title': title,
      'body': body,
      'icon': icon,
      'image': image,
      'sound': sound,
      'badge': badge,
      'clickAction': clickAction,
      'deepLink': deepLink,
      'data': data,
      'userId': userId,
      'deviceToken': deviceToken,
      'userGroups': userGroups,
      'tags': tags,
      'createdAt': createdAt.toIso8601String(),
      'scheduledAt': scheduledAt?.toIso8601String(),
      'sentAt': sentAt?.toIso8601String(),
      'deliveredAt': deliveredAt?.toIso8601String(),
      'readAt': readAt?.toIso8601String(),
      'clickedAt': clickedAt?.toIso8601String(),
      'expiresAt': expiresAt?.toIso8601String(),
      'retryCount': retryCount,
      'maxRetries': maxRetries,
      'errorMessage': errorMessage,
      'channels': channels,
      'localizations': localizations,
    };
  }

  /// 从 JSON 创建
  factory PushNotification.fromJson(Map<String, dynamic> json) {
    return PushNotification(
      id: json['id'] as String,
      type: NotificationType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => NotificationType.custom,
      ),
      priority: NotificationPriority.values.firstWhere(
        (e) => e.name == json['priority'],
        orElse: () => NotificationPriority.normal,
      ),
      status: NotificationStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => NotificationStatus.pending,
      ),
      title: json['title'] as String,
      body: json['body'] as String,
      icon: json['icon'] as String?,
      image: json['image'] as String?,
      sound: json['sound'] as String?,
      badge: json['badge'] as int?,
      clickAction: json['clickAction'] as String?,
      deepLink: json['deepLink'] as String?,
      data: json['data'] as Map<String, dynamic>? ?? {},
      userId: json['userId'] as String?,
      deviceToken: json['deviceToken'] as String?,
      userGroups: json['userGroups'] != null
          ? List<String>.from(json['userGroups'] as List)
          : null,
      tags: json['tags'] != null
          ? List<String>.from(json['tags'] as List)
          : null,
      createdAt: DateTime.parse(json['createdAt'] as String),
      scheduledAt: json['scheduledAt'] != null
          ? DateTime.parse(json['scheduledAt'] as String)
          : null,
      sentAt: json['sentAt'] != null
          ? DateTime.parse(json['sentAt'] as String)
          : null,
      deliveredAt: json['deliveredAt'] != null
          ? DateTime.parse(json['deliveredAt'] as String)
          : null,
      readAt: json['readAt'] != null
          ? DateTime.parse(json['readAt'] as String)
          : null,
      clickedAt: json['clickedAt'] != null
          ? DateTime.parse(json['clickedAt'] as String)
          : null,
      expiresAt: json['expiresAt'] != null
          ? DateTime.parse(json['expiresAt'] as String)
          : null,
      retryCount: json['retryCount'] as int? ?? 0,
      maxRetries: json['maxRetries'] as int? ?? 3,
      errorMessage: json['errorMessage'] as String?,
      channels: json['channels'] != null
          ? List<String>.from(json['channels'] as List)
          : ['fcm'],
      localizations: json['localizations'] != null
          ? Map<String, Map<String, String>>.from(
              (json['localizations'] as Map).map(
                (key, value) => MapEntry(
                  key as String,
                  Map<String, String>.from(value as Map),
                ),
              ),
            )
          : null,
    );
  }

  @override
  List<Object?> get props => [
        id,
        type,
        priority,
        status,
        title,
        body,
        icon,
        image,
        sound,
        badge,
        clickAction,
        deepLink,
        data,
        userId,
        deviceToken,
        userGroups,
        tags,
        createdAt,
        scheduledAt,
        sentAt,
        deliveredAt,
        readAt,
        clickedAt,
        expiresAt,
        retryCount,
        maxRetries,
        errorMessage,
        channels,
        localizations,
      ];

  @override
  String toString() {
    return 'PushNotification(id: $id, type: $type, title: $title, status: $status)';
  }
}
