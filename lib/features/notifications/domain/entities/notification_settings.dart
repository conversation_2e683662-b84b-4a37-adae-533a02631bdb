import 'package:equatable/equatable.dart';
import 'push_notification.dart';

/// 通知渠道设置
class NotificationChannelSettings extends Equatable {
  /// 渠道ID
  final String channelId;
  
  /// 渠道名称
  final String name;
  
  /// 渠道描述
  final String description;
  
  /// 是否启用
  final bool enabled;
  
  /// 重要性级别
  final int importance;
  
  /// 是否显示徽章
  final bool showBadge;
  
  /// 是否播放声音
  final bool playSound;
  
  /// 自定义声音
  final String? soundUri;
  
  /// 是否振动
  final bool enableVibration;
  
  /// 振动模式
  final List<int>? vibrationPattern;
  
  /// 是否显示在锁屏
  final bool showOnLockScreen;
  
  /// LED灯颜色
  final int? ledColor;

  const NotificationChannelSettings({
    required this.channelId,
    required this.name,
    required this.description,
    this.enabled = true,
    this.importance = 3,
    this.showBadge = true,
    this.playSound = true,
    this.soundUri,
    this.enableVibration = true,
    this.vibrationPattern,
    this.showOnLockScreen = true,
    this.ledColor,
  });

  NotificationChannelSettings copyWith({
    String? channelId,
    String? name,
    String? description,
    bool? enabled,
    int? importance,
    bool? showBadge,
    bool? playSound,
    String? soundUri,
    bool? enableVibration,
    List<int>? vibrationPattern,
    bool? showOnLockScreen,
    int? ledColor,
  }) {
    return NotificationChannelSettings(
      channelId: channelId ?? this.channelId,
      name: name ?? this.name,
      description: description ?? this.description,
      enabled: enabled ?? this.enabled,
      importance: importance ?? this.importance,
      showBadge: showBadge ?? this.showBadge,
      playSound: playSound ?? this.playSound,
      soundUri: soundUri ?? this.soundUri,
      enableVibration: enableVibration ?? this.enableVibration,
      vibrationPattern: vibrationPattern ?? this.vibrationPattern,
      showOnLockScreen: showOnLockScreen ?? this.showOnLockScreen,
      ledColor: ledColor ?? this.ledColor,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'channelId': channelId,
      'name': name,
      'description': description,
      'enabled': enabled,
      'importance': importance,
      'showBadge': showBadge,
      'playSound': playSound,
      'soundUri': soundUri,
      'enableVibration': enableVibration,
      'vibrationPattern': vibrationPattern,
      'showOnLockScreen': showOnLockScreen,
      'ledColor': ledColor,
    };
  }

  factory NotificationChannelSettings.fromJson(Map<String, dynamic> json) {
    return NotificationChannelSettings(
      channelId: json['channelId'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      enabled: json['enabled'] as bool? ?? true,
      importance: json['importance'] as int? ?? 3,
      showBadge: json['showBadge'] as bool? ?? true,
      playSound: json['playSound'] as bool? ?? true,
      soundUri: json['soundUri'] as String?,
      enableVibration: json['enableVibration'] as bool? ?? true,
      vibrationPattern: json['vibrationPattern'] != null
          ? List<int>.from(json['vibrationPattern'] as List)
          : null,
      showOnLockScreen: json['showOnLockScreen'] as bool? ?? true,
      ledColor: json['ledColor'] as int?,
    );
  }

  @override
  List<Object?> get props => [
        channelId,
        name,
        description,
        enabled,
        importance,
        showBadge,
        playSound,
        soundUri,
        enableVibration,
        vibrationPattern,
        showOnLockScreen,
        ledColor,
      ];
}

/// 通知设置实体
/// 
/// **功能依赖**: 需要启用 notifications 模块
/// **配置项**: FEATURE_NOTIFICATIONS
class NotificationSettings extends Equatable {
  /// 用户ID
  final String userId;
  
  /// 是否启用推送通知
  final bool pushNotificationsEnabled;
  
  /// 是否启用应用内通知
  final bool inAppNotificationsEnabled;
  
  /// 是否启用邮件通知
  final bool emailNotificationsEnabled;
  
  /// 是否启用短信通知
  final bool smsNotificationsEnabled;
  
  /// 免打扰模式
  final bool doNotDisturbEnabled;
  
  /// 免打扰开始时间
  final String? doNotDisturbStartTime;
  
  /// 免打扰结束时间
  final String? doNotDisturbEndTime;
  
  /// 免打扰天数（周一到周日，1-7）
  final List<int>? doNotDisturbDays;
  
  /// 按通知类型的设置
  final Map<NotificationType, bool> typeSettings;
  
  /// 按优先级的设置
  final Map<NotificationPriority, bool> prioritySettings;
  
  /// 渠道设置
  final Map<String, NotificationChannelSettings> channelSettings;
  
  /// 设备令牌
  final String? deviceToken;
  
  /// 支持的通知渠道
  final List<String> supportedChannels;
  
  /// 语言偏好
  final String? languagePreference;
  
  /// 时区
  final String? timezone;
  
  /// 最后更新时间
  final DateTime lastUpdated;
  
  /// 自定义设置
  final Map<String, dynamic> customSettings;

  const NotificationSettings({
    required this.userId,
    this.pushNotificationsEnabled = true,
    this.inAppNotificationsEnabled = true,
    this.emailNotificationsEnabled = false,
    this.smsNotificationsEnabled = false,
    this.doNotDisturbEnabled = false,
    this.doNotDisturbStartTime,
    this.doNotDisturbEndTime,
    this.doNotDisturbDays,
    this.typeSettings = const {},
    this.prioritySettings = const {},
    this.channelSettings = const {},
    this.deviceToken,
    this.supportedChannels = const ['fcm'],
    this.languagePreference,
    this.timezone,
    required this.lastUpdated,
    this.customSettings = const {},
  });

  /// 创建默认设置
  factory NotificationSettings.defaultSettings(String userId) {
    return NotificationSettings(
      userId: userId,
      pushNotificationsEnabled: true,
      inAppNotificationsEnabled: true,
      typeSettings: {
        NotificationType.system: true,
        NotificationType.transactional: true,
        NotificationType.marketing: false,
        NotificationType.social: true,
        NotificationType.news: false,
        NotificationType.reminder: true,
        NotificationType.warning: true,
        NotificationType.emergency: true,
        NotificationType.custom: true,
      },
      prioritySettings: {
        NotificationPriority.low: false,
        NotificationPriority.normal: true,
        NotificationPriority.high: true,
        NotificationPriority.urgent: true,
      },
      channelSettings: {
        'default': const NotificationChannelSettings(
          channelId: 'default',
          name: '默认通知',
          description: '应用的默认通知渠道',
        ),
        'system': const NotificationChannelSettings(
          channelId: 'system',
          name: '系统通知',
          description: '系统相关的重要通知',
          importance: 4,
        ),
        'marketing': const NotificationChannelSettings(
          channelId: 'marketing',
          name: '营销通知',
          description: '促销和营销相关的通知',
          importance: 2,
          enabled: false,
        ),
      },
      lastUpdated: DateTime.now(),
    );
  }

  /// 检查是否允许发送通知
  bool shouldReceiveNotification(PushNotification notification) {
    // 检查总开关
    if (!pushNotificationsEnabled) return false;
    
    // 检查免打扰模式
    if (doNotDisturbEnabled && _isInDoNotDisturbPeriod()) {
      // 紧急通知可以突破免打扰
      if (notification.priority != NotificationPriority.urgent) {
        return false;
      }
    }
    
    // 检查通知类型设置
    final typeEnabled = typeSettings[notification.type] ?? true;
    if (!typeEnabled) return false;
    
    // 检查优先级设置
    final priorityEnabled = prioritySettings[notification.priority] ?? true;
    if (!priorityEnabled) return false;
    
    // 检查渠道设置
    for (final channel in notification.channels) {
      final channelSetting = channelSettings[channel];
      if (channelSetting != null && !channelSetting.enabled) {
        return false;
      }
    }
    
    return true;
  }

  /// 检查是否在免打扰时间段内
  bool _isInDoNotDisturbPeriod() {
    if (!doNotDisturbEnabled || 
        doNotDisturbStartTime == null || 
        doNotDisturbEndTime == null) {
      return false;
    }
    
    final now = DateTime.now();
    
    // 检查是否在免打扰天数内
    if (doNotDisturbDays != null && doNotDisturbDays!.isNotEmpty) {
      if (!doNotDisturbDays!.contains(now.weekday)) {
        return false;
      }
    }
    
    // 解析时间
    final startParts = doNotDisturbStartTime!.split(':');
    final endParts = doNotDisturbEndTime!.split(':');
    
    final startTime = DateTime(
      now.year,
      now.month,
      now.day,
      int.parse(startParts[0]),
      int.parse(startParts[1]),
    );
    
    var endTime = DateTime(
      now.year,
      now.month,
      now.day,
      int.parse(endParts[0]),
      int.parse(endParts[1]),
    );
    
    // 如果结束时间小于开始时间，说明跨天了
    if (endTime.isBefore(startTime)) {
      endTime = endTime.add(const Duration(days: 1));
    }
    
    return now.isAfter(startTime) && now.isBefore(endTime);
  }

  /// 获取通知类型的设置
  bool isTypeEnabled(NotificationType type) {
    return typeSettings[type] ?? true;
  }

  /// 获取优先级的设置
  bool isPriorityEnabled(NotificationPriority priority) {
    return prioritySettings[priority] ?? true;
  }

  /// 获取渠道设置
  NotificationChannelSettings? getChannelSettings(String channelId) {
    return channelSettings[channelId];
  }

  /// 复制并修改设置
  NotificationSettings copyWith({
    String? userId,
    bool? pushNotificationsEnabled,
    bool? inAppNotificationsEnabled,
    bool? emailNotificationsEnabled,
    bool? smsNotificationsEnabled,
    bool? doNotDisturbEnabled,
    String? doNotDisturbStartTime,
    String? doNotDisturbEndTime,
    List<int>? doNotDisturbDays,
    Map<NotificationType, bool>? typeSettings,
    Map<NotificationPriority, bool>? prioritySettings,
    Map<String, NotificationChannelSettings>? channelSettings,
    String? deviceToken,
    List<String>? supportedChannels,
    String? languagePreference,
    String? timezone,
    DateTime? lastUpdated,
    Map<String, dynamic>? customSettings,
  }) {
    return NotificationSettings(
      userId: userId ?? this.userId,
      pushNotificationsEnabled: pushNotificationsEnabled ?? this.pushNotificationsEnabled,
      inAppNotificationsEnabled: inAppNotificationsEnabled ?? this.inAppNotificationsEnabled,
      emailNotificationsEnabled: emailNotificationsEnabled ?? this.emailNotificationsEnabled,
      smsNotificationsEnabled: smsNotificationsEnabled ?? this.smsNotificationsEnabled,
      doNotDisturbEnabled: doNotDisturbEnabled ?? this.doNotDisturbEnabled,
      doNotDisturbStartTime: doNotDisturbStartTime ?? this.doNotDisturbStartTime,
      doNotDisturbEndTime: doNotDisturbEndTime ?? this.doNotDisturbEndTime,
      doNotDisturbDays: doNotDisturbDays ?? this.doNotDisturbDays,
      typeSettings: typeSettings ?? this.typeSettings,
      prioritySettings: prioritySettings ?? this.prioritySettings,
      channelSettings: channelSettings ?? this.channelSettings,
      deviceToken: deviceToken ?? this.deviceToken,
      supportedChannels: supportedChannels ?? this.supportedChannels,
      languagePreference: languagePreference ?? this.languagePreference,
      timezone: timezone ?? this.timezone,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      customSettings: customSettings ?? this.customSettings,
    );
  }

  /// 转换为 JSON
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'pushNotificationsEnabled': pushNotificationsEnabled,
      'inAppNotificationsEnabled': inAppNotificationsEnabled,
      'emailNotificationsEnabled': emailNotificationsEnabled,
      'smsNotificationsEnabled': smsNotificationsEnabled,
      'doNotDisturbEnabled': doNotDisturbEnabled,
      'doNotDisturbStartTime': doNotDisturbStartTime,
      'doNotDisturbEndTime': doNotDisturbEndTime,
      'doNotDisturbDays': doNotDisturbDays,
      'typeSettings': typeSettings.map((key, value) => MapEntry(key.name, value)),
      'prioritySettings': prioritySettings.map((key, value) => MapEntry(key.name, value)),
      'channelSettings': channelSettings.map((key, value) => MapEntry(key, value.toJson())),
      'deviceToken': deviceToken,
      'supportedChannels': supportedChannels,
      'languagePreference': languagePreference,
      'timezone': timezone,
      'lastUpdated': lastUpdated.toIso8601String(),
      'customSettings': customSettings,
    };
  }

  /// 从 JSON 创建
  factory NotificationSettings.fromJson(Map<String, dynamic> json) {
    return NotificationSettings(
      userId: json['userId'] as String,
      pushNotificationsEnabled: json['pushNotificationsEnabled'] as bool? ?? true,
      inAppNotificationsEnabled: json['inAppNotificationsEnabled'] as bool? ?? true,
      emailNotificationsEnabled: json['emailNotificationsEnabled'] as bool? ?? false,
      smsNotificationsEnabled: json['smsNotificationsEnabled'] as bool? ?? false,
      doNotDisturbEnabled: json['doNotDisturbEnabled'] as bool? ?? false,
      doNotDisturbStartTime: json['doNotDisturbStartTime'] as String?,
      doNotDisturbEndTime: json['doNotDisturbEndTime'] as String?,
      doNotDisturbDays: json['doNotDisturbDays'] != null
          ? List<int>.from(json['doNotDisturbDays'] as List)
          : null,
      typeSettings: json['typeSettings'] != null
          ? Map<NotificationType, bool>.fromEntries(
              (json['typeSettings'] as Map<String, dynamic>).entries.map(
                (entry) => MapEntry(
                  NotificationType.values.firstWhere(
                    (e) => e.name == entry.key,
                    orElse: () => NotificationType.custom,
                  ),
                  entry.value as bool,
                ),
              ),
            )
          : {},
      prioritySettings: json['prioritySettings'] != null
          ? Map<NotificationPriority, bool>.fromEntries(
              (json['prioritySettings'] as Map<String, dynamic>).entries.map(
                (entry) => MapEntry(
                  NotificationPriority.values.firstWhere(
                    (e) => e.name == entry.key,
                    orElse: () => NotificationPriority.normal,
                  ),
                  entry.value as bool,
                ),
              ),
            )
          : {},
      channelSettings: json['channelSettings'] != null
          ? Map<String, NotificationChannelSettings>.fromEntries(
              (json['channelSettings'] as Map<String, dynamic>).entries.map(
                (entry) => MapEntry(
                  entry.key,
                  NotificationChannelSettings.fromJson(entry.value as Map<String, dynamic>),
                ),
              ),
            )
          : {},
      deviceToken: json['deviceToken'] as String?,
      supportedChannels: json['supportedChannels'] != null
          ? List<String>.from(json['supportedChannels'] as List)
          : ['fcm'],
      languagePreference: json['languagePreference'] as String?,
      timezone: json['timezone'] as String?,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      customSettings: json['customSettings'] as Map<String, dynamic>? ?? {},
    );
  }

  @override
  List<Object?> get props => [
        userId,
        pushNotificationsEnabled,
        inAppNotificationsEnabled,
        emailNotificationsEnabled,
        smsNotificationsEnabled,
        doNotDisturbEnabled,
        doNotDisturbStartTime,
        doNotDisturbEndTime,
        doNotDisturbDays,
        typeSettings,
        prioritySettings,
        channelSettings,
        deviceToken,
        supportedChannels,
        languagePreference,
        timezone,
        lastUpdated,
        customSettings,
      ];

  @override
  String toString() {
    return 'NotificationSettings(userId: $userId, pushEnabled: $pushNotificationsEnabled)';
  }
}
