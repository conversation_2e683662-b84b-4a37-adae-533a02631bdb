import '../entities/push_notification.dart';
import '../entities/notification_settings.dart';

/// 推送通知服务接口
/// 
/// **功能依赖**: 需要启用 notifications 模块
/// **配置项**: FEATURE_NOTIFICATIONS
abstract class IPushNotificationService {
  /// 初始化推送服务
  Future<void> initialize({
    String? apiKey,
    String? projectId,
    Map<String, dynamic>? config,
  });
  
  /// 请求通知权限
  Future<bool> requestPermission();
  
  /// 检查通知权限状态
  Future<bool> hasPermission();
  
  /// 获取设备令牌
  Future<String?> getDeviceToken();
  
  /// 刷新设备令牌
  Future<String?> refreshDeviceToken();
  
  /// 监听令牌变化
  Stream<String> onTokenRefresh();
  
  /// 发送即时通知
  Future<void> sendInstantNotification({
    required String userId,
    required String title,
    required String body,
    NotificationType type = NotificationType.system,
    NotificationPriority priority = NotificationPriority.normal,
    String? icon,
    String? image,
    String? sound,
    String? clickAction,
    String? deepLink,
    Map<String, dynamic>? data,
  });
  
  /// 发送自定义通知
  Future<void> sendCustomNotification(PushNotification notification);
  
  /// 计划通知
  Future<void> scheduleNotification({
    required PushNotification notification,
    required DateTime scheduledTime,
  });
  
  /// 取消计划通知
  Future<void> cancelScheduledNotification(String notificationId);
  
  /// 发送给多个用户
  Future<void> sendToUsers({
    required List<String> userIds,
    required String title,
    required String body,
    NotificationType type = NotificationType.system,
    Map<String, dynamic>? data,
  });
  
  /// 发送给用户组
  Future<void> sendToUserGroup({
    required String groupId,
    required String title,
    required String body,
    NotificationType type = NotificationType.system,
    Map<String, dynamic>? data,
  });
  
  /// 发送给标签用户
  Future<void> sendToTags({
    required List<String> tags,
    required String title,
    required String body,
    NotificationType type = NotificationType.system,
    Map<String, dynamic>? data,
  });
  
  /// 广播通知
  Future<void> broadcastNotification({
    required String title,
    required String body,
    NotificationType type = NotificationType.system,
    Map<String, dynamic>? data,
    List<String>? excludeUsers,
  });
  
  /// 监听通知接收
  Stream<PushNotification> onNotificationReceived();
  
  /// 监听通知点击
  Stream<PushNotification> onNotificationClicked();
  
  /// 监听通知忽略
  Stream<PushNotification> onNotificationDismissed();
  
  /// 处理前台通知
  Future<void> handleForegroundNotification(PushNotification notification);
  
  /// 处理后台通知
  Future<void> handleBackgroundNotification(Map<String, dynamic> message);
  
  /// 显示本地通知
  Future<void> showLocalNotification({
    required String title,
    required String body,
    String? icon,
    String? sound,
    Map<String, dynamic>? data,
  });
  
  /// 取消本地通知
  Future<void> cancelLocalNotification(int notificationId);
  
  /// 取消所有本地通知
  Future<void> cancelAllLocalNotifications();
  
  /// 设置应用徽章数量
  Future<void> setBadgeCount(int count);
  
  /// 清除应用徽章
  Future<void> clearBadge();
  
  /// 创建通知渠道（Android）
  Future<void> createNotificationChannel({
    required String channelId,
    required String channelName,
    required String channelDescription,
    int importance = 3,
    bool enableVibration = true,
    bool enableSound = true,
    String? soundUri,
  });
  
  /// 删除通知渠道（Android）
  Future<void> deleteNotificationChannel(String channelId);
  
  /// 订阅主题
  Future<void> subscribeToTopic(String topic);
  
  /// 取消订阅主题
  Future<void> unsubscribeFromTopic(String topic);
  
  /// 获取已订阅的主题
  Future<List<String>> getSubscribedTopics();
  
  /// 设置用户属性
  Future<void> setUserProperty(String key, String value);
  
  /// 设置多个用户属性
  Future<void> setUserProperties(Map<String, String> properties);
  
  /// 添加用户到组
  Future<void> addUserToGroup(String groupId);
  
  /// 从组中移除用户
  Future<void> removeUserFromGroup(String groupId);
  
  /// 添加用户标签
  Future<void> addUserTag(String tag);
  
  /// 移除用户标签
  Future<void> removeUserTag(String tag);
  
  /// 获取用户标签
  Future<List<String>> getUserTags();
  
  /// 启用/禁用通知
  Future<void> setNotificationsEnabled(bool enabled);
  
  /// 检查通知是否启用
  Future<bool> areNotificationsEnabled();
  
  /// 获取通知历史
  Future<List<PushNotification>> getNotificationHistory({
    int? limit,
    DateTime? since,
  });
  
  /// 标记通知为已读
  Future<void> markNotificationAsRead(String notificationId);
  
  /// 标记所有通知为已读
  Future<void> markAllNotificationsAsRead();
  
  /// 删除通知
  Future<void> deleteNotification(String notificationId);
  
  /// 清空通知历史
  Future<void> clearNotificationHistory();
  
  /// 获取未读通知数量
  Future<int> getUnreadNotificationCount();
  
  /// 测试通知
  Future<void> sendTestNotification({
    String title = 'Test Notification',
    String body = 'This is a test notification',
  });
  
  /// 验证通知配置
  Future<bool> validateConfiguration();
  
  /// 获取推送服务状态
  Future<Map<String, dynamic>> getServiceStatus();
  
  /// 重置推送服务
  Future<void> resetService();
}
