import 'package:equatable/equatable.dart';

/// 配置类型
enum ConfigType {
  /// 应用配置
  application,
  
  /// 功能配置
  feature,
  
  /// 安全配置
  security,
  
  /// 性能配置
  performance,
  
  /// 网络配置
  network,
  
  /// 数据库配置
  database,
  
  /// 缓存配置
  cache,
  
  /// 日志配置
  logging,
  
  /// 监控配置
  monitoring,
  
  /// 用户界面配置
  ui,
  
  /// 第三方服务配置
  thirdParty,
  
  /// 自定义配置
  custom,
}

/// 配置环境
enum ConfigEnvironment {
  /// 开发环境
  development,
  
  /// 测试环境
  testing,
  
  /// 预发布环境
  staging,
  
  /// 生产环境
  production,
  
  /// 本地环境
  local,
}

/// 配置状态
enum ConfigStatus {
  /// 草稿
  draft,
  
  /// 待审核
  pending,
  
  /// 已批准
  approved,
  
  /// 已部署
  deployed,
  
  /// 已回滚
  rollback,
  
  /// 已废弃
  deprecated,
}

/// 配置管理实体
/// 
/// **功能依赖**: 需要启用 enterprise 模块
/// **配置项**: FEATURE_ENTERPRISE
class ConfigManagement extends Equatable {
  /// 配置ID
  final String id;
  
  /// 配置键
  final String key;
  
  /// 配置名称
  final String name;
  
  /// 配置类型
  final ConfigType type;
  
  /// 配置环境
  final ConfigEnvironment environment;
  
  /// 配置状态
  final ConfigStatus status;
  
  /// 配置值
  final dynamic value;
  
  /// 默认值
  final dynamic defaultValue;
  
  /// 配置描述
  final String description;
  
  /// 数据类型
  final String dataType;
  
  /// 是否敏感数据
  final bool isSensitive;
  
  /// 是否必需
  final bool isRequired;
  
  /// 是否只读
  final bool isReadOnly;
  
  /// 验证规则
  final Map<String, dynamic>? validationRules;
  
  /// 可选值列表
  final List<dynamic>? allowedValues;
  
  /// 最小值
  final dynamic minValue;
  
  /// 最大值
  final dynamic maxValue;
  
  /// 配置组
  final String? group;
  
  /// 标签
  final List<String> tags;
  
  /// 创建时间
  final DateTime createdAt;
  
  /// 更新时间
  final DateTime updatedAt;
  
  /// 创建者
  final String createdBy;
  
  /// 更新者
  final String? updatedBy;
  
  /// 版本号
  final int version;
  
  /// 变更历史
  final List<ConfigChange> changeHistory;
  
  /// 依赖配置
  final List<String>? dependencies;
  
  /// 影响的服务
  final List<String>? affectedServices;
  
  /// 生效时间
  final DateTime? effectiveAt;
  
  /// 过期时间
  final DateTime? expiresAt;

  const ConfigManagement({
    required this.id,
    required this.key,
    required this.name,
    required this.type,
    required this.environment,
    this.status = ConfigStatus.draft,
    required this.value,
    this.defaultValue,
    this.description = '',
    this.dataType = 'string',
    this.isSensitive = false,
    this.isRequired = false,
    this.isReadOnly = false,
    this.validationRules,
    this.allowedValues,
    this.minValue,
    this.maxValue,
    this.group,
    this.tags = const [],
    required this.createdAt,
    required this.updatedAt,
    required this.createdBy,
    this.updatedBy,
    this.version = 1,
    this.changeHistory = const [],
    this.dependencies,
    this.affectedServices,
    this.effectiveAt,
    this.expiresAt,
  });

  /// 创建应用配置
  factory ConfigManagement.application({
    required String id,
    required String key,
    required String name,
    required dynamic value,
    ConfigEnvironment environment = ConfigEnvironment.development,
    String description = '',
    String dataType = 'string',
    bool isRequired = false,
    String createdBy = 'system',
  }) {
    final now = DateTime.now();
    return ConfigManagement(
      id: id,
      key: key,
      name: name,
      type: ConfigType.application,
      environment: environment,
      value: value,
      description: description,
      dataType: dataType,
      isRequired: isRequired,
      createdAt: now,
      updatedAt: now,
      createdBy: createdBy,
    );
  }

  /// 创建功能开关配置
  factory ConfigManagement.featureFlag({
    required String id,
    required String key,
    required String name,
    required bool enabled,
    ConfigEnvironment environment = ConfigEnvironment.development,
    String description = '',
    String createdBy = 'system',
    List<String>? affectedServices,
  }) {
    final now = DateTime.now();
    return ConfigManagement(
      id: id,
      key: key,
      name: name,
      type: ConfigType.feature,
      environment: environment,
      value: enabled,
      defaultValue: false,
      description: description,
      dataType: 'boolean',
      createdAt: now,
      updatedAt: now,
      createdBy: createdBy,
      affectedServices: affectedServices,
    );
  }

  /// 创建安全配置
  factory ConfigManagement.security({
    required String id,
    required String key,
    required String name,
    required dynamic value,
    ConfigEnvironment environment = ConfigEnvironment.development,
    String description = '',
    bool isSensitive = true,
    String createdBy = 'system',
  }) {
    final now = DateTime.now();
    return ConfigManagement(
      id: id,
      key: key,
      name: name,
      type: ConfigType.security,
      environment: environment,
      value: value,
      description: description,
      isSensitive: isSensitive,
      isRequired: true,
      createdAt: now,
      updatedAt: now,
      createdBy: createdBy,
    );
  }

  /// 是否有效
  bool get isValid {
    if (expiresAt != null && DateTime.now().isAfter(expiresAt!)) {
      return false;
    }
    if (effectiveAt != null && DateTime.now().isBefore(effectiveAt!)) {
      return false;
    }
    return status == ConfigStatus.deployed || status == ConfigStatus.approved;
  }

  /// 是否已过期
  bool get isExpired {
    return expiresAt != null && DateTime.now().isAfter(expiresAt!);
  }

  /// 是否生效
  bool get isEffective {
    if (effectiveAt != null && DateTime.now().isBefore(effectiveAt!)) {
      return false;
    }
    return isValid;
  }

  /// 是否可编辑
  bool get isEditable {
    return !isReadOnly && (status == ConfigStatus.draft || status == ConfigStatus.pending);
  }

  /// 验证配置值
  bool validateValue(dynamic newValue) {
    // 检查数据类型
    if (!_isValidDataType(newValue)) {
      return false;
    }
    
    // 检查允许的值
    if (allowedValues != null && !allowedValues!.contains(newValue)) {
      return false;
    }
    
    // 检查范围
    if (minValue != null && newValue < minValue) {
      return false;
    }
    if (maxValue != null && newValue > maxValue) {
      return false;
    }
    
    // 检查验证规则
    if (validationRules != null) {
      return _validateRules(newValue);
    }
    
    return true;
  }

  /// 检查数据类型
  bool _isValidDataType(dynamic value) {
    switch (dataType.toLowerCase()) {
      case 'string':
        return value is String;
      case 'int':
      case 'integer':
        return value is int;
      case 'double':
      case 'float':
        return value is double || value is int;
      case 'bool':
      case 'boolean':
        return value is bool;
      case 'list':
      case 'array':
        return value is List;
      case 'map':
      case 'object':
        return value is Map;
      default:
        return true;
    }
  }

  /// 验证规则
  bool _validateRules(dynamic value) {
    if (validationRules == null) return true;
    
    for (final entry in validationRules!.entries) {
      final rule = entry.key;
      final ruleValue = entry.value;
      
      switch (rule) {
        case 'minLength':
          if (value is String && value.length < ruleValue) return false;
          break;
        case 'maxLength':
          if (value is String && value.length > ruleValue) return false;
          break;
        case 'pattern':
          if (value is String && !RegExp(ruleValue).hasMatch(value)) return false;
          break;
        case 'required':
          if (ruleValue == true && (value == null || value == '')) return false;
          break;
      }
    }
    
    return true;
  }

  /// 更新配置值
  ConfigManagement updateValue({
    required dynamic newValue,
    required String updatedBy,
    String? changeReason,
  }) {
    if (!validateValue(newValue)) {
      throw ArgumentError('Invalid configuration value');
    }
    
    final change = ConfigChange(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      oldValue: value,
      newValue: newValue,
      changedBy: updatedBy,
      changedAt: DateTime.now(),
      reason: changeReason,
    );
    
    return copyWith(
      value: newValue,
      updatedAt: DateTime.now(),
      updatedBy: updatedBy,
      version: version + 1,
      changeHistory: [...changeHistory, change],
    );
  }

  /// 部署配置
  ConfigManagement deploy({
    required String deployedBy,
    DateTime? effectiveAt,
  }) {
    return copyWith(
      status: ConfigStatus.deployed,
      updatedAt: DateTime.now(),
      updatedBy: deployedBy,
      effectiveAt: effectiveAt ?? DateTime.now(),
    );
  }

  /// 回滚配置
  ConfigManagement rollback({
    required String rolledBackBy,
    String? reason,
  }) {
    if (changeHistory.isEmpty) {
      throw StateError('No previous version to rollback to');
    }
    
    final previousChange = changeHistory.last;
    final change = ConfigChange(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      oldValue: value,
      newValue: previousChange.oldValue,
      changedBy: rolledBackBy,
      changedAt: DateTime.now(),
      reason: reason ?? 'Rollback to previous version',
    );
    
    return copyWith(
      value: previousChange.oldValue,
      status: ConfigStatus.rollback,
      updatedAt: DateTime.now(),
      updatedBy: rolledBackBy,
      version: version + 1,
      changeHistory: [...changeHistory, change],
    );
  }

  /// 复制并修改配置
  ConfigManagement copyWith({
    String? id,
    String? key,
    String? name,
    ConfigType? type,
    ConfigEnvironment? environment,
    ConfigStatus? status,
    dynamic value,
    dynamic defaultValue,
    String? description,
    String? dataType,
    bool? isSensitive,
    bool? isRequired,
    bool? isReadOnly,
    Map<String, dynamic>? validationRules,
    List<dynamic>? allowedValues,
    dynamic minValue,
    dynamic maxValue,
    String? group,
    List<String>? tags,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? updatedBy,
    int? version,
    List<ConfigChange>? changeHistory,
    List<String>? dependencies,
    List<String>? affectedServices,
    DateTime? effectiveAt,
    DateTime? expiresAt,
  }) {
    return ConfigManagement(
      id: id ?? this.id,
      key: key ?? this.key,
      name: name ?? this.name,
      type: type ?? this.type,
      environment: environment ?? this.environment,
      status: status ?? this.status,
      value: value ?? this.value,
      defaultValue: defaultValue ?? this.defaultValue,
      description: description ?? this.description,
      dataType: dataType ?? this.dataType,
      isSensitive: isSensitive ?? this.isSensitive,
      isRequired: isRequired ?? this.isRequired,
      isReadOnly: isReadOnly ?? this.isReadOnly,
      validationRules: validationRules ?? this.validationRules,
      allowedValues: allowedValues ?? this.allowedValues,
      minValue: minValue ?? this.minValue,
      maxValue: maxValue ?? this.maxValue,
      group: group ?? this.group,
      tags: tags ?? this.tags,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      updatedBy: updatedBy ?? this.updatedBy,
      version: version ?? this.version,
      changeHistory: changeHistory ?? this.changeHistory,
      dependencies: dependencies ?? this.dependencies,
      affectedServices: affectedServices ?? this.affectedServices,
      effectiveAt: effectiveAt ?? this.effectiveAt,
      expiresAt: expiresAt ?? this.expiresAt,
    );
  }

  /// 转换为 JSON
  Map<String, dynamic> toJson({bool includeSensitive = false}) {
    final json = {
      'id': id,
      'key': key,
      'name': name,
      'type': type.name,
      'environment': environment.name,
      'status': status.name,
      'value': isSensitive && !includeSensitive ? '***' : value,
      'defaultValue': defaultValue,
      'description': description,
      'dataType': dataType,
      'isSensitive': isSensitive,
      'isRequired': isRequired,
      'isReadOnly': isReadOnly,
      'validationRules': validationRules,
      'allowedValues': allowedValues,
      'minValue': minValue,
      'maxValue': maxValue,
      'group': group,
      'tags': tags,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'createdBy': createdBy,
      'updatedBy': updatedBy,
      'version': version,
      'changeHistory': changeHistory.map((e) => e.toJson()).toList(),
      'dependencies': dependencies,
      'affectedServices': affectedServices,
      'effectiveAt': effectiveAt?.toIso8601String(),
      'expiresAt': expiresAt?.toIso8601String(),
    };
    
    return json;
  }

  /// 从 JSON 创建
  factory ConfigManagement.fromJson(Map<String, dynamic> json) {
    return ConfigManagement(
      id: json['id'] as String,
      key: json['key'] as String,
      name: json['name'] as String,
      type: ConfigType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => ConfigType.custom,
      ),
      environment: ConfigEnvironment.values.firstWhere(
        (e) => e.name == json['environment'],
        orElse: () => ConfigEnvironment.development,
      ),
      status: ConfigStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => ConfigStatus.draft,
      ),
      value: json['value'],
      defaultValue: json['defaultValue'],
      description: json['description'] as String? ?? '',
      dataType: json['dataType'] as String? ?? 'string',
      isSensitive: json['isSensitive'] as bool? ?? false,
      isRequired: json['isRequired'] as bool? ?? false,
      isReadOnly: json['isReadOnly'] as bool? ?? false,
      validationRules: json['validationRules'] as Map<String, dynamic>?,
      allowedValues: json['allowedValues'] as List<dynamic>?,
      minValue: json['minValue'],
      maxValue: json['maxValue'],
      group: json['group'] as String?,
      tags: json['tags'] != null
          ? List<String>.from(json['tags'] as List)
          : [],
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      createdBy: json['createdBy'] as String,
      updatedBy: json['updatedBy'] as String?,
      version: json['version'] as int? ?? 1,
      changeHistory: json['changeHistory'] != null
          ? (json['changeHistory'] as List)
              .map((e) => ConfigChange.fromJson(e as Map<String, dynamic>))
              .toList()
          : [],
      dependencies: json['dependencies'] != null
          ? List<String>.from(json['dependencies'] as List)
          : null,
      affectedServices: json['affectedServices'] != null
          ? List<String>.from(json['affectedServices'] as List)
          : null,
      effectiveAt: json['effectiveAt'] != null
          ? DateTime.parse(json['effectiveAt'] as String)
          : null,
      expiresAt: json['expiresAt'] != null
          ? DateTime.parse(json['expiresAt'] as String)
          : null,
    );
  }

  @override
  List<Object?> get props => [
        id,
        key,
        name,
        type,
        environment,
        status,
        value,
        defaultValue,
        description,
        dataType,
        isSensitive,
        isRequired,
        isReadOnly,
        validationRules,
        allowedValues,
        minValue,
        maxValue,
        group,
        tags,
        createdAt,
        updatedAt,
        createdBy,
        updatedBy,
        version,
        changeHistory,
        dependencies,
        affectedServices,
        effectiveAt,
        expiresAt,
      ];

  @override
  String toString() {
    return 'ConfigManagement(id: $id, key: $key, environment: $environment, status: $status)';
  }
}

/// 配置变更记录
class ConfigChange extends Equatable {
  /// 变更ID
  final String id;
  
  /// 旧值
  final dynamic oldValue;
  
  /// 新值
  final dynamic newValue;
  
  /// 变更者
  final String changedBy;
  
  /// 变更时间
  final DateTime changedAt;
  
  /// 变更原因
  final String? reason;

  const ConfigChange({
    required this.id,
    required this.oldValue,
    required this.newValue,
    required this.changedBy,
    required this.changedAt,
    this.reason,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'oldValue': oldValue,
      'newValue': newValue,
      'changedBy': changedBy,
      'changedAt': changedAt.toIso8601String(),
      'reason': reason,
    };
  }

  factory ConfigChange.fromJson(Map<String, dynamic> json) {
    return ConfigChange(
      id: json['id'] as String,
      oldValue: json['oldValue'],
      newValue: json['newValue'],
      changedBy: json['changedBy'] as String,
      changedAt: DateTime.parse(json['changedAt'] as String),
      reason: json['reason'] as String?,
    );
  }

  @override
  List<Object?> get props => [id, oldValue, newValue, changedBy, changedAt, reason];
}
