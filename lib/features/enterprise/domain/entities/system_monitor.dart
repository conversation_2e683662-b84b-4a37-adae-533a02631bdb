import 'package:equatable/equatable.dart';

/// 监控指标类型
enum MetricType {
  /// CPU使用率
  cpuUsage,
  
  /// 内存使用率
  memoryUsage,
  
  /// 磁盘使用率
  diskUsage,
  
  /// 网络流量
  networkTraffic,
  
  /// 应用性能
  appPerformance,
  
  /// 用户活跃度
  userActivity,
  
  /// 错误率
  errorRate,
  
  /// 响应时间
  responseTime,
  
  /// 吞吐量
  throughput,
  
  /// 自定义指标
  custom,
}

/// 监控级别
enum MonitorLevel {
  /// 信息
  info,
  
  /// 警告
  warning,
  
  /// 错误
  error,
  
  /// 严重
  critical,
}

/// 系统监控实体
/// 
/// **功能依赖**: 需要启用 enterprise 模块
/// **配置项**: FEATURE_ENTERPRISE
class SystemMonitor extends Equatable {
  /// 监控ID
  final String id;
  
  /// 监控名称
  final String name;
  
  /// 指标类型
  final MetricType metricType;
  
  /// 监控级别
  final MonitorLevel level;
  
  /// 当前值
  final double currentValue;
  
  /// 阈值
  final double threshold;
  
  /// 最大值
  final double? maxValue;
  
  /// 最小值
  final double? minValue;
  
  /// 单位
  final String unit;
  
  /// 描述
  final String description;
  
  /// 监控时间
  final DateTime timestamp;
  
  /// 数据源
  final String dataSource;
  
  /// 标签
  final Map<String, String> tags;
  
  /// 是否启用
  final bool enabled;
  
  /// 是否触发告警
  final bool isAlerted;
  
  /// 告警消息
  final String? alertMessage;
  
  /// 历史数据点
  final List<DataPoint> historyData;
  
  /// 趋势
  final String? trend;
  
  /// 预测值
  final double? predictedValue;
  
  /// 监控间隔（秒）
  final int monitorInterval;
  
  /// 保留天数
  final int retentionDays;

  const SystemMonitor({
    required this.id,
    required this.name,
    required this.metricType,
    this.level = MonitorLevel.info,
    required this.currentValue,
    required this.threshold,
    this.maxValue,
    this.minValue,
    this.unit = '',
    this.description = '',
    required this.timestamp,
    this.dataSource = 'system',
    this.tags = const {},
    this.enabled = true,
    this.isAlerted = false,
    this.alertMessage,
    this.historyData = const [],
    this.trend,
    this.predictedValue,
    this.monitorInterval = 60,
    this.retentionDays = 30,
  });

  /// 创建CPU监控
  factory SystemMonitor.cpu({
    required String id,
    required double cpuUsage,
    double threshold = 80.0,
    Map<String, String> tags = const {},
  }) {
    return SystemMonitor(
      id: id,
      name: 'CPU使用率',
      metricType: MetricType.cpuUsage,
      level: cpuUsage > threshold ? MonitorLevel.warning : MonitorLevel.info,
      currentValue: cpuUsage,
      threshold: threshold,
      maxValue: 100.0,
      minValue: 0.0,
      unit: '%',
      description: '系统CPU使用率监控',
      timestamp: DateTime.now(),
      tags: tags,
      isAlerted: cpuUsage > threshold,
      alertMessage: cpuUsage > threshold ? 'CPU使用率过高: ${cpuUsage.toStringAsFixed(1)}%' : null,
    );
  }

  /// 创建内存监控
  factory SystemMonitor.memory({
    required String id,
    required double memoryUsage,
    double threshold = 85.0,
    Map<String, String> tags = const {},
  }) {
    return SystemMonitor(
      id: id,
      name: '内存使用率',
      metricType: MetricType.memoryUsage,
      level: memoryUsage > threshold ? MonitorLevel.warning : MonitorLevel.info,
      currentValue: memoryUsage,
      threshold: threshold,
      maxValue: 100.0,
      minValue: 0.0,
      unit: '%',
      description: '系统内存使用率监控',
      timestamp: DateTime.now(),
      tags: tags,
      isAlerted: memoryUsage > threshold,
      alertMessage: memoryUsage > threshold ? '内存使用率过高: ${memoryUsage.toStringAsFixed(1)}%' : null,
    );
  }

  /// 创建应用性能监控
  factory SystemMonitor.appPerformance({
    required String id,
    required double responseTime,
    double threshold = 1000.0,
    Map<String, String> tags = const {},
  }) {
    return SystemMonitor(
      id: id,
      name: '应用响应时间',
      metricType: MetricType.appPerformance,
      level: responseTime > threshold ? MonitorLevel.warning : MonitorLevel.info,
      currentValue: responseTime,
      threshold: threshold,
      minValue: 0.0,
      unit: 'ms',
      description: '应用响应时间监控',
      timestamp: DateTime.now(),
      tags: tags,
      isAlerted: responseTime > threshold,
      alertMessage: responseTime > threshold ? '响应时间过长: ${responseTime.toStringAsFixed(0)}ms' : null,
    );
  }

  /// 创建错误率监控
  factory SystemMonitor.errorRate({
    required String id,
    required double errorRate,
    double threshold = 5.0,
    Map<String, String> tags = const {},
  }) {
    return SystemMonitor(
      id: id,
      name: '错误率',
      metricType: MetricType.errorRate,
      level: errorRate > threshold ? MonitorLevel.error : MonitorLevel.info,
      currentValue: errorRate,
      threshold: threshold,
      maxValue: 100.0,
      minValue: 0.0,
      unit: '%',
      description: '应用错误率监控',
      timestamp: DateTime.now(),
      tags: tags,
      isAlerted: errorRate > threshold,
      alertMessage: errorRate > threshold ? '错误率过高: ${errorRate.toStringAsFixed(2)}%' : null,
    );
  }

  /// 是否超过阈值
  bool get isThresholdExceeded => currentValue > threshold;

  /// 是否为关键指标
  bool get isCritical => level == MonitorLevel.critical;

  /// 是否需要告警
  bool get needsAlert => isThresholdExceeded && enabled;

  /// 获取状态颜色
  String get statusColor {
    if (!enabled) return 'gray';
    switch (level) {
      case MonitorLevel.info:
        return 'green';
      case MonitorLevel.warning:
        return 'yellow';
      case MonitorLevel.error:
        return 'orange';
      case MonitorLevel.critical:
        return 'red';
    }
  }

  /// 获取健康度评分
  double get healthScore {
    if (!enabled) return 0.0;
    if (maxValue == null) return currentValue <= threshold ? 100.0 : 0.0;
    
    final normalizedValue = currentValue / maxValue!;
    final normalizedThreshold = threshold / maxValue!;
    
    if (normalizedValue <= normalizedThreshold) {
      return 100.0 - (normalizedValue / normalizedThreshold * 20);
    } else {
      return 80.0 - ((normalizedValue - normalizedThreshold) / (1.0 - normalizedThreshold) * 80);
    }
  }

  /// 添加历史数据点
  SystemMonitor addDataPoint(double value, {DateTime? timestamp}) {
    final dataPoint = DataPoint(
      value: value,
      timestamp: timestamp ?? DateTime.now(),
    );
    
    final newHistoryData = [...historyData, dataPoint];
    
    // 保留最近的数据点
    final maxDataPoints = retentionDays * 24 * 60 ~/ monitorInterval;
    if (newHistoryData.length > maxDataPoints) {
      newHistoryData.removeRange(0, newHistoryData.length - maxDataPoints);
    }
    
    return copyWith(
      currentValue: value,
      timestamp: dataPoint.timestamp,
      historyData: newHistoryData,
      isAlerted: value > threshold,
      alertMessage: value > threshold ? _generateAlertMessage(value) : null,
    );
  }

  /// 生成告警消息
  String _generateAlertMessage(double value) {
    switch (metricType) {
      case MetricType.cpuUsage:
        return 'CPU使用率过高: ${value.toStringAsFixed(1)}%';
      case MetricType.memoryUsage:
        return '内存使用率过高: ${value.toStringAsFixed(1)}%';
      case MetricType.diskUsage:
        return '磁盘使用率过高: ${value.toStringAsFixed(1)}%';
      case MetricType.responseTime:
        return '响应时间过长: ${value.toStringAsFixed(0)}ms';
      case MetricType.errorRate:
        return '错误率过高: ${value.toStringAsFixed(2)}%';
      default:
        return '$name 超过阈值: ${value.toStringAsFixed(2)} $unit';
    }
  }

  /// 复制并修改监控
  SystemMonitor copyWith({
    String? id,
    String? name,
    MetricType? metricType,
    MonitorLevel? level,
    double? currentValue,
    double? threshold,
    double? maxValue,
    double? minValue,
    String? unit,
    String? description,
    DateTime? timestamp,
    String? dataSource,
    Map<String, String>? tags,
    bool? enabled,
    bool? isAlerted,
    String? alertMessage,
    List<DataPoint>? historyData,
    String? trend,
    double? predictedValue,
    int? monitorInterval,
    int? retentionDays,
  }) {
    return SystemMonitor(
      id: id ?? this.id,
      name: name ?? this.name,
      metricType: metricType ?? this.metricType,
      level: level ?? this.level,
      currentValue: currentValue ?? this.currentValue,
      threshold: threshold ?? this.threshold,
      maxValue: maxValue ?? this.maxValue,
      minValue: minValue ?? this.minValue,
      unit: unit ?? this.unit,
      description: description ?? this.description,
      timestamp: timestamp ?? this.timestamp,
      dataSource: dataSource ?? this.dataSource,
      tags: tags ?? this.tags,
      enabled: enabled ?? this.enabled,
      isAlerted: isAlerted ?? this.isAlerted,
      alertMessage: alertMessage ?? this.alertMessage,
      historyData: historyData ?? this.historyData,
      trend: trend ?? this.trend,
      predictedValue: predictedValue ?? this.predictedValue,
      monitorInterval: monitorInterval ?? this.monitorInterval,
      retentionDays: retentionDays ?? this.retentionDays,
    );
  }

  /// 转换为 JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'metricType': metricType.name,
      'level': level.name,
      'currentValue': currentValue,
      'threshold': threshold,
      'maxValue': maxValue,
      'minValue': minValue,
      'unit': unit,
      'description': description,
      'timestamp': timestamp.toIso8601String(),
      'dataSource': dataSource,
      'tags': tags,
      'enabled': enabled,
      'isAlerted': isAlerted,
      'alertMessage': alertMessage,
      'historyData': historyData.map((e) => e.toJson()).toList(),
      'trend': trend,
      'predictedValue': predictedValue,
      'monitorInterval': monitorInterval,
      'retentionDays': retentionDays,
    };
  }

  /// 从 JSON 创建
  factory SystemMonitor.fromJson(Map<String, dynamic> json) {
    return SystemMonitor(
      id: json['id'] as String,
      name: json['name'] as String,
      metricType: MetricType.values.firstWhere(
        (e) => e.name == json['metricType'],
        orElse: () => MetricType.custom,
      ),
      level: MonitorLevel.values.firstWhere(
        (e) => e.name == json['level'],
        orElse: () => MonitorLevel.info,
      ),
      currentValue: (json['currentValue'] as num).toDouble(),
      threshold: (json['threshold'] as num).toDouble(),
      maxValue: json['maxValue'] != null ? (json['maxValue'] as num).toDouble() : null,
      minValue: json['minValue'] != null ? (json['minValue'] as num).toDouble() : null,
      unit: json['unit'] as String? ?? '',
      description: json['description'] as String? ?? '',
      timestamp: DateTime.parse(json['timestamp'] as String),
      dataSource: json['dataSource'] as String? ?? 'system',
      tags: json['tags'] != null
          ? Map<String, String>.from(json['tags'] as Map)
          : {},
      enabled: json['enabled'] as bool? ?? true,
      isAlerted: json['isAlerted'] as bool? ?? false,
      alertMessage: json['alertMessage'] as String?,
      historyData: json['historyData'] != null
          ? (json['historyData'] as List)
              .map((e) => DataPoint.fromJson(e as Map<String, dynamic>))
              .toList()
          : [],
      trend: json['trend'] as String?,
      predictedValue: json['predictedValue'] != null 
          ? (json['predictedValue'] as num).toDouble() 
          : null,
      monitorInterval: json['monitorInterval'] as int? ?? 60,
      retentionDays: json['retentionDays'] as int? ?? 30,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        metricType,
        level,
        currentValue,
        threshold,
        maxValue,
        minValue,
        unit,
        description,
        timestamp,
        dataSource,
        tags,
        enabled,
        isAlerted,
        alertMessage,
        historyData,
        trend,
        predictedValue,
        monitorInterval,
        retentionDays,
      ];

  @override
  String toString() {
    return 'SystemMonitor(id: $id, name: $name, value: $currentValue $unit, level: $level)';
  }
}

/// 数据点
class DataPoint extends Equatable {
  /// 值
  final double value;
  
  /// 时间戳
  final DateTime timestamp;

  const DataPoint({
    required this.value,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'value': value,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory DataPoint.fromJson(Map<String, dynamic> json) {
    return DataPoint(
      value: (json['value'] as num).toDouble(),
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }

  @override
  List<Object?> get props => [value, timestamp];
}
