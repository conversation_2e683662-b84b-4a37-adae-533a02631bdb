import 'package:equatable/equatable.dart';

/// 事件类型
enum EventType {
  /// 页面浏览
  pageView,
  
  /// 用户操作
  userAction,
  
  /// 业务事件
  businessEvent,
  
  /// 系统事件
  systemEvent,
  
  /// 错误事件
  errorEvent,
  
  /// 性能事件
  performanceEvent,
  
  /// 自定义事件
  customEvent,
}

/// 事件优先级
enum EventPriority {
  /// 低优先级
  low,
  
  /// 正常优先级
  normal,
  
  /// 高优先级
  high,
  
  /// 关键优先级
  critical,
}

/// 追踪事件实体
/// 
/// **功能依赖**: 需要启用 analytics 模块
/// **配置项**: FEATURE_ANALYTICS
class TrackingEvent extends Equatable {
  /// 事件ID
  final String id;
  
  /// 事件名称
  final String name;
  
  /// 事件类型
  final EventType type;
  
  /// 事件优先级
  final EventPriority priority;
  
  /// 事件时间戳
  final DateTime timestamp;
  
  /// 事件属性
  final Map<String, dynamic> properties;
  
  /// 用户ID
  final String? userId;
  
  /// 会话ID
  final String? sessionId;
  
  /// 设备ID
  final String? deviceId;
  
  /// 应用版本
  final String? appVersion;
  
  /// 平台信息
  final String? platform;
  
  /// 页面路径
  final String? pagePath;
  
  /// 引用页面
  final String? referrer;
  
  /// 用户代理
  final String? userAgent;
  
  /// IP地址
  final String? ipAddress;
  
  /// 地理位置
  final Map<String, dynamic>? location;
  
  /// 自定义维度
  final Map<String, String>? customDimensions;
  
  /// 自定义指标
  final Map<String, num>? customMetrics;

  const TrackingEvent({
    required this.id,
    required this.name,
    required this.type,
    this.priority = EventPriority.normal,
    required this.timestamp,
    this.properties = const {},
    this.userId,
    this.sessionId,
    this.deviceId,
    this.appVersion,
    this.platform,
    this.pagePath,
    this.referrer,
    this.userAgent,
    this.ipAddress,
    this.location,
    this.customDimensions,
    this.customMetrics,
  });

  /// 创建页面浏览事件
  factory TrackingEvent.pageView({
    required String id,
    required String pagePath,
    String? pageTitle,
    String? referrer,
    Map<String, dynamic>? properties,
    String? userId,
    String? sessionId,
  }) {
    return TrackingEvent(
      id: id,
      name: 'page_view',
      type: EventType.pageView,
      priority: EventPriority.normal,
      timestamp: DateTime.now(),
      pagePath: pagePath,
      referrer: referrer,
      properties: {
        'page_title': pageTitle,
        ...?properties,
      },
      userId: userId,
      sessionId: sessionId,
    );
  }

  /// 创建用户操作事件
  factory TrackingEvent.userAction({
    required String id,
    required String action,
    String? target,
    Map<String, dynamic>? properties,
    String? userId,
    String? sessionId,
    String? pagePath,
  }) {
    return TrackingEvent(
      id: id,
      name: 'user_action',
      type: EventType.userAction,
      priority: EventPriority.normal,
      timestamp: DateTime.now(),
      pagePath: pagePath,
      properties: {
        'action': action,
        'target': target,
        ...?properties,
      },
      userId: userId,
      sessionId: sessionId,
    );
  }

  /// 创建业务事件
  factory TrackingEvent.businessEvent({
    required String id,
    required String eventName,
    Map<String, dynamic>? properties,
    EventPriority priority = EventPriority.normal,
    String? userId,
    String? sessionId,
  }) {
    return TrackingEvent(
      id: id,
      name: eventName,
      type: EventType.businessEvent,
      priority: priority,
      timestamp: DateTime.now(),
      properties: properties ?? {},
      userId: userId,
      sessionId: sessionId,
    );
  }

  /// 创建错误事件
  factory TrackingEvent.error({
    required String id,
    required String errorMessage,
    String? errorType,
    String? stackTrace,
    Map<String, dynamic>? properties,
    String? userId,
    String? sessionId,
    String? pagePath,
  }) {
    return TrackingEvent(
      id: id,
      name: 'error',
      type: EventType.errorEvent,
      priority: EventPriority.high,
      timestamp: DateTime.now(),
      pagePath: pagePath,
      properties: {
        'error_message': errorMessage,
        'error_type': errorType,
        'stack_trace': stackTrace,
        ...?properties,
      },
      userId: userId,
      sessionId: sessionId,
    );
  }

  /// 创建性能事件
  factory TrackingEvent.performance({
    required String id,
    required String metricName,
    required num value,
    String? unit,
    Map<String, dynamic>? properties,
    String? userId,
    String? sessionId,
  }) {
    return TrackingEvent(
      id: id,
      name: 'performance',
      type: EventType.performanceEvent,
      priority: EventPriority.normal,
      timestamp: DateTime.now(),
      properties: {
        'metric_name': metricName,
        'value': value,
        'unit': unit,
        ...?properties,
      },
      userId: userId,
      sessionId: sessionId,
    );
  }

  /// 是否为关键事件
  bool get isCritical => priority == EventPriority.critical;

  /// 是否为高优先级事件
  bool get isHighPriority => priority == EventPriority.high;

  /// 获取事件的唯一标识
  String get uniqueKey => '${type.name}_${name}_$timestamp';

  /// 复制并修改事件
  TrackingEvent copyWith({
    String? id,
    String? name,
    EventType? type,
    EventPriority? priority,
    DateTime? timestamp,
    Map<String, dynamic>? properties,
    String? userId,
    String? sessionId,
    String? deviceId,
    String? appVersion,
    String? platform,
    String? pagePath,
    String? referrer,
    String? userAgent,
    String? ipAddress,
    Map<String, dynamic>? location,
    Map<String, String>? customDimensions,
    Map<String, num>? customMetrics,
  }) {
    return TrackingEvent(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      timestamp: timestamp ?? this.timestamp,
      properties: properties ?? this.properties,
      userId: userId ?? this.userId,
      sessionId: sessionId ?? this.sessionId,
      deviceId: deviceId ?? this.deviceId,
      appVersion: appVersion ?? this.appVersion,
      platform: platform ?? this.platform,
      pagePath: pagePath ?? this.pagePath,
      referrer: referrer ?? this.referrer,
      userAgent: userAgent ?? this.userAgent,
      ipAddress: ipAddress ?? this.ipAddress,
      location: location ?? this.location,
      customDimensions: customDimensions ?? this.customDimensions,
      customMetrics: customMetrics ?? this.customMetrics,
    );
  }

  /// 转换为 JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type.name,
      'priority': priority.name,
      'timestamp': timestamp.toIso8601String(),
      'properties': properties,
      'userId': userId,
      'sessionId': sessionId,
      'deviceId': deviceId,
      'appVersion': appVersion,
      'platform': platform,
      'pagePath': pagePath,
      'referrer': referrer,
      'userAgent': userAgent,
      'ipAddress': ipAddress,
      'location': location,
      'customDimensions': customDimensions,
      'customMetrics': customMetrics,
    };
  }

  /// 从 JSON 创建
  factory TrackingEvent.fromJson(Map<String, dynamic> json) {
    return TrackingEvent(
      id: json['id'] as String,
      name: json['name'] as String,
      type: EventType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => EventType.customEvent,
      ),
      priority: EventPriority.values.firstWhere(
        (e) => e.name == json['priority'],
        orElse: () => EventPriority.normal,
      ),
      timestamp: DateTime.parse(json['timestamp'] as String),
      properties: json['properties'] as Map<String, dynamic>? ?? {},
      userId: json['userId'] as String?,
      sessionId: json['sessionId'] as String?,
      deviceId: json['deviceId'] as String?,
      appVersion: json['appVersion'] as String?,
      platform: json['platform'] as String?,
      pagePath: json['pagePath'] as String?,
      referrer: json['referrer'] as String?,
      userAgent: json['userAgent'] as String?,
      ipAddress: json['ipAddress'] as String?,
      location: json['location'] as Map<String, dynamic>?,
      customDimensions: json['customDimensions'] != null
          ? Map<String, String>.from(json['customDimensions'] as Map)
          : null,
      customMetrics: json['customMetrics'] != null
          ? Map<String, num>.from(json['customMetrics'] as Map)
          : null,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        type,
        priority,
        timestamp,
        properties,
        userId,
        sessionId,
        deviceId,
        appVersion,
        platform,
        pagePath,
        referrer,
        userAgent,
        ipAddress,
        location,
        customDimensions,
        customMetrics,
      ];

  @override
  String toString() {
    return 'TrackingEvent(id: $id, name: $name, type: $type, priority: $priority, timestamp: $timestamp)';
  }
}
