import '../entities/tracking_event.dart';
import '../entities/user_behavior.dart';

/// 分析统计仓库接口
/// 
/// **功能依赖**: 需要启用 analytics 模块
/// **配置项**: FEATURE_ANALYTICS
abstract class IAnalyticsRepository {
  /// 记录追踪事件
  Future<void> trackEvent(TrackingEvent event);
  
  /// 批量记录追踪事件
  Future<void> trackEvents(List<TrackingEvent> events);
  
  /// 记录用户行为
  Future<void> recordBehavior(UserBehavior behavior);
  
  /// 批量记录用户行为
  Future<void> recordBehaviors(List<UserBehavior> behaviors);
  
  /// 获取追踪事件
  Future<List<TrackingEvent>> getEvents({
    String? userId,
    String? sessionId,
    EventType? type,
    DateTime? startTime,
    DateTime? endTime,
    int? limit,
    int? offset,
  });
  
  /// 获取用户行为
  Future<List<UserBehavior>> getBehaviors({
    String? userId,
    String? sessionId,
    BehaviorType? type,
    DateTime? startTime,
    DateTime? endTime,
    int? limit,
    int? offset,
  });
  
  /// 获取事件统计
  Future<Map<String, dynamic>> getEventStats({
    String? userId,
    EventType? type,
    DateTime? startTime,
    DateTime? endTime,
  });
  
  /// 获取用户行为统计
  Future<Map<String, dynamic>> getBehaviorStats({
    String? userId,
    BehaviorType? type,
    DateTime? startTime,
    DateTime? endTime,
  });
  
  /// 获取用户活跃度
  Future<Map<String, dynamic>> getUserActivity({
    required String userId,
    DateTime? startTime,
    DateTime? endTime,
  });
  
  /// 获取页面访问统计
  Future<Map<String, dynamic>> getPageViewStats({
    String? pagePath,
    DateTime? startTime,
    DateTime? endTime,
  });
  
  /// 获取功能使用统计
  Future<Map<String, dynamic>> getFeatureUsageStats({
    String? featureName,
    DateTime? startTime,
    DateTime? endTime,
  });
  
  /// 获取用户留存数据
  Future<Map<String, dynamic>> getUserRetention({
    DateTime? startTime,
    DateTime? endTime,
    String cohortType = 'daily', // daily, weekly, monthly
  });
  
  /// 获取转化漏斗数据
  Future<Map<String, dynamic>> getConversionFunnel({
    required List<String> steps,
    DateTime? startTime,
    DateTime? endTime,
  });
  
  /// 获取用户路径分析
  Future<List<Map<String, dynamic>>> getUserJourney({
    required String userId,
    DateTime? startTime,
    DateTime? endTime,
  });
  
  /// 获取热门内容
  Future<List<Map<String, dynamic>>> getPopularContent({
    String? contentType,
    DateTime? startTime,
    DateTime? endTime,
    int? limit,
  });
  
  /// 获取用户细分数据
  Future<Map<String, dynamic>> getUserSegmentation({
    Map<String, dynamic>? criteria,
    DateTime? startTime,
    DateTime? endTime,
  });
  
  /// 获取实时统计
  Future<Map<String, dynamic>> getRealTimeStats();
  
  /// 导出分析数据
  Future<Map<String, dynamic>> exportAnalyticsData({
    DateTime? startTime,
    DateTime? endTime,
    List<String>? eventTypes,
    List<String>? behaviorTypes,
  });
  
  /// 清理过期数据
  Future<void> cleanupExpiredData(DateTime before);
  
  /// 获取数据存储大小
  Future<int> getDataStorageSize();
  
  /// 压缩历史数据
  Future<void> compressHistoricalData(DateTime before);
}
