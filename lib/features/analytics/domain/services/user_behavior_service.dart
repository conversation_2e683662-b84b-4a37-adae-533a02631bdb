import '../entities/user_behavior.dart';

/// 用户行为分析服务接口
/// 
/// **功能依赖**: 需要启用 analytics 模块
/// **配置项**: FEATURE_ANALYTICS
abstract class IUserBehaviorService {
  /// 初始化行为分析服务
  Future<void> initialize({
    String? userId,
    String? sessionId,
  });
  
  /// 开始记录用户行为
  Future<String> startBehavior({
    required BehaviorType type,
    required String name,
    String? target,
    String? pagePath,
    Map<String, dynamic>? properties,
  });
  
  /// 结束用户行为记录
  Future<void> endBehavior({
    required String behaviorId,
    BehaviorStatus status = BehaviorStatus.completed,
    Map<String, dynamic>? properties,
  });
  
  /// 记录页面访问行为
  Future<void> recordPageVisit({
    required String pagePath,
    String? pageTitle,
    String? referrer,
    Map<String, dynamic>? properties,
  });
  
  /// 记录功能使用行为
  Future<void> recordFeatureUsage({
    required String featureName,
    String? target,
    Map<String, dynamic>? properties,
  });
  
  /// 记录搜索行为
  Future<void> recordSearch({
    required String query,
    int? resultCount,
    String? category,
    Map<String, dynamic>? properties,
  });
  
  /// 记录购买行为
  Future<void> recordPurchase({
    required String productId,
    required num amount,
    String? currency,
    String? category,
    Map<String, dynamic>? properties,
  });
  
  /// 记录内容消费行为
  Future<void> recordContentConsumption({
    required String contentId,
    String? contentType,
    int? consumptionTime,
    Map<String, dynamic>? properties,
  });
  
  /// 记录社交行为
  Future<void> recordSocialAction({
    required String action,
    String? target,
    String? platform,
    Map<String, dynamic>? properties,
  });
  
  /// 记录用户偏好
  Future<void> recordPreference({
    required String preferenceType,
    required dynamic value,
    Map<String, dynamic>? properties,
  });
  
  /// 记录自定义行为
  Future<void> recordCustomBehavior({
    required String name,
    String? target,
    num? value,
    Map<String, dynamic>? properties,
  });
  
  /// 获取用户行为历史
  Future<List<UserBehavior>> getUserBehaviorHistory({
    String? userId,
    BehaviorType? type,
    DateTime? startTime,
    DateTime? endTime,
    int? limit,
  });
  
  /// 分析用户行为模式
  Future<Map<String, dynamic>> analyzeBehaviorPatterns({
    required String userId,
    DateTime? startTime,
    DateTime? endTime,
  });
  
  /// 获取用户活跃度分析
  Future<Map<String, dynamic>> getUserActivityAnalysis({
    required String userId,
    DateTime? startTime,
    DateTime? endTime,
  });
  
  /// 获取用户偏好分析
  Future<Map<String, dynamic>> getUserPreferenceAnalysis({
    required String userId,
    DateTime? startTime,
    DateTime? endTime,
  });
  
  /// 获取用户路径分析
  Future<List<Map<String, dynamic>>> getUserJourneyAnalysis({
    required String userId,
    DateTime? startTime,
    DateTime? endTime,
  });
  
  /// 获取用户细分分析
  Future<Map<String, dynamic>> getUserSegmentAnalysis({
    Map<String, dynamic>? criteria,
    DateTime? startTime,
    DateTime? endTime,
  });
  
  /// 预测用户行为
  Future<Map<String, dynamic>> predictUserBehavior({
    required String userId,
    String? behaviorType,
    int? predictionDays,
  });
  
  /// 获取用户相似度分析
  Future<List<Map<String, dynamic>>> getUserSimilarityAnalysis({
    required String userId,
    int? limit,
  });
  
  /// 获取行为异常检测
  Future<List<Map<String, dynamic>>> detectBehaviorAnomalies({
    String? userId,
    DateTime? startTime,
    DateTime? endTime,
  });
  
  /// 生成用户行为报告
  Future<Map<String, dynamic>> generateBehaviorReport({
    String? userId,
    DateTime? startTime,
    DateTime? endTime,
    List<String>? metrics,
  });
  
  /// 设置行为分析配置
  Future<void> setBehaviorAnalysisConfig({
    bool? enableRealTimeAnalysis,
    int? batchSize,
    Duration? analysisInterval,
    Map<String, dynamic>? customConfig,
  });
  
  /// 获取行为分析配置
  Future<Map<String, dynamic>> getBehaviorAnalysisConfig();
  
  /// 清理过期行为数据
  Future<void> cleanupExpiredBehaviorData(DateTime before);
  
  /// 导出用户行为数据
  Future<Map<String, dynamic>> exportBehaviorData({
    String? userId,
    DateTime? startTime,
    DateTime? endTime,
    List<String>? behaviorTypes,
  });
}
