import '../entities/tracking_event.dart';

/// 事件追踪服务接口
/// 
/// **功能依赖**: 需要启用 analytics 模块
/// **配置项**: FEATURE_ANALYTICS
abstract class IEventTrackingService {
  /// 初始化追踪服务
  Future<void> initialize({
    String? userId,
    Map<String, dynamic>? userProperties,
  });
  
  /// 设置用户ID
  Future<void> setUserId(String userId);
  
  /// 设置用户属性
  Future<void> setUserProperties(Map<String, dynamic> properties);
  
  /// 开始新会话
  Future<String> startSession();
  
  /// 结束当前会话
  Future<void> endSession();
  
  /// 获取当前会话ID
  String? getCurrentSessionId();
  
  /// 追踪页面浏览
  Future<void> trackPageView({
    required String pagePath,
    String? pageTitle,
    String? referrer,
    Map<String, dynamic>? properties,
  });
  
  /// 追踪用户操作
  Future<void> trackUserAction({
    required String action,
    String? target,
    String? category,
    String? label,
    num? value,
    Map<String, dynamic>? properties,
  });
  
  /// 追踪业务事件
  Future<void> trackBusinessEvent({
    required String eventName,
    Map<String, dynamic>? properties,
    EventPriority priority = EventPriority.normal,
  });
  
  /// 追踪错误事件
  Future<void> trackError({
    required String errorMessage,
    String? errorType,
    String? stackTrace,
    Map<String, dynamic>? properties,
  });
  
  /// 追踪性能事件
  Future<void> trackPerformance({
    required String metricName,
    required num value,
    String? unit,
    Map<String, dynamic>? properties,
  });
  
  /// 追踪自定义事件
  Future<void> trackCustomEvent({
    required String eventName,
    Map<String, dynamic>? properties,
    EventPriority priority = EventPriority.normal,
  });
  
  /// 追踪时间事件开始
  Future<void> startTimedEvent({
    required String eventName,
    Map<String, dynamic>? properties,
  });
  
  /// 追踪时间事件结束
  Future<void> endTimedEvent({
    required String eventName,
    Map<String, dynamic>? properties,
  });
  
  /// 设置全局属性
  Future<void> setGlobalProperties(Map<String, dynamic> properties);
  
  /// 移除全局属性
  Future<void> removeGlobalProperty(String key);
  
  /// 清除所有全局属性
  Future<void> clearGlobalProperties();
  
  /// 启用/禁用追踪
  Future<void> setTrackingEnabled(bool enabled);
  
  /// 检查追踪是否启用
  bool isTrackingEnabled();
  
  /// 刷新缓存的事件
  Future<void> flush();
  
  /// 重置追踪服务
  Future<void> reset();
  
  /// 获取设备信息
  Future<Map<String, dynamic>> getDeviceInfo();
  
  /// 获取应用信息
  Future<Map<String, dynamic>> getAppInfo();
  
  /// 设置事件过滤器
  void setEventFilter(bool Function(TrackingEvent event) filter);
  
  /// 移除事件过滤器
  void removeEventFilter();
  
  /// 添加事件拦截器
  void addEventInterceptor(TrackingEvent Function(TrackingEvent event) interceptor);
  
  /// 移除事件拦截器
  void removeEventInterceptor(TrackingEvent Function(TrackingEvent event) interceptor);
  
  /// 获取事件队列大小
  int getEventQueueSize();
  
  /// 清空事件队列
  Future<void> clearEventQueue();
}
