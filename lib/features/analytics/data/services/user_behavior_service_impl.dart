import 'dart:async';
import 'package:injectable/injectable.dart';
import 'package:uuid/uuid.dart';
import '../../domain/entities/user_behavior.dart';
import '../../domain/repositories/analytics_repository.dart';
import '../../domain/services/user_behavior_service.dart';
import '../../../../core/logging/logger.dart';

/// 用户行为分析服务实现
/// 
/// **功能依赖**: 需要启用 analytics 模块
/// **配置项**: FEATURE_ANALYTICS
@LazySingleton(as: IUserBehaviorService)
class UserBehaviorServiceImpl implements IUserBehaviorService {
  final IAnalyticsRepository _analyticsRepository;
  final Uuid _uuid;

  // 内部状态
  String? _userId;
  String? _sessionId;
  final Map<String, UserBehavior> _activeBehaviors = {};
  
  // 分析配置
  bool _enableRealTimeAnalysis = true;
  int _batchSize = 100;
  Duration _analysisInterval = const Duration(minutes: 5);
  Map<String, dynamic> _customConfig = {};
  
  // 定时器
  Timer? _analysisTimer;

  UserBehaviorServiceImpl(
    this._analyticsRepository,
    this._uuid,
  );

  @override
  Future<void> initialize({
    String? userId,
    String? sessionId,
  }) async {
    try {
      _userId = userId;
      _sessionId = sessionId;
      
      // 启动实时分析
      if (_enableRealTimeAnalysis) {
        _startAnalysisTimer();
      }
      
      Logger.i('User behavior service initialized');
    } catch (e) {
      Logger.e('Failed to initialize user behavior service: $e');
      rethrow;
    }
  }

  @override
  Future<String> startBehavior({
    required BehaviorType type,
    required String name,
    String? target,
    String? pagePath,
    Map<String, dynamic>? properties,
  }) async {
    try {
      final behaviorId = _uuid.v4();
      
      final behavior = UserBehavior(
        id: behaviorId,
        userId: _userId ?? 'anonymous',
        sessionId: _sessionId ?? 'unknown',
        type: type,
        name: name,
        status: BehaviorStatus.started,
        startTime: DateTime.now(),
        target: target,
        pagePath: pagePath,
        properties: properties ?? {},
      );
      
      // 记录到活跃行为中
      _activeBehaviors[behaviorId] = behavior;
      
      // 记录行为开始
      await _analyticsRepository.recordBehavior(behavior);
      
      Logger.d('Started behavior: $name (ID: $behaviorId)');
      return behaviorId;
    } catch (e) {
      Logger.e('Failed to start behavior: $e');
      rethrow;
    }
  }

  @override
  Future<void> endBehavior({
    required String behaviorId,
    BehaviorStatus status = BehaviorStatus.completed,
    Map<String, dynamic>? properties,
  }) async {
    try {
      final activeBehavior = _activeBehaviors.remove(behaviorId);
      if (activeBehavior == null) {
        Logger.w('Behavior $behaviorId not found in active behaviors');
        return;
      }
      
      final endTime = DateTime.now();
      final duration = endTime.difference(activeBehavior.startTime).inMilliseconds;
      
      final completedBehavior = activeBehavior.copyWith(
        status: status,
        endTime: endTime,
        duration: duration,
        properties: {
          ...activeBehavior.properties,
          ...?properties,
        },
      );
      
      // 记录完成的行为
      await _analyticsRepository.recordBehavior(completedBehavior);
      
      Logger.d('Ended behavior: ${activeBehavior.name} (ID: $behaviorId, Duration: ${duration}ms)');
    } catch (e) {
      Logger.e('Failed to end behavior: $e');
      rethrow;
    }
  }

  @override
  Future<void> recordPageVisit({
    required String pagePath,
    String? pageTitle,
    String? referrer,
    Map<String, dynamic>? properties,
  }) async {
    try {
      final behavior = UserBehavior.pageVisit(
        id: _uuid.v4(),
        userId: _userId ?? 'anonymous',
        sessionId: _sessionId ?? 'unknown',
        pagePath: pagePath,
        pageTitle: pageTitle,
        properties: {
          'referrer': referrer,
          ...?properties,
        },
      );
      
      await _analyticsRepository.recordBehavior(behavior);
    } catch (e) {
      Logger.e('Failed to record page visit: $e');
      rethrow;
    }
  }

  @override
  Future<void> recordFeatureUsage({
    required String featureName,
    String? target,
    Map<String, dynamic>? properties,
  }) async {
    try {
      final behavior = UserBehavior.featureUsage(
        id: _uuid.v4(),
        userId: _userId ?? 'anonymous',
        sessionId: _sessionId ?? 'unknown',
        featureName: featureName,
        target: target,
        properties: properties,
      );
      
      await _analyticsRepository.recordBehavior(behavior);
    } catch (e) {
      Logger.e('Failed to record feature usage: $e');
      rethrow;
    }
  }

  @override
  Future<void> recordSearch({
    required String query,
    int? resultCount,
    String? category,
    Map<String, dynamic>? properties,
  }) async {
    try {
      final behavior = UserBehavior.search(
        id: _uuid.v4(),
        userId: _userId ?? 'anonymous',
        sessionId: _sessionId ?? 'unknown',
        query: query,
        resultCount: resultCount,
        properties: {
          'category': category,
          ...?properties,
        },
      );
      
      await _analyticsRepository.recordBehavior(behavior);
    } catch (e) {
      Logger.e('Failed to record search: $e');
      rethrow;
    }
  }

  @override
  Future<void> recordPurchase({
    required String productId,
    required num amount,
    String? currency,
    String? category,
    Map<String, dynamic>? properties,
  }) async {
    try {
      final behavior = UserBehavior.purchase(
        id: _uuid.v4(),
        userId: _userId ?? 'anonymous',
        sessionId: _sessionId ?? 'unknown',
        productId: productId,
        amount: amount,
        currency: currency,
        properties: {
          'category': category,
          ...?properties,
        },
      );
      
      await _analyticsRepository.recordBehavior(behavior);
    } catch (e) {
      Logger.e('Failed to record purchase: $e');
      rethrow;
    }
  }

  @override
  Future<void> recordContentConsumption({
    required String contentId,
    String? contentType,
    int? consumptionTime,
    Map<String, dynamic>? properties,
  }) async {
    try {
      final behavior = UserBehavior.contentConsumption(
        id: _uuid.v4(),
        userId: _userId ?? 'anonymous',
        sessionId: _sessionId ?? 'unknown',
        contentId: contentId,
        contentType: contentType,
        consumptionTime: consumptionTime,
        properties: properties,
      );
      
      await _analyticsRepository.recordBehavior(behavior);
    } catch (e) {
      Logger.e('Failed to record content consumption: $e');
      rethrow;
    }
  }

  @override
  Future<void> recordSocialAction({
    required String action,
    String? target,
    String? platform,
    Map<String, dynamic>? properties,
  }) async {
    try {
      final behavior = UserBehavior(
        id: _uuid.v4(),
        userId: _userId ?? 'anonymous',
        sessionId: _sessionId ?? 'unknown',
        type: BehaviorType.social,
        name: 'social_action',
        startTime: DateTime.now(),
        target: target,
        properties: {
          'action': action,
          'platform': platform,
          ...?properties,
        },
      );
      
      await _analyticsRepository.recordBehavior(behavior);
    } catch (e) {
      Logger.e('Failed to record social action: $e');
      rethrow;
    }
  }

  @override
  Future<void> recordPreference({
    required String preferenceType,
    required dynamic value,
    Map<String, dynamic>? properties,
  }) async {
    try {
      final behavior = UserBehavior(
        id: _uuid.v4(),
        userId: _userId ?? 'anonymous',
        sessionId: _sessionId ?? 'unknown',
        type: BehaviorType.preference,
        name: 'preference_set',
        startTime: DateTime.now(),
        properties: {
          'preference_type': preferenceType,
          'value': value,
          ...?properties,
        },
      );
      
      await _analyticsRepository.recordBehavior(behavior);
    } catch (e) {
      Logger.e('Failed to record preference: $e');
      rethrow;
    }
  }

  @override
  Future<void> recordCustomBehavior({
    required String name,
    String? target,
    num? value,
    Map<String, dynamic>? properties,
  }) async {
    try {
      final behavior = UserBehavior(
        id: _uuid.v4(),
        userId: _userId ?? 'anonymous',
        sessionId: _sessionId ?? 'unknown',
        type: BehaviorType.custom,
        name: name,
        startTime: DateTime.now(),
        target: target,
        value: value,
        properties: properties ?? {},
      );
      
      await _analyticsRepository.recordBehavior(behavior);
    } catch (e) {
      Logger.e('Failed to record custom behavior: $e');
      rethrow;
    }
  }

  @override
  Future<List<UserBehavior>> getUserBehaviorHistory({
    String? userId,
    BehaviorType? type,
    DateTime? startTime,
    DateTime? endTime,
    int? limit,
  }) async {
    try {
      return await _analyticsRepository.getBehaviors(
        userId: userId ?? _userId,
        type: type,
        startTime: startTime,
        endTime: endTime,
        limit: limit,
      );
    } catch (e) {
      Logger.e('Failed to get user behavior history: $e');
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>> analyzeBehaviorPatterns({
    required String userId,
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    try {
      final behaviors = await _analyticsRepository.getBehaviors(
        userId: userId,
        startTime: startTime,
        endTime: endTime,
      );
      
      return _analyzeBehaviorPatterns(behaviors);
    } catch (e) {
      Logger.e('Failed to analyze behavior patterns: $e');
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>> getUserActivityAnalysis({
    required String userId,
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    try {
      return await _analyticsRepository.getUserActivity(
        userId: userId,
        startTime: startTime,
        endTime: endTime,
      );
    } catch (e) {
      Logger.e('Failed to get user activity analysis: $e');
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>> getUserPreferenceAnalysis({
    required String userId,
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    try {
      final behaviors = await _analyticsRepository.getBehaviors(
        userId: userId,
        type: BehaviorType.preference,
        startTime: startTime,
        endTime: endTime,
      );
      
      return _analyzeUserPreferences(behaviors);
    } catch (e) {
      Logger.e('Failed to get user preference analysis: $e');
      rethrow;
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getUserJourneyAnalysis({
    required String userId,
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    try {
      return await _analyticsRepository.getUserJourney(
        userId: userId,
        startTime: startTime,
        endTime: endTime,
      );
    } catch (e) {
      Logger.e('Failed to get user journey analysis: $e');
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>> getUserSegmentAnalysis({
    Map<String, dynamic>? criteria,
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    try {
      return await _analyticsRepository.getUserSegmentation(
        criteria: criteria,
        startTime: startTime,
        endTime: endTime,
      );
    } catch (e) {
      Logger.e('Failed to get user segment analysis: $e');
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>> predictUserBehavior({
    required String userId,
    String? behaviorType,
    int? predictionDays,
  }) async {
    try {
      // 获取历史行为数据
      final behaviors = await _analyticsRepository.getBehaviors(
        userId: userId,
        startTime: DateTime.now().subtract(const Duration(days: 30)),
      );
      
      return _predictBehavior(behaviors, behaviorType, predictionDays ?? 7);
    } catch (e) {
      Logger.e('Failed to predict user behavior: $e');
      rethrow;
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getUserSimilarityAnalysis({
    required String userId,
    int? limit,
  }) async {
    try {
      // 简化的相似度分析实现
      final userBehaviors = await _analyticsRepository.getBehaviors(
        userId: userId,
        startTime: DateTime.now().subtract(const Duration(days: 30)),
      );
      
      return _findSimilarUsers(userBehaviors, limit ?? 10);
    } catch (e) {
      Logger.e('Failed to get user similarity analysis: $e');
      rethrow;
    }
  }

  @override
  Future<List<Map<String, dynamic>>> detectBehaviorAnomalies({
    String? userId,
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    try {
      final behaviors = await _analyticsRepository.getBehaviors(
        userId: userId,
        startTime: startTime,
        endTime: endTime,
      );
      
      return _detectAnomalies(behaviors);
    } catch (e) {
      Logger.e('Failed to detect behavior anomalies: $e');
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>> generateBehaviorReport({
    String? userId,
    DateTime? startTime,
    DateTime? endTime,
    List<String>? metrics,
  }) async {
    try {
      final behaviors = await _analyticsRepository.getBehaviors(
        userId: userId,
        startTime: startTime,
        endTime: endTime,
      );
      
      return _generateReport(behaviors, metrics);
    } catch (e) {
      Logger.e('Failed to generate behavior report: $e');
      rethrow;
    }
  }

  @override
  Future<void> setBehaviorAnalysisConfig({
    bool? enableRealTimeAnalysis,
    int? batchSize,
    Duration? analysisInterval,
    Map<String, dynamic>? customConfig,
  }) async {
    if (enableRealTimeAnalysis != null) {
      _enableRealTimeAnalysis = enableRealTimeAnalysis;
      if (_enableRealTimeAnalysis) {
        _startAnalysisTimer();
      } else {
        _analysisTimer?.cancel();
      }
    }
    
    if (batchSize != null) _batchSize = batchSize;
    if (analysisInterval != null) _analysisInterval = analysisInterval;
    if (customConfig != null) _customConfig.addAll(customConfig);
  }

  @override
  Future<Map<String, dynamic>> getBehaviorAnalysisConfig() async {
    return {
      'enableRealTimeAnalysis': _enableRealTimeAnalysis,
      'batchSize': _batchSize,
      'analysisInterval': _analysisInterval.inMilliseconds,
      'customConfig': _customConfig,
    };
  }

  @override
  Future<void> cleanupExpiredBehaviorData(DateTime before) async {
    try {
      await _analyticsRepository.cleanupExpiredData(before);
    } catch (e) {
      Logger.e('Failed to cleanup expired behavior data: $e');
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>> exportBehaviorData({
    String? userId,
    DateTime? startTime,
    DateTime? endTime,
    List<String>? behaviorTypes,
  }) async {
    try {
      return await _analyticsRepository.exportAnalyticsData(
        startTime: startTime,
        endTime: endTime,
        behaviorTypes: behaviorTypes,
      );
    } catch (e) {
      Logger.e('Failed to export behavior data: $e');
      rethrow;
    }
  }

  /// 启动分析定时器
  void _startAnalysisTimer() {
    _analysisTimer?.cancel();
    _analysisTimer = Timer.periodic(_analysisInterval, (_) {
      _performRealTimeAnalysis();
    });
  }

  /// 执行实时分析
  Future<void> _performRealTimeAnalysis() async {
    try {
      // 分析活跃行为
      for (final behavior in _activeBehaviors.values) {
        final duration = DateTime.now().difference(behavior.startTime);
        
        // 检测长时间未完成的行为
        if (duration.inMinutes > 30) {
          Logger.w('Long-running behavior detected: ${behavior.name} (${duration.inMinutes} minutes)');
        }
      }
    } catch (e) {
      Logger.e('Failed to perform real-time analysis: $e');
    }
  }

  /// 分析行为模式
  Map<String, dynamic> _analyzeBehaviorPatterns(List<UserBehavior> behaviors) {
    final patterns = <String, dynamic>{};
    
    // 按类型分组
    final typeGroups = <BehaviorType, List<UserBehavior>>{};
    for (final behavior in behaviors) {
      typeGroups.putIfAbsent(behavior.type, () => []).add(behavior);
    }
    
    // 分析每种类型的模式
    for (final entry in typeGroups.entries) {
      final type = entry.key;
      final typeBehaviors = entry.value;
      
      patterns[type.name] = {
        'count': typeBehaviors.length,
        'average_duration': _calculateAverageDuration(typeBehaviors),
        'frequency': _calculateFrequency(typeBehaviors),
        'peak_hours': _findPeakHours(typeBehaviors),
      };
    }
    
    return patterns;
  }

  /// 分析用户偏好
  Map<String, dynamic> _analyzeUserPreferences(List<UserBehavior> behaviors) {
    final preferences = <String, dynamic>{};
    
    for (final behavior in behaviors) {
      final preferenceType = behavior.properties['preference_type'] as String?;
      final value = behavior.properties['value'];
      
      if (preferenceType != null && value != null) {
        preferences[preferenceType] = value;
      }
    }
    
    return {
      'preferences': preferences,
      'preference_count': preferences.length,
      'last_updated': behaviors.isNotEmpty 
          ? behaviors.last.startTime.toIso8601String()
          : null,
    };
  }

  /// 预测用户行为
  Map<String, dynamic> _predictBehavior(
    List<UserBehavior> behaviors,
    String? behaviorType,
    int predictionDays,
  ) {
    // 简化的预测算法
    final predictions = <String, dynamic>{};
    
    // 计算历史频率
    final typeFrequency = <String, int>{};
    for (final behavior in behaviors) {
      typeFrequency[behavior.name] = (typeFrequency[behavior.name] ?? 0) + 1;
    }
    
    // 基于历史频率预测
    for (final entry in typeFrequency.entries) {
      final frequency = entry.value / 30.0; // 每天的频率
      predictions[entry.key] = {
        'predicted_count': (frequency * predictionDays).round(),
        'confidence': _calculateConfidence(entry.value),
      };
    }
    
    return {
      'predictions': predictions,
      'prediction_period_days': predictionDays,
      'based_on_days': 30,
    };
  }

  /// 查找相似用户
  List<Map<String, dynamic>> _findSimilarUsers(
    List<UserBehavior> userBehaviors,
    int limit,
  ) {
    // 简化的相似度计算
    return List.generate(limit, (index) => {
      'user_id': 'similar_user_$index',
      'similarity_score': 0.8 - (index * 0.1),
      'common_behaviors': ['page_visit', 'feature_usage'],
    });
  }

  /// 检测异常行为
  List<Map<String, dynamic>> _detectAnomalies(List<UserBehavior> behaviors) {
    final anomalies = <Map<String, dynamic>>[];
    
    // 检测异常长的会话
    for (final behavior in behaviors) {
      if (behavior.actualDuration != null && behavior.actualDuration! > 3600000) { // 1小时
        anomalies.add({
          'type': 'long_session',
          'behavior_id': behavior.id,
          'duration': behavior.actualDuration,
          'severity': 'medium',
        });
      }
    }
    
    return anomalies;
  }

  /// 生成行为报告
  Map<String, dynamic> _generateReport(
    List<UserBehavior> behaviors,
    List<String>? metrics,
  ) {
    return {
      'total_behaviors': behaviors.length,
      'unique_sessions': behaviors.map((b) => b.sessionId).toSet().length,
      'behavior_types': _groupByType(behaviors),
      'average_duration': _calculateAverageDuration(behaviors),
      'generated_at': DateTime.now().toIso8601String(),
    };
  }

  /// 按类型分组
  Map<String, int> _groupByType(List<UserBehavior> behaviors) {
    final groups = <String, int>{};
    for (final behavior in behaviors) {
      groups[behavior.type.name] = (groups[behavior.type.name] ?? 0) + 1;
    }
    return groups;
  }

  /// 计算平均持续时间
  double _calculateAverageDuration(List<UserBehavior> behaviors) {
    final durations = behaviors
        .where((b) => b.actualDuration != null)
        .map((b) => b.actualDuration!)
        .toList();
    
    if (durations.isEmpty) return 0.0;
    
    return durations.reduce((a, b) => a + b) / durations.length;
  }

  /// 计算频率
  double _calculateFrequency(List<UserBehavior> behaviors) {
    if (behaviors.isEmpty) return 0.0;
    
    final timeSpan = behaviors.last.startTime.difference(behaviors.first.startTime);
    return behaviors.length / timeSpan.inDays.clamp(1, double.infinity);
  }

  /// 查找高峰时段
  List<int> _findPeakHours(List<UserBehavior> behaviors) {
    final hourCounts = <int, int>{};
    
    for (final behavior in behaviors) {
      final hour = behavior.startTime.hour;
      hourCounts[hour] = (hourCounts[hour] ?? 0) + 1;
    }
    
    final sortedHours = hourCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sortedHours.take(3).map((e) => e.key).toList();
  }

  /// 计算置信度
  double _calculateConfidence(int sampleSize) {
    return (sampleSize / 100.0).clamp(0.0, 1.0);
  }
}
