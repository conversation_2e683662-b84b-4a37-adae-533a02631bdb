import 'dart:async';
import 'dart:io';
import 'package:injectable/injectable.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:uuid/uuid.dart';
import '../../domain/entities/tracking_event.dart';
import '../../domain/repositories/analytics_repository.dart';
import '../../domain/services/event_tracking_service.dart';
import '../../../../core/logging/logger.dart';

/// 事件追踪服务实现
/// 
/// **功能依赖**: 需要启用 analytics 模块
/// **配置项**: FEATURE_ANALYTICS
@LazySingleton(as: IEventTrackingService)
class EventTrackingServiceImpl implements IEventTrackingService {
  final IAnalyticsRepository _analyticsRepository;
  final DeviceInfoPlugin _deviceInfo;
  final Uuid _uuid;

  // 内部状态
  String? _userId;
  String? _sessionId;
  Map<String, dynamic> _userProperties = {};
  Map<String, dynamic> _globalProperties = {};
  Map<String, DateTime> _timedEvents = {};
  bool _trackingEnabled = true;
  
  // 事件过滤器和拦截器
  bool Function(TrackingEvent event)? _eventFilter;
  final List<TrackingEvent Function(TrackingEvent event)> _eventInterceptors = [];
  
  // 事件队列
  final List<TrackingEvent> _eventQueue = [];
  Timer? _flushTimer;
  
  // 设备和应用信息缓存
  Map<String, dynamic>? _deviceInfoCache;
  Map<String, dynamic>? _appInfoCache;

  EventTrackingServiceImpl(
    this._analyticsRepository,
    this._deviceInfo,
    this._uuid,
  );

  @override
  Future<void> initialize({
    String? userId,
    Map<String, dynamic>? userProperties,
  }) async {
    try {
      // 设置用户信息
      if (userId != null) {
        _userId = userId;
      }
      
      if (userProperties != null) {
        _userProperties = Map.from(userProperties);
      }
      
      // 缓存设备和应用信息
      _deviceInfoCache = await getDeviceInfo();
      _appInfoCache = await getAppInfo();
      
      // 开始新会话
      await startSession();
      
      // 启动定时刷新
      _startFlushTimer();
      
      Logger.i('Event tracking service initialized');
    } catch (e) {
      Logger.e('Failed to initialize event tracking service: $e');
      rethrow;
    }
  }

  @override
  Future<void> setUserId(String userId) async {
    _userId = userId;
    
    // 记录用户ID设置事件
    await trackCustomEvent(
      eventName: 'user_id_set',
      properties: {'user_id': userId},
    );
  }

  @override
  Future<void> setUserProperties(Map<String, dynamic> properties) async {
    _userProperties.addAll(properties);
    
    // 记录用户属性更新事件
    await trackCustomEvent(
      eventName: 'user_properties_updated',
      properties: {'updated_properties': properties.keys.toList()},
    );
  }

  @override
  Future<String> startSession() async {
    _sessionId = _uuid.v4();
    
    // 记录会话开始事件
    await trackCustomEvent(
      eventName: 'session_start',
      properties: {
        'session_id': _sessionId,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
    
    return _sessionId!;
  }

  @override
  Future<void> endSession() async {
    if (_sessionId != null) {
      // 记录会话结束事件
      await trackCustomEvent(
        eventName: 'session_end',
        properties: {
          'session_id': _sessionId,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
      
      // 刷新所有待发送事件
      await flush();
      
      _sessionId = null;
    }
  }

  @override
  String? getCurrentSessionId() => _sessionId;

  @override
  Future<void> trackPageView({
    required String pagePath,
    String? pageTitle,
    String? referrer,
    Map<String, dynamic>? properties,
  }) async {
    if (!_trackingEnabled) return;

    final event = TrackingEvent.pageView(
      id: _uuid.v4(),
      pagePath: pagePath,
      pageTitle: pageTitle,
      referrer: referrer,
      properties: {
        ...?properties,
        ..._globalProperties,
      },
      userId: _userId,
      sessionId: _sessionId,
    );

    await _processEvent(event);
  }

  @override
  Future<void> trackUserAction({
    required String action,
    String? target,
    String? category,
    String? label,
    num? value,
    Map<String, dynamic>? properties,
  }) async {
    if (!_trackingEnabled) return;

    final event = TrackingEvent.userAction(
      id: _uuid.v4(),
      action: action,
      target: target,
      properties: {
        'category': category,
        'label': label,
        'value': value,
        ...?properties,
        ..._globalProperties,
      },
      userId: _userId,
      sessionId: _sessionId,
    );

    await _processEvent(event);
  }

  @override
  Future<void> trackBusinessEvent({
    required String eventName,
    Map<String, dynamic>? properties,
    EventPriority priority = EventPriority.normal,
  }) async {
    if (!_trackingEnabled) return;

    final event = TrackingEvent.businessEvent(
      id: _uuid.v4(),
      eventName: eventName,
      properties: {
        ...?properties,
        ..._globalProperties,
      },
      priority: priority,
      userId: _userId,
      sessionId: _sessionId,
    );

    await _processEvent(event);
  }

  @override
  Future<void> trackError({
    required String errorMessage,
    String? errorType,
    String? stackTrace,
    Map<String, dynamic>? properties,
  }) async {
    if (!_trackingEnabled) return;

    final event = TrackingEvent.error(
      id: _uuid.v4(),
      errorMessage: errorMessage,
      errorType: errorType,
      stackTrace: stackTrace,
      properties: {
        ...?properties,
        ..._globalProperties,
      },
      userId: _userId,
      sessionId: _sessionId,
    );

    await _processEvent(event);
  }

  @override
  Future<void> trackPerformance({
    required String metricName,
    required num value,
    String? unit,
    Map<String, dynamic>? properties,
  }) async {
    if (!_trackingEnabled) return;

    final event = TrackingEvent.performance(
      id: _uuid.v4(),
      metricName: metricName,
      value: value,
      unit: unit,
      properties: {
        ...?properties,
        ..._globalProperties,
      },
      userId: _userId,
      sessionId: _sessionId,
    );

    await _processEvent(event);
  }

  @override
  Future<void> trackCustomEvent({
    required String eventName,
    Map<String, dynamic>? properties,
    EventPriority priority = EventPriority.normal,
  }) async {
    if (!_trackingEnabled) return;

    final event = TrackingEvent(
      id: _uuid.v4(),
      name: eventName,
      type: EventType.customEvent,
      priority: priority,
      timestamp: DateTime.now(),
      properties: {
        ...?properties,
        ..._globalProperties,
      },
      userId: _userId,
      sessionId: _sessionId,
      deviceId: _deviceInfoCache?['device_id'],
      appVersion: _appInfoCache?['version'],
      platform: _deviceInfoCache?['platform'],
    );

    await _processEvent(event);
  }

  @override
  Future<void> startTimedEvent({
    required String eventName,
    Map<String, dynamic>? properties,
  }) async {
    _timedEvents[eventName] = DateTime.now();
    
    // 记录计时事件开始
    await trackCustomEvent(
      eventName: '${eventName}_start',
      properties: {
        'timed_event': true,
        ...?properties,
      },
    );
  }

  @override
  Future<void> endTimedEvent({
    required String eventName,
    Map<String, dynamic>? properties,
  }) async {
    final startTime = _timedEvents.remove(eventName);
    if (startTime == null) {
      Logger.w('Timed event "$eventName" was not started');
      return;
    }

    final duration = DateTime.now().difference(startTime);
    
    await trackCustomEvent(
      eventName: '${eventName}_end',
      properties: {
        'timed_event': true,
        'duration_ms': duration.inMilliseconds,
        'duration_seconds': duration.inSeconds,
        ...?properties,
      },
    );
  }

  @override
  Future<void> setGlobalProperties(Map<String, dynamic> properties) async {
    _globalProperties.addAll(properties);
  }

  @override
  Future<void> removeGlobalProperty(String key) async {
    _globalProperties.remove(key);
  }

  @override
  Future<void> clearGlobalProperties() async {
    _globalProperties.clear();
  }

  @override
  Future<void> setTrackingEnabled(bool enabled) async {
    _trackingEnabled = enabled;
    
    if (!enabled) {
      // 清空事件队列
      _eventQueue.clear();
      // 停止定时刷新
      _flushTimer?.cancel();
    } else {
      // 重新启动定时刷新
      _startFlushTimer();
    }
  }

  @override
  bool isTrackingEnabled() => _trackingEnabled;

  @override
  Future<void> flush() async {
    if (_eventQueue.isEmpty) return;

    try {
      final eventsToSend = List<TrackingEvent>.from(_eventQueue);
      _eventQueue.clear();
      
      await _analyticsRepository.trackEvents(eventsToSend);
      Logger.d('Flushed ${eventsToSend.length} events');
    } catch (e) {
      Logger.e('Failed to flush events: $e');
      // 重新添加到队列
      _eventQueue.addAll(_eventQueue);
    }
  }

  @override
  Future<void> reset() async {
    _userId = null;
    _sessionId = null;
    _userProperties.clear();
    _globalProperties.clear();
    _timedEvents.clear();
    _eventQueue.clear();
    _eventFilter = null;
    _eventInterceptors.clear();
    _flushTimer?.cancel();
    
    Logger.i('Event tracking service reset');
  }

  @override
  Future<Map<String, dynamic>> getDeviceInfo() async {
    if (_deviceInfoCache != null) return _deviceInfoCache!;

    try {
      final deviceInfo = <String, dynamic>{};
      
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        deviceInfo.addAll({
          'platform': 'android',
          'device_id': androidInfo.id,
          'model': androidInfo.model,
          'manufacturer': androidInfo.manufacturer,
          'version': androidInfo.version.release,
          'sdk_int': androidInfo.version.sdkInt,
        });
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        deviceInfo.addAll({
          'platform': 'ios',
          'device_id': iosInfo.identifierForVendor,
          'model': iosInfo.model,
          'name': iosInfo.name,
          'version': iosInfo.systemVersion,
        });
      }
      
      return deviceInfo;
    } catch (e) {
      Logger.e('Failed to get device info: $e');
      return {'platform': Platform.operatingSystem};
    }
  }

  @override
  Future<Map<String, dynamic>> getAppInfo() async {
    if (_appInfoCache != null) return _appInfoCache!;

    try {
      final packageInfo = await PackageInfo.fromPlatform();
      return {
        'app_name': packageInfo.appName,
        'package_name': packageInfo.packageName,
        'version': packageInfo.version,
        'build_number': packageInfo.buildNumber,
      };
    } catch (e) {
      Logger.e('Failed to get app info: $e');
      return {};
    }
  }

  @override
  void setEventFilter(bool Function(TrackingEvent event) filter) {
    _eventFilter = filter;
  }

  @override
  void removeEventFilter() {
    _eventFilter = null;
  }

  @override
  void addEventInterceptor(TrackingEvent Function(TrackingEvent event) interceptor) {
    _eventInterceptors.add(interceptor);
  }

  @override
  void removeEventInterceptor(TrackingEvent Function(TrackingEvent event) interceptor) {
    _eventInterceptors.remove(interceptor);
  }

  @override
  int getEventQueueSize() => _eventQueue.length;

  @override
  Future<void> clearEventQueue() async {
    _eventQueue.clear();
  }

  /// 处理事件
  Future<void> _processEvent(TrackingEvent event) async {
    try {
      // 应用事件拦截器
      var processedEvent = event;
      for (final interceptor in _eventInterceptors) {
        processedEvent = interceptor(processedEvent);
      }
      
      // 应用事件过滤器
      if (_eventFilter != null && !_eventFilter!(processedEvent)) {
        return;
      }
      
      // 添加设备和应用信息
      processedEvent = processedEvent.copyWith(
        deviceId: _deviceInfoCache?['device_id'],
        appVersion: _appInfoCache?['version'],
        platform: _deviceInfoCache?['platform'],
      );
      
      // 添加到队列
      _eventQueue.add(processedEvent);
      
      // 如果是关键事件，立即发送
      if (processedEvent.isCritical) {
        await _analyticsRepository.trackEvent(processedEvent);
        _eventQueue.remove(processedEvent);
      }
    } catch (e) {
      Logger.e('Failed to process event: $e');
    }
  }

  /// 启动定时刷新
  void _startFlushTimer() {
    _flushTimer?.cancel();
    _flushTimer = Timer.periodic(
      const Duration(seconds: 30),
      (_) => flush(),
    );
  }
}
