import 'package:injectable/injectable.dart';
import 'package:uuid/uuid.dart';
import '../../domain/entities/tracking_event.dart';
import '../../domain/services/event_tracking_service.dart';

/// NoOp事件追踪服务实现
/// 
/// 当analytics模块禁用时使用的空实现
/// 所有追踪操作返回成功状态，不执行实际追踪逻辑
@LazySingleton(as: IEventTrackingService)
class NoOpEventTrackingService implements IEventTrackingService {
  final Uuid _uuid = const Uuid();
  String? _sessionId;
  bool _trackingEnabled = false; // 默认禁用

  @override
  Future<void> initialize({
    String? userId,
    Map<String, dynamic>? userProperties,
  }) async {
    // 空实现，不执行任何操作
  }

  @override
  Future<void> setUserId(String userId) async {
    // 空实现，不执行任何操作
  }

  @override
  Future<void> setUserProperties(Map<String, dynamic> properties) async {
    // 空实现，不执行任何操作
  }

  @override
  Future<String> startSession() async {
    // 生成一个假的会话ID
    _sessionId = _uuid.v4();
    return _sessionId!;
  }

  @override
  Future<void> endSession() async {
    // 清除会话ID
    _sessionId = null;
  }

  @override
  String? getCurrentSessionId() => _sessionId;

  @override
  Future<void> trackPageView({
    required String pagePath,
    String? pageTitle,
    String? referrer,
    Map<String, dynamic>? properties,
  }) async {
    // 空实现，不执行任何操作
  }

  @override
  Future<void> trackUserAction({
    required String action,
    String? target,
    String? category,
    String? label,
    num? value,
    Map<String, dynamic>? properties,
  }) async {
    // 空实现，不执行任何操作
  }

  @override
  Future<void> trackBusinessEvent({
    required String eventName,
    Map<String, dynamic>? properties,
    EventPriority priority = EventPriority.normal,
  }) async {
    // 空实现，不执行任何操作
  }

  @override
  Future<void> trackError({
    required String errorMessage,
    String? errorType,
    String? stackTrace,
    Map<String, dynamic>? properties,
  }) async {
    // 空实现，不执行任何操作
  }

  @override
  Future<void> trackPerformance({
    required String metricName,
    required num value,
    String? unit,
    Map<String, dynamic>? properties,
  }) async {
    // 空实现，不执行任何操作
  }

  @override
  Future<void> trackCustomEvent({
    required String eventName,
    Map<String, dynamic>? properties,
    EventPriority priority = EventPriority.normal,
  }) async {
    // 空实现，不执行任何操作
  }

  @override
  Future<void> startTimedEvent({
    required String eventName,
    Map<String, dynamic>? properties,
  }) async {
    // 空实现，不执行任何操作
  }

  @override
  Future<void> endTimedEvent({
    required String eventName,
    Map<String, dynamic>? properties,
  }) async {
    // 空实现，不执行任何操作
  }

  @override
  Future<void> setGlobalProperties(Map<String, dynamic> properties) async {
    // 空实现，不执行任何操作
  }

  @override
  Future<void> removeGlobalProperty(String key) async {
    // 空实现，不执行任何操作
  }

  @override
  Future<void> clearGlobalProperties() async {
    // 空实现，不执行任何操作
  }

  @override
  Future<void> setTrackingEnabled(bool enabled) async {
    // 记录状态但不执行实际操作
    _trackingEnabled = enabled;
  }

  @override
  bool isTrackingEnabled() => _trackingEnabled;

  @override
  Future<void> flush() async {
    // 空实现，不执行任何操作
  }

  @override
  Future<void> reset() async {
    // 重置内部状态
    _sessionId = null;
    _trackingEnabled = false;
  }

  @override
  Future<Map<String, dynamic>> getDeviceInfo() async {
    // 返回空设备信息
    return {
      'platform': 'unknown',
      'device_id': 'noop-device',
      'model': 'NoOp Device',
      'manufacturer': 'NoOp',
      'version': '1.0.0',
    };
  }

  @override
  Future<Map<String, dynamic>> getAppInfo() async {
    // 返回空应用信息
    return {
      'app_name': 'NoOp App',
      'package_name': 'com.noop.app',
      'version': '1.0.0',
      'build_number': '1',
    };
  }

  @override
  void setEventFilter(bool Function(TrackingEvent event) filter) {
    // 空实现，不执行任何操作
  }

  @override
  void removeEventFilter() {
    // 空实现，不执行任何操作
  }

  @override
  void addEventInterceptor(TrackingEvent Function(TrackingEvent event) interceptor) {
    // 空实现，不执行任何操作
  }

  @override
  void removeEventInterceptor(TrackingEvent Function(TrackingEvent event) interceptor) {
    // 空实现，不执行任何操作
  }

  @override
  int getEventQueueSize() => 0; // 始终返回0

  @override
  Future<void> clearEventQueue() async {
    // 空实现，不执行任何操作
  }
}
