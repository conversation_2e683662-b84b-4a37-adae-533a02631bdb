import '../entities/tracking_event.dart';
import '../entities/user_behavior.dart';

/// 分析统计远程数据源接口
/// 
/// **功能依赖**: 需要启用 analytics 模块
/// **配置项**: FEATURE_ANALYTICS
abstract class IAnalyticsRemoteDataSource {
  /// 发送追踪事件
  Future<void> sendEvent(TrackingEvent event);
  
  /// 批量发送追踪事件
  Future<void> sendEvents(List<TrackingEvent> events);
  
  /// 发送用户行为
  Future<void> sendBehavior(UserBehavior behavior);
  
  /// 批量发送用户行为
  Future<void> sendBehaviors(List<UserBehavior> behaviors);
  
  /// 获取追踪事件
  Future<List<TrackingEvent>> getEvents({
    String? userId,
    String? sessionId,
    EventType? type,
    DateTime? startTime,
    DateTime? endTime,
    int? limit,
    int? offset,
  });
  
  /// 获取用户行为
  Future<List<UserBehavior>> getBehaviors({
    String? userId,
    String? sessionId,
    BehaviorType? type,
    DateTime? startTime,
    DateTime? endTime,
    int? limit,
    int? offset,
  });
  
  /// 获取事件统计
  Future<Map<String, dynamic>> getEventStats({
    String? userId,
    EventType? type,
    DateTime? startTime,
    DateTime? endTime,
  });
  
  /// 获取用户行为统计
  Future<Map<String, dynamic>> getBehaviorStats({
    String? userId,
    BehaviorType? type,
    DateTime? startTime,
    DateTime? endTime,
  });
  
  /// 获取用户活跃度
  Future<Map<String, dynamic>> getUserActivity({
    required String userId,
    DateTime? startTime,
    DateTime? endTime,
  });
  
  /// 获取页面访问统计
  Future<Map<String, dynamic>> getPageViewStats({
    String? pagePath,
    DateTime? startTime,
    DateTime? endTime,
  });
  
  /// 获取功能使用统计
  Future<Map<String, dynamic>> getFeatureUsageStats({
    String? featureName,
    DateTime? startTime,
    DateTime? endTime,
  });
  
  /// 获取用户留存数据
  Future<Map<String, dynamic>> getUserRetention({
    DateTime? startTime,
    DateTime? endTime,
    String cohortType = 'daily',
  });
  
  /// 获取转化漏斗数据
  Future<Map<String, dynamic>> getConversionFunnel({
    required List<String> steps,
    DateTime? startTime,
    DateTime? endTime,
  });
  
  /// 获取用户路径分析
  Future<List<Map<String, dynamic>>> getUserJourney({
    required String userId,
    DateTime? startTime,
    DateTime? endTime,
  });
  
  /// 获取热门内容
  Future<List<Map<String, dynamic>>> getPopularContent({
    String? contentType,
    DateTime? startTime,
    DateTime? endTime,
    int? limit,
  });
  
  /// 获取用户细分数据
  Future<Map<String, dynamic>> getUserSegmentation({
    Map<String, dynamic>? criteria,
    DateTime? startTime,
    DateTime? endTime,
  });
  
  /// 获取实时统计
  Future<Map<String, dynamic>> getRealTimeStats();
  
  /// 同步本地数据到远程
  Future<void> syncLocalData({
    List<TrackingEvent>? events,
    List<UserBehavior>? behaviors,
  });
  
  /// 获取服务器配置
  Future<Map<String, dynamic>> getServerConfig();
  
  /// 验证API密钥
  Future<bool> validateApiKey(String apiKey);
  
  /// 获取数据导出URL
  Future<String> getDataExportUrl({
    DateTime? startTime,
    DateTime? endTime,
    List<String>? eventTypes,
    List<String>? behaviorTypes,
  });
}
