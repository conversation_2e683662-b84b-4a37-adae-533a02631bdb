import 'package:injectable/injectable.dart';
import '../../domain/entities/tracking_event.dart';
import '../../domain/entities/user_behavior.dart';
import '../../domain/repositories/analytics_repository.dart';

/// NoOp分析统计仓库实现
/// 
/// 当analytics模块禁用时使用的空实现
/// 所有分析操作返回空数据或成功状态，不执行实际分析逻辑
@LazySingleton(as: IAnalyticsRepository)
class NoOpAnalyticsRepository implements IAnalyticsRepository {
  @override
  Future<void> trackEvent(TrackingEvent event) async {
    // 空实现，不执行任何操作
  }

  @override
  Future<void> trackEvents(List<TrackingEvent> events) async {
    // 空实现，不执行任何操作
  }

  @override
  Future<void> recordBehavior(UserBehavior behavior) async {
    // 空实现，不执行任何操作
  }

  @override
  Future<void> recordBehaviors(List<UserBehavior> behaviors) async {
    // 空实现，不执行任何操作
  }

  @override
  Future<List<TrackingEvent>> getEvents({
    String? userId,
    String? sessionId,
    EventType? type,
    DateTime? startTime,
    DateTime? endTime,
    int? limit,
    int? offset,
  }) async {
    // 返回空列表
    return [];
  }

  @override
  Future<List<UserBehavior>> getBehaviors({
    String? userId,
    String? sessionId,
    BehaviorType? type,
    DateTime? startTime,
    DateTime? endTime,
    int? limit,
    int? offset,
  }) async {
    // 返回空列表
    return [];
  }

  @override
  Future<Map<String, dynamic>> getEventStats({
    String? userId,
    EventType? type,
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    // 返回空统计数据
    return {
      'total_events': 0,
      'unique_users': 0,
      'sessions': 0,
      'event_types': <String, int>{},
      'time_range': {
        'start': startTime?.toIso8601String(),
        'end': endTime?.toIso8601String(),
      },
    };
  }

  @override
  Future<Map<String, dynamic>> getBehaviorStats({
    String? userId,
    BehaviorType? type,
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    // 返回空统计数据
    return {
      'total_behaviors': 0,
      'unique_users': 0,
      'sessions': 0,
      'behavior_types': <String, int>{},
      'average_duration': 0,
      'time_range': {
        'start': startTime?.toIso8601String(),
        'end': endTime?.toIso8601String(),
      },
    };
  }

  @override
  Future<Map<String, dynamic>> getUserActivity({
    required String userId,
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    // 返回空活跃度数据
    return {
      'user_id': userId,
      'total_sessions': 0,
      'total_events': 0,
      'total_behaviors': 0,
      'session_duration': 0,
      'last_active': null,
      'activity_score': 0,
      'time_range': {
        'start': startTime?.toIso8601String(),
        'end': endTime?.toIso8601String(),
      },
    };
  }

  @override
  Future<Map<String, dynamic>> getPageViewStats({
    String? pagePath,
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    // 返回空页面访问统计
    return {
      'total_page_views': 0,
      'unique_visitors': 0,
      'bounce_rate': 0.0,
      'average_time_on_page': 0,
      'top_pages': <Map<String, dynamic>>[],
      'time_range': {
        'start': startTime?.toIso8601String(),
        'end': endTime?.toIso8601String(),
      },
    };
  }

  @override
  Future<Map<String, dynamic>> getFeatureUsageStats({
    String? featureName,
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    // 返回空功能使用统计
    return {
      'total_usage': 0,
      'unique_users': 0,
      'usage_frequency': 0.0,
      'top_features': <Map<String, dynamic>>[],
      'time_range': {
        'start': startTime?.toIso8601String(),
        'end': endTime?.toIso8601String(),
      },
    };
  }

  @override
  Future<Map<String, dynamic>> getUserRetention({
    DateTime? startTime,
    DateTime? endTime,
    String cohortType = 'daily',
  }) async {
    // 返回空留存数据
    return {
      'cohort_type': cohortType,
      'retention_rates': <String, double>{},
      'cohort_sizes': <String, int>{},
      'time_range': {
        'start': startTime?.toIso8601String(),
        'end': endTime?.toIso8601String(),
      },
    };
  }

  @override
  Future<Map<String, dynamic>> getConversionFunnel({
    required List<String> steps,
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    // 返回空转化漏斗数据
    return {
      'steps': steps,
      'funnel_data': steps.map((step) => {
        'step': step,
        'users': 0,
        'conversion_rate': 0.0,
      }).toList(),
      'overall_conversion_rate': 0.0,
      'time_range': {
        'start': startTime?.toIso8601String(),
        'end': endTime?.toIso8601String(),
      },
    };
  }

  @override
  Future<List<Map<String, dynamic>>> getUserJourney({
    required String userId,
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    // 返回空用户路径
    return [];
  }

  @override
  Future<List<Map<String, dynamic>>> getPopularContent({
    String? contentType,
    DateTime? startTime,
    DateTime? endTime,
    int? limit,
  }) async {
    // 返回空热门内容
    return [];
  }

  @override
  Future<Map<String, dynamic>> getUserSegmentation({
    Map<String, dynamic>? criteria,
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    // 返回空用户细分数据
    return {
      'segments': <Map<String, dynamic>>[],
      'total_users': 0,
      'criteria': criteria,
      'time_range': {
        'start': startTime?.toIso8601String(),
        'end': endTime?.toIso8601String(),
      },
    };
  }

  @override
  Future<Map<String, dynamic>> getRealTimeStats() async {
    // 返回空实时统计
    return {
      'active_users': 0,
      'active_sessions': 0,
      'events_per_minute': 0,
      'top_pages': <Map<String, dynamic>>[],
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  @override
  Future<Map<String, dynamic>> exportAnalyticsData({
    DateTime? startTime,
    DateTime? endTime,
    List<String>? eventTypes,
    List<String>? behaviorTypes,
  }) async {
    // 返回空导出数据
    return {
      'events': <Map<String, dynamic>>[],
      'behaviors': <Map<String, dynamic>>[],
      'export_info': {
        'total_records': 0,
        'export_time': DateTime.now().toIso8601String(),
        'time_range': {
          'start': startTime?.toIso8601String(),
          'end': endTime?.toIso8601String(),
        },
      },
    };
  }

  @override
  Future<void> cleanupExpiredData(DateTime before) async {
    // 空实现，不执行任何操作
  }

  @override
  Future<int> getDataStorageSize() async {
    // 返回0字节
    return 0;
  }

  @override
  Future<void> compressHistoricalData(DateTime before) async {
    // 空实现，不执行任何操作
  }
}
