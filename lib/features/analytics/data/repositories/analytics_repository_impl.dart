import 'package:injectable/injectable.dart';
import '../../domain/entities/tracking_event.dart';
import '../../domain/entities/user_behavior.dart';
import '../../domain/repositories/analytics_repository.dart';
import '../datasources/analytics_local_datasource.dart';
import '../datasources/analytics_remote_datasource.dart';
import '../../../../core/network/network_info.dart';
import '../../../../core/logging/logger.dart';

/// 分析统计仓库实现
/// 
/// **功能依赖**: 需要启用 analytics 模块
/// **配置项**: FEATURE_ANALYTICS
@LazySingleton(as: IAnalyticsRepository)
class AnalyticsRepositoryImpl implements IAnalyticsRepository {
  final IAnalyticsLocalDataSource _localDataSource;
  final IAnalyticsRemoteDataSource _remoteDataSource;
  final INetworkInfo _networkInfo;

  AnalyticsRepositoryImpl(
    this._localDataSource,
    this._remoteDataSource,
    this._networkInfo,
  );

  @override
  Future<void> trackEvent(TrackingEvent event) async {
    try {
      // 本地存储事件
      await _localDataSource.storeEvent(event);
      
      // 如果有网络连接，尝试立即发送
      if (await _networkInfo.isConnected) {
        try {
          await _remoteDataSource.sendEvent(event);
          await _localDataSource.markEventAsSent(event.id);
        } catch (e) {
          // 发送失败，保留在本地队列中
          Logger.w('Failed to send analytics event: $e');
        }
      }
    } catch (e) {
      Logger.e('Failed to track event: $e');
      rethrow;
    }
  }

  @override
  Future<void> trackEvents(List<TrackingEvent> events) async {
    try {
      // 批量本地存储
      await _localDataSource.storeEvents(events);
      
      // 如果有网络连接，尝试批量发送
      if (await _networkInfo.isConnected) {
        try {
          await _remoteDataSource.sendEvents(events);
          await _localDataSource.markEventsAsSent(events.map((e) => e.id).toList());
        } catch (e) {
          Logger.w('Failed to send analytics events: $e');
        }
      }
    } catch (e) {
      Logger.e('Failed to track events: $e');
      rethrow;
    }
  }

  @override
  Future<void> recordBehavior(UserBehavior behavior) async {
    try {
      // 本地存储行为
      await _localDataSource.storeBehavior(behavior);
      
      // 如果有网络连接，尝试立即发送
      if (await _networkInfo.isConnected) {
        try {
          await _remoteDataSource.sendBehavior(behavior);
          await _localDataSource.markBehaviorAsSent(behavior.id);
        } catch (e) {
          Logger.w('Failed to send user behavior: $e');
        }
      }
    } catch (e) {
      Logger.e('Failed to record behavior: $e');
      rethrow;
    }
  }

  @override
  Future<void> recordBehaviors(List<UserBehavior> behaviors) async {
    try {
      // 批量本地存储
      await _localDataSource.storeBehaviors(behaviors);
      
      // 如果有网络连接，尝试批量发送
      if (await _networkInfo.isConnected) {
        try {
          await _remoteDataSource.sendBehaviors(behaviors);
          await _localDataSource.markBehaviorsAsSent(behaviors.map((b) => b.id).toList());
        } catch (e) {
          Logger.w('Failed to send user behaviors: $e');
        }
      }
    } catch (e) {
      Logger.e('Failed to record behaviors: $e');
      rethrow;
    }
  }

  @override
  Future<List<TrackingEvent>> getEvents({
    String? userId,
    String? sessionId,
    EventType? type,
    DateTime? startTime,
    DateTime? endTime,
    int? limit,
    int? offset,
  }) async {
    try {
      // 优先从本地获取
      final localEvents = await _localDataSource.getEvents(
        userId: userId,
        sessionId: sessionId,
        type: type,
        startTime: startTime,
        endTime: endTime,
        limit: limit,
        offset: offset,
      );

      // 如果有网络连接，尝试从远程获取更新
      if (await _networkInfo.isConnected) {
        try {
          final remoteEvents = await _remoteDataSource.getEvents(
            userId: userId,
            sessionId: sessionId,
            type: type,
            startTime: startTime,
            endTime: endTime,
            limit: limit,
            offset: offset,
          );
          
          // 合并本地和远程数据
          return _mergeEvents(localEvents, remoteEvents);
        } catch (e) {
          Logger.w('Failed to fetch remote events: $e');
        }
      }

      return localEvents;
    } catch (e) {
      Logger.e('Failed to get events: $e');
      rethrow;
    }
  }

  @override
  Future<List<UserBehavior>> getBehaviors({
    String? userId,
    String? sessionId,
    BehaviorType? type,
    DateTime? startTime,
    DateTime? endTime,
    int? limit,
    int? offset,
  }) async {
    try {
      // 优先从本地获取
      final localBehaviors = await _localDataSource.getBehaviors(
        userId: userId,
        sessionId: sessionId,
        type: type,
        startTime: startTime,
        endTime: endTime,
        limit: limit,
        offset: offset,
      );

      // 如果有网络连接，尝试从远程获取更新
      if (await _networkInfo.isConnected) {
        try {
          final remoteBehaviors = await _remoteDataSource.getBehaviors(
            userId: userId,
            sessionId: sessionId,
            type: type,
            startTime: startTime,
            endTime: endTime,
            limit: limit,
            offset: offset,
          );
          
          // 合并本地和远程数据
          return _mergeBehaviors(localBehaviors, remoteBehaviors);
        } catch (e) {
          Logger.w('Failed to fetch remote behaviors: $e');
        }
      }

      return localBehaviors;
    } catch (e) {
      Logger.e('Failed to get behaviors: $e');
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>> getEventStats({
    String? userId,
    EventType? type,
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    try {
      // 优先从远程获取统计数据
      if (await _networkInfo.isConnected) {
        try {
          return await _remoteDataSource.getEventStats(
            userId: userId,
            type: type,
            startTime: startTime,
            endTime: endTime,
          );
        } catch (e) {
          Logger.w('Failed to fetch remote event stats: $e');
        }
      }

      // 从本地计算统计数据
      return await _localDataSource.getEventStats(
        userId: userId,
        type: type,
        startTime: startTime,
        endTime: endTime,
      );
    } catch (e) {
      Logger.e('Failed to get event stats: $e');
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>> getBehaviorStats({
    String? userId,
    BehaviorType? type,
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    try {
      // 优先从远程获取统计数据
      if (await _networkInfo.isConnected) {
        try {
          return await _remoteDataSource.getBehaviorStats(
            userId: userId,
            type: type,
            startTime: startTime,
            endTime: endTime,
          );
        } catch (e) {
          Logger.w('Failed to fetch remote behavior stats: $e');
        }
      }

      // 从本地计算统计数据
      return await _localDataSource.getBehaviorStats(
        userId: userId,
        type: type,
        startTime: startTime,
        endTime: endTime,
      );
    } catch (e) {
      Logger.e('Failed to get behavior stats: $e');
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>> getUserActivity({
    required String userId,
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    try {
      if (await _networkInfo.isConnected) {
        try {
          return await _remoteDataSource.getUserActivity(
            userId: userId,
            startTime: startTime,
            endTime: endTime,
          );
        } catch (e) {
          Logger.w('Failed to fetch remote user activity: $e');
        }
      }

      return await _localDataSource.getUserActivity(
        userId: userId,
        startTime: startTime,
        endTime: endTime,
      );
    } catch (e) {
      Logger.e('Failed to get user activity: $e');
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>> getPageViewStats({
    String? pagePath,
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    try {
      if (await _networkInfo.isConnected) {
        try {
          return await _remoteDataSource.getPageViewStats(
            pagePath: pagePath,
            startTime: startTime,
            endTime: endTime,
          );
        } catch (e) {
          Logger.w('Failed to fetch remote page view stats: $e');
        }
      }

      return await _localDataSource.getPageViewStats(
        pagePath: pagePath,
        startTime: startTime,
        endTime: endTime,
      );
    } catch (e) {
      Logger.e('Failed to get page view stats: $e');
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>> getFeatureUsageStats({
    String? featureName,
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    try {
      if (await _networkInfo.isConnected) {
        try {
          return await _remoteDataSource.getFeatureUsageStats(
            featureName: featureName,
            startTime: startTime,
            endTime: endTime,
          );
        } catch (e) {
          Logger.w('Failed to fetch remote feature usage stats: $e');
        }
      }

      return await _localDataSource.getFeatureUsageStats(
        featureName: featureName,
        startTime: startTime,
        endTime: endTime,
      );
    } catch (e) {
      Logger.e('Failed to get feature usage stats: $e');
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>> getUserRetention({
    DateTime? startTime,
    DateTime? endTime,
    String cohortType = 'daily',
  }) async {
    try {
      if (await _networkInfo.isConnected) {
        try {
          return await _remoteDataSource.getUserRetention(
            startTime: startTime,
            endTime: endTime,
            cohortType: cohortType,
          );
        } catch (e) {
          Logger.w('Failed to fetch remote user retention: $e');
        }
      }

      return await _localDataSource.getUserRetention(
        startTime: startTime,
        endTime: endTime,
        cohortType: cohortType,
      );
    } catch (e) {
      Logger.e('Failed to get user retention: $e');
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>> getConversionFunnel({
    required List<String> steps,
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    try {
      if (await _networkInfo.isConnected) {
        try {
          return await _remoteDataSource.getConversionFunnel(
            steps: steps,
            startTime: startTime,
            endTime: endTime,
          );
        } catch (e) {
          Logger.w('Failed to fetch remote conversion funnel: $e');
        }
      }

      return await _localDataSource.getConversionFunnel(
        steps: steps,
        startTime: startTime,
        endTime: endTime,
      );
    } catch (e) {
      Logger.e('Failed to get conversion funnel: $e');
      rethrow;
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getUserJourney({
    required String userId,
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    try {
      if (await _networkInfo.isConnected) {
        try {
          return await _remoteDataSource.getUserJourney(
            userId: userId,
            startTime: startTime,
            endTime: endTime,
          );
        } catch (e) {
          Logger.w('Failed to fetch remote user journey: $e');
        }
      }

      return await _localDataSource.getUserJourney(
        userId: userId,
        startTime: startTime,
        endTime: endTime,
      );
    } catch (e) {
      Logger.e('Failed to get user journey: $e');
      rethrow;
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getPopularContent({
    String? contentType,
    DateTime? startTime,
    DateTime? endTime,
    int? limit,
  }) async {
    try {
      if (await _networkInfo.isConnected) {
        try {
          return await _remoteDataSource.getPopularContent(
            contentType: contentType,
            startTime: startTime,
            endTime: endTime,
            limit: limit,
          );
        } catch (e) {
          Logger.w('Failed to fetch remote popular content: $e');
        }
      }

      return await _localDataSource.getPopularContent(
        contentType: contentType,
        startTime: startTime,
        endTime: endTime,
        limit: limit,
      );
    } catch (e) {
      Logger.e('Failed to get popular content: $e');
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>> getUserSegmentation({
    Map<String, dynamic>? criteria,
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    try {
      if (await _networkInfo.isConnected) {
        try {
          return await _remoteDataSource.getUserSegmentation(
            criteria: criteria,
            startTime: startTime,
            endTime: endTime,
          );
        } catch (e) {
          Logger.w('Failed to fetch remote user segmentation: $e');
        }
      }

      return await _localDataSource.getUserSegmentation(
        criteria: criteria,
        startTime: startTime,
        endTime: endTime,
      );
    } catch (e) {
      Logger.e('Failed to get user segmentation: $e');
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>> getRealTimeStats() async {
    try {
      if (await _networkInfo.isConnected) {
        return await _remoteDataSource.getRealTimeStats();
      }

      return await _localDataSource.getRealTimeStats();
    } catch (e) {
      Logger.e('Failed to get real-time stats: $e');
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>> exportAnalyticsData({
    DateTime? startTime,
    DateTime? endTime,
    List<String>? eventTypes,
    List<String>? behaviorTypes,
  }) async {
    try {
      return await _localDataSource.exportAnalyticsData(
        startTime: startTime,
        endTime: endTime,
        eventTypes: eventTypes,
        behaviorTypes: behaviorTypes,
      );
    } catch (e) {
      Logger.e('Failed to export analytics data: $e');
      rethrow;
    }
  }

  @override
  Future<void> cleanupExpiredData(DateTime before) async {
    try {
      await _localDataSource.cleanupExpiredData(before);
    } catch (e) {
      Logger.e('Failed to cleanup expired data: $e');
      rethrow;
    }
  }

  @override
  Future<int> getDataStorageSize() async {
    try {
      return await _localDataSource.getDataStorageSize();
    } catch (e) {
      Logger.e('Failed to get data storage size: $e');
      rethrow;
    }
  }

  @override
  Future<void> compressHistoricalData(DateTime before) async {
    try {
      await _localDataSource.compressHistoricalData(before);
    } catch (e) {
      Logger.e('Failed to compress historical data: $e');
      rethrow;
    }
  }

  /// 合并本地和远程事件数据
  List<TrackingEvent> _mergeEvents(
    List<TrackingEvent> localEvents,
    List<TrackingEvent> remoteEvents,
  ) {
    final eventMap = <String, TrackingEvent>{};
    
    // 添加本地事件
    for (final event in localEvents) {
      eventMap[event.id] = event;
    }
    
    // 添加远程事件（覆盖本地事件）
    for (final event in remoteEvents) {
      eventMap[event.id] = event;
    }
    
    final mergedEvents = eventMap.values.toList();
    mergedEvents.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    
    return mergedEvents;
  }

  /// 合并本地和远程行为数据
  List<UserBehavior> _mergeBehaviors(
    List<UserBehavior> localBehaviors,
    List<UserBehavior> remoteBehaviors,
  ) {
    final behaviorMap = <String, UserBehavior>{};
    
    // 添加本地行为
    for (final behavior in localBehaviors) {
      behaviorMap[behavior.id] = behavior;
    }
    
    // 添加远程行为（覆盖本地行为）
    for (final behavior in remoteBehaviors) {
      behaviorMap[behavior.id] = behavior;
    }
    
    final mergedBehaviors = behaviorMap.values.toList();
    mergedBehaviors.sort((a, b) => b.startTime.compareTo(a.startTime));
    
    return mergedBehaviors;
  }
}
