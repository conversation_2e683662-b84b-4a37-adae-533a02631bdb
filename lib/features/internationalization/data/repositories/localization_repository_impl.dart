import 'dart:async';
import 'dart:convert';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:injectable/injectable.dart';
import '../../../../core/database/database_service.dart';
import '../../domain/entities/locale_info.dart';
import '../../domain/repositories/localization_repository.dart';

/// 本地化仓库实现
/// 
/// **功能依赖**: 需要启用 internationalization 模块
/// **配置项**: FEATURE_INTERNATIONALIZATION
@Injectable(as: ILocalizationRepository)
class LocalizationRepositoryImpl implements ILocalizationRepository {
  final IDatabaseService _databaseService;
  
  // 流控制器
  final StreamController<Locale> _localeController = StreamController.broadcast();
  
  // 缓存
  Locale? _cachedLocale;
  List<LocaleInfo>? _cachedSupportedLocales;
  final Map<String, Map<String, String>> _localizationCache = {};
  
  // 存储键
  static const String _localeKey = 'current_locale';
  static const String _localizationCacheKey = 'localization_cache';

  LocalizationRepositoryImpl(this._databaseService);

  @override
  Stream<Locale> get localeStream => _localeController.stream;

  @override
  Future<Locale> getCurrentLocale() async {
    if (_cachedLocale != null) {
      return _cachedLocale!;
    }

    try {
      final localeData = await _databaseService.get<Map<String, dynamic>>(_localeKey);
      if (localeData != null) {
        _cachedLocale = Locale(
          localeData['languageCode'] as String,
          localeData['countryCode'] as String?,
        );
      } else {
        _cachedLocale = await getSystemLocale();
      }
    } catch (e) {
      _cachedLocale = await getSystemLocale();
    }

    return _cachedLocale!;
  }

  @override
  Future<void> setCurrentLocale(Locale locale) async {
    try {
      final localeData = {
        'languageCode': locale.languageCode,
        'countryCode': locale.countryCode,
      };
      
      await _databaseService.set(_localeKey, localeData);
      _cachedLocale = locale;
      _localeController.add(locale);
    } catch (e) {
      throw Exception('设置当前语言环境失败: $e');
    }
  }

  @override
  Future<List<LocaleInfo>> getSupportedLocales() async {
    if (_cachedSupportedLocales != null) {
      return _cachedSupportedLocales!;
    }

    try {
      _cachedSupportedLocales = LocalePresets.getSupportedLocales();
      return _cachedSupportedLocales!;
    } catch (e) {
      throw Exception('获取支持的语言环境失败: $e');
    }
  }

  @override
  Future<bool> isLocaleSupported(Locale locale) async {
    try {
      final supportedLocales = await getSupportedLocales();
      return supportedLocales.any((info) => 
        info.locale.languageCode == locale.languageCode &&
        info.locale.countryCode == locale.countryCode
      );
    } catch (e) {
      return false;
    }
  }

  @override
  Future<Locale> getBestMatchingLocale(Locale locale) async {
    try {
      final supportedLocales = await getSupportedLocales();
      
      // 精确匹配
      for (final info in supportedLocales) {
        if (info.locale == locale) {
          return info.locale;
        }
      }
      
      // 语言代码匹配
      for (final info in supportedLocales) {
        if (info.locale.languageCode == locale.languageCode) {
          return info.locale;
        }
      }
      
      // 返回默认语言环境
      return LocalePresets.getDefaultLocale().locale;
    } catch (e) {
      return LocalePresets.getDefaultLocale().locale;
    }
  }

  @override
  Future<Locale> getSystemLocale() async {
    try {
      final systemLocale = ui.window.locale;
      final bestMatch = await getBestMatchingLocale(systemLocale);
      return bestMatch;
    } catch (e) {
      return LocalePresets.getDefaultLocale().locale;
    }
  }

  @override
  Future<void> resetToSystemLocale() async {
    try {
      final systemLocale = await getSystemLocale();
      await setCurrentLocale(systemLocale);
    } catch (e) {
      throw Exception('重置为系统语言环境失败: $e');
    }
  }

  @override
  Future<Map<String, String>> getLocalizedStrings(Locale locale) async {
    final localeKey = '${locale.languageCode}_${locale.countryCode}';
    
    // 检查缓存
    if (_localizationCache.containsKey(localeKey)) {
      return _localizationCache[localeKey]!;
    }

    try {
      // 从本地资源加载
      final jsonString = await rootBundle.loadString(
        'assets/l10n/$localeKey.json',
      );
      final Map<String, dynamic> jsonMap = json.decode(jsonString);
      final Map<String, String> strings = jsonMap.map((key, value) {
        return MapEntry(key, value.toString());
      });

      // 缓存结果
      _localizationCache[localeKey] = strings;
      await cacheLocalizedStrings(locale, strings);

      return strings;
    } catch (e) {
      // 如果加载失败，尝试加载默认语言
      if (locale.languageCode != 'en') {
        return await getLocalizedStrings(const Locale('en', 'US'));
      }
      
      throw Exception('加载本地化字符串失败: $e');
    }
  }

  @override
  Future<String?> getLocalizedString(Locale locale, String key) async {
    try {
      final strings = await getLocalizedStrings(locale);
      return strings[key];
    } catch (e) {
      return null;
    }
  }

  @override
  Future<void> cacheLocalizedStrings(Locale locale, Map<String, String> strings) async {
    try {
      final localeKey = '${locale.languageCode}_${locale.countryCode}';
      final cacheKey = '${_localizationCacheKey}_$localeKey';
      
      await _databaseService.set(cacheKey, json.encode(strings));
      _localizationCache[localeKey] = strings;
    } catch (e) {
      // 缓存失败不影响主要功能
      debugPrint('缓存本地化字符串失败: $e');
    }
  }

  @override
  Future<void> clearLocalizationCache() async {
    try {
      _localizationCache.clear();
      
      // 清除数据库中的缓存
      final supportedLocales = await getSupportedLocales();
      for (final info in supportedLocales) {
        final localeKey = '${info.locale.languageCode}_${info.locale.countryCode}';
        final cacheKey = '${_localizationCacheKey}_$localeKey';
        await _databaseService.delete(cacheKey);
      }
    } catch (e) {
      throw Exception('清除本地化缓存失败: $e');
    }
  }

  @override
  Future<bool> downloadRemoteLocalizations(Locale locale) async {
    try {
      // TODO: 实现远程本地化资源下载
      // 这里应该从远程服务器下载最新的本地化文件
      
      // 模拟下载过程
      await Future.delayed(const Duration(seconds: 2));
      
      // 下载成功后更新缓存
      // final strings = await _downloadFromRemote(locale);
      // await cacheLocalizedStrings(locale, strings);
      
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> checkForLocalizationUpdates(Locale locale) async {
    try {
      // TODO: 实现本地化更新检查
      // 这里应该检查远程服务器是否有更新的本地化文件
      
      // 模拟检查过程
      await Future.delayed(const Duration(seconds: 1));
      
      return false; // 暂时返回无更新
    } catch (e) {
      return false;
    }
  }

  @override
  Future<void> importLocalizationFile(Locale locale, Map<String, String> strings) async {
    try {
      await cacheLocalizedStrings(locale, strings);
      
      // 如果导入的是当前语言环境，触发更新
      final currentLocale = await getCurrentLocale();
      if (currentLocale == locale) {
        _localeController.add(locale);
      }
    } catch (e) {
      throw Exception('导入本地化文件失败: $e');
    }
  }

  @override
  Future<Map<String, String>> exportLocalizationFile(Locale locale) async {
    try {
      return await getLocalizedStrings(locale);
    } catch (e) {
      throw Exception('导出本地化文件失败: $e');
    }
  }

  /// 清理资源
  void dispose() {
    _localeController.close();
  }
}

/// NoOp本地化仓库实现
/// 
/// 当internationalization模块禁用时使用的空实现
/// 所有本地化操作返回默认值，不执行实际本地化逻辑
@Injectable(as: ILocalizationRepository)
@Environment('noop')
class NoOpLocalizationRepository implements ILocalizationRepository {
  const NoOpLocalizationRepository();

  @override
  Future<Locale> getCurrentLocale() async => const Locale('en', 'US');

  @override
  Future<void> setCurrentLocale(Locale locale) async {
    // NoOp: 国际化功能未启用
  }

  @override
  Future<List<LocaleInfo>> getSupportedLocales() async => [LocaleInfo.enUS()];

  @override
  Future<bool> isLocaleSupported(Locale locale) async => locale.languageCode == 'en';

  @override
  Future<Locale> getBestMatchingLocale(Locale locale) async => const Locale('en', 'US');

  @override
  Future<Locale> getSystemLocale() async => const Locale('en', 'US');

  @override
  Future<void> resetToSystemLocale() async {
    // NoOp: 国际化功能未启用
  }

  @override
  Future<Map<String, String>> getLocalizedStrings(Locale locale) async => {};

  @override
  Future<String?> getLocalizedString(Locale locale, String key) async => key;

  @override
  Future<void> cacheLocalizedStrings(Locale locale, Map<String, String> strings) async {
    // NoOp: 国际化功能未启用
  }

  @override
  Future<void> clearLocalizationCache() async {
    // NoOp: 国际化功能未启用
  }

  @override
  Future<bool> downloadRemoteLocalizations(Locale locale) async => false;

  @override
  Future<bool> checkForLocalizationUpdates(Locale locale) async => false;

  @override
  Future<void> importLocalizationFile(Locale locale, Map<String, String> strings) async {
    // NoOp: 国际化功能未启用
  }

  @override
  Future<Map<String, String>> exportLocalizationFile(Locale locale) async => {};

  @override
  Stream<Locale> get localeStream => Stream.value(const Locale('en', 'US'));
}
