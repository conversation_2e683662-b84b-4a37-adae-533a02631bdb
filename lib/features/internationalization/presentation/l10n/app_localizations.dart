import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:intl/intl.dart';

/// 应用本地化类
/// 
/// **功能依赖**: 需要启用 internationalization 模块
/// **配置项**: FEATURE_INTERNATIONALIZATION
class AppLocalizations {
  AppLocalizations(this.locale);

  final Locale locale;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  static const List<Locale> supportedLocales = [
    Locale('en', 'US'), // English
    Locale('zh', 'CN'), // Chinese Simplified
    Locale('zh', 'TW'), // Chinese Traditional
    Locale('ja', 'JP'), // Japanese
    Locale('ko', 'KR'), // Korean
    Locale('ar', 'SA'), // Arabic
    Locale('es', 'ES'), // Spanish
    Locale('fr', 'FR'), // French
    Locale('de', 'DE'), // German
    Locale('ru', 'RU'), // Russian
  ];

  Map<String, String> _localizedStrings = {};

  Future<bool> load() async {
    try {
      // 尝试加载特定语言环境的本地化文件
      final String jsonString = await rootBundle.loadString(
        'assets/l10n/${locale.languageCode}_${locale.countryCode}.json',
      );
      final Map<String, dynamic> jsonMap = json.decode(jsonString);

      _localizedStrings = jsonMap.map((key, value) {
        return MapEntry(key, value.toString());
      });

      return true;
    } catch (e) {
      // 如果加载失败，尝试加载默认语言
      if (locale.languageCode != 'en') {
        try {
          final String jsonString = await rootBundle.loadString(
            'assets/l10n/en_US.json',
          );
          final Map<String, dynamic> jsonMap = json.decode(jsonString);

          _localizedStrings = jsonMap.map((key, value) {
            return MapEntry(key, value.toString());
          });

          return true;
        } catch (e) {
          debugPrint('Failed to load default localization: $e');
        }
      }
      return false;
    }
  }

  String translate(String key, {Map<String, dynamic>? args}) {
    String translation = _localizedStrings[key] ?? key;

    // 处理参数替换
    if (args != null) {
      args.forEach((argKey, argValue) {
        translation = translation.replaceAll('{$argKey}', argValue.toString());
      });
    }

    return translation;
  }

  // ============================================================================
  // 通用文本
  // ============================================================================
  
  String get appName => translate('app_name');
  String get welcome => translate('welcome');
  String get login => translate('login');
  String get logout => translate('logout');
  String get email => translate('email');
  String get password => translate('password');
  String get confirmPassword => translate('confirm_password');
  String get register => translate('register');
  String get forgotPassword => translate('forgot_password');
  String get resetPassword => translate('reset_password');
  String get save => translate('save');
  String get cancel => translate('cancel');
  String get delete => translate('delete');
  String get edit => translate('edit');
  String get add => translate('add');
  String get search => translate('search');
  String get filter => translate('filter');
  String get sort => translate('sort');
  String get refresh => translate('refresh');
  String get loading => translate('loading');
  String get error => translate('error');
  String get success => translate('success');
  String get warning => translate('warning');
  String get info => translate('info');
  String get yes => translate('yes');
  String get no => translate('no');
  String get ok => translate('ok');
  String get close => translate('close');
  String get back => translate('back');
  String get next => translate('next');
  String get previous => translate('previous');
  String get finish => translate('finish');
  String get settings => translate('settings');
  String get profile => translate('profile');
  String get about => translate('about');
  String get help => translate('help');
  String get contact => translate('contact');
  String get privacy => translate('privacy');
  String get terms => translate('terms');

  // ============================================================================
  // 错误消息
  // ============================================================================
  
  String get errorGeneral => translate('error_general');
  String get errorNetwork => translate('error_network');
  String get errorTimeout => translate('error_timeout');
  String get errorUnauthorized => translate('error_unauthorized');
  String get errorForbidden => translate('error_forbidden');
  String get errorNotFound => translate('error_not_found');
  String get errorServerError => translate('error_server_error');
  String get errorValidationFailed => translate('error_validation_failed');

  // ============================================================================
  // 验证消息
  // ============================================================================
  
  String get validationRequired => translate('validation_required');
  String get validationEmailInvalid => translate('validation_email_invalid');
  String get validationPasswordTooShort => translate('validation_password_too_short');
  String get validationPasswordMismatch => translate('validation_password_mismatch');
  String get validationPhoneInvalid => translate('validation_phone_invalid');

  // ============================================================================
  // 主题相关
  // ============================================================================
  
  String get theme => translate('theme');
  String get lightTheme => translate('light_theme');
  String get darkTheme => translate('dark_theme');
  String get systemTheme => translate('system_theme');
  String get customTheme => translate('custom_theme');
  String get createTheme => translate('create_theme');
  String get editTheme => translate('edit_theme');
  String get deleteTheme => translate('delete_theme');
  String get themePreview => translate('theme_preview');

  // ============================================================================
  // 语言相关
  // ============================================================================
  
  String get language => translate('language');
  String get selectLanguage => translate('select_language');
  String get currentLanguage => translate('current_language');
  String get languageChanged => translate('language_changed');

  // ============================================================================
  // 格式化方法
  // ============================================================================
  
  String formatDate(DateTime date) {
    return DateFormat.yMMMd(locale.toString()).format(date);
  }

  String formatTime(DateTime time) {
    return DateFormat.Hm(locale.toString()).format(time);
  }

  String formatDateTime(DateTime dateTime) {
    return DateFormat.yMMMd(locale.toString()).add_Hm().format(dateTime);
  }

  String formatCurrency(double amount, {String? currencyCode}) {
    final format = NumberFormat.currency(
      locale: locale.toString(),
      symbol: currencyCode ?? getCurrencySymbol(),
    );
    return format.format(amount);
  }

  String formatNumber(num number) {
    return NumberFormat('#,##0', locale.toString()).format(number);
  }

  String formatPercent(double value) {
    return NumberFormat.percentPattern(locale.toString()).format(value);
  }

  String getCurrencySymbol() {
    switch (locale.countryCode) {
      case 'US':
        return '\$';
      case 'CN':
      case 'TW':
        return '¥';
      case 'JP':
        return '¥';
      case 'KR':
        return '₩';
      case 'ES':
      case 'FR':
      case 'DE':
        return '€';
      case 'SA':
        return 'ر.س';
      case 'RU':
        return '₽';
      default:
        return '\$';
    }
  }

  // ============================================================================
  // 复数形式处理
  // ============================================================================
  
  String plural(String key, int count, {Map<String, dynamic>? args}) {
    String pluralKey;
    
    if (count == 0) {
      pluralKey = '${key}_zero';
    } else if (count == 1) {
      pluralKey = '${key}_one';
    } else {
      pluralKey = '${key}_other';
    }

    final translation = _localizedStrings[pluralKey] ?? _localizedStrings[key] ?? key;
    
    String result = translation.replaceAll('{count}', count.toString());
    
    if (args != null) {
      args.forEach((argKey, argValue) {
        result = result.replaceAll('{$argKey}', argValue.toString());
      });
    }

    return result;
  }

  // ============================================================================
  // RTL 支持
  // ============================================================================
  
  bool get isRTL {
    return ['ar', 'he', 'fa', 'ur'].contains(locale.languageCode);
  }

  TextDirection get textDirection {
    return isRTL ? TextDirection.rtl : TextDirection.ltr;
  }
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return AppLocalizations.supportedLocales
        .any((supportedLocale) => supportedLocale.languageCode == locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    final AppLocalizations localizations = AppLocalizations(locale);
    await localizations.load();
    return localizations;
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}
