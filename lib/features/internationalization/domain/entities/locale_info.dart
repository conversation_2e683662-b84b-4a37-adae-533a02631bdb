import 'package:flutter/material.dart';
import 'package:equatable/equatable.dart';

/// 语言环境信息实体
/// 
/// **功能依赖**: 需要启用 internationalization 模块
/// **配置项**: FEATURE_INTERNATIONALIZATION
class LocaleInfo extends Equatable {
  /// 语言环境
  final Locale locale;
  
  /// 英文名称
  final String name;
  
  /// 本地化名称
  final String nativeName;
  
  /// 国旗表情符号
  final String flag;
  
  /// 是否为RTL语言
  final bool isRTL;
  
  /// 是否已启用
  final bool isEnabled;
  
  /// 完成度（0.0 - 1.0）
  final double completeness;
  
  /// 最后更新时间
  final DateTime? lastUpdated;

  const LocaleInfo({
    required this.locale,
    required this.name,
    required this.nativeName,
    required this.flag,
    this.isRTL = false,
    this.isEnabled = true,
    this.completeness = 1.0,
    this.lastUpdated,
  });

  /// 创建中文简体语言信息
  factory LocaleInfo.zhCN() {
    return LocaleInfo(
      locale: const Locale('zh', 'CN'),
      name: 'Chinese (Simplified)',
      nativeName: '简体中文',
      flag: '🇨🇳',
      isRTL: false,
      isEnabled: true,
      completeness: 1.0,
      lastUpdated: DateTime.now(),
    );
  }

  /// 创建英文语言信息
  factory LocaleInfo.enUS() {
    return LocaleInfo(
      locale: const Locale('en', 'US'),
      name: 'English (US)',
      nativeName: 'English',
      flag: '🇺🇸',
      isRTL: false,
      isEnabled: true,
      completeness: 1.0,
      lastUpdated: DateTime.now(),
    );
  }

  /// 创建日文语言信息
  factory LocaleInfo.jaJP() {
    return LocaleInfo(
      locale: const Locale('ja', 'JP'),
      name: 'Japanese',
      nativeName: '日本語',
      flag: '🇯🇵',
      isRTL: false,
      isEnabled: true,
      completeness: 0.8,
      lastUpdated: DateTime.now(),
    );
  }

  /// 创建韩文语言信息
  factory LocaleInfo.koKR() {
    return LocaleInfo(
      locale: const Locale('ko', 'KR'),
      name: 'Korean',
      nativeName: '한국어',
      flag: '🇰🇷',
      isRTL: false,
      isEnabled: true,
      completeness: 0.7,
      lastUpdated: DateTime.now(),
    );
  }

  /// 创建阿拉伯语语言信息
  factory LocaleInfo.arSA() {
    return LocaleInfo(
      locale: const Locale('ar', 'SA'),
      name: 'Arabic',
      nativeName: 'العربية',
      flag: '🇸🇦',
      isRTL: true,
      isEnabled: true,
      completeness: 0.6,
      lastUpdated: DateTime.now(),
    );
  }

  /// 获取语言代码
  String get languageCode => locale.languageCode;
  
  /// 获取国家代码
  String? get countryCode => locale.countryCode;
  
  /// 获取完整的语言环境代码
  String get localeCode => '${locale.languageCode}_${locale.countryCode}';
  
  /// 获取文本方向
  TextDirection get textDirection => isRTL ? TextDirection.rtl : TextDirection.ltr;
  
  /// 是否完全翻译
  bool get isFullyTranslated => completeness >= 1.0;
  
  /// 获取完成度百分比
  int get completenessPercentage => (completeness * 100).round();

  /// 复制并修改语言信息
  LocaleInfo copyWith({
    Locale? locale,
    String? name,
    String? nativeName,
    String? flag,
    bool? isRTL,
    bool? isEnabled,
    double? completeness,
    DateTime? lastUpdated,
  }) {
    return LocaleInfo(
      locale: locale ?? this.locale,
      name: name ?? this.name,
      nativeName: nativeName ?? this.nativeName,
      flag: flag ?? this.flag,
      isRTL: isRTL ?? this.isRTL,
      isEnabled: isEnabled ?? this.isEnabled,
      completeness: completeness ?? this.completeness,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  /// 转换为 JSON
  Map<String, dynamic> toJson() {
    return {
      'languageCode': locale.languageCode,
      'countryCode': locale.countryCode,
      'name': name,
      'nativeName': nativeName,
      'flag': flag,
      'isRTL': isRTL,
      'isEnabled': isEnabled,
      'completeness': completeness,
      'lastUpdated': lastUpdated?.toIso8601String(),
    };
  }

  /// 从 JSON 创建
  factory LocaleInfo.fromJson(Map<String, dynamic> json) {
    return LocaleInfo(
      locale: Locale(
        json['languageCode'] as String,
        json['countryCode'] as String?,
      ),
      name: json['name'] as String,
      nativeName: json['nativeName'] as String,
      flag: json['flag'] as String,
      isRTL: json['isRTL'] as bool? ?? false,
      isEnabled: json['isEnabled'] as bool? ?? true,
      completeness: (json['completeness'] as num?)?.toDouble() ?? 1.0,
      lastUpdated: json['lastUpdated'] != null 
          ? DateTime.parse(json['lastUpdated'] as String)
          : null,
    );
  }

  @override
  List<Object?> get props => [
        locale,
        name,
        nativeName,
        flag,
        isRTL,
        isEnabled,
        completeness,
        lastUpdated,
      ];

  @override
  String toString() {
    return 'LocaleInfo(locale: $locale, name: $name, nativeName: $nativeName)';
  }
}

/// 语言环境预设
class LocalePresets {
  LocalePresets._();

  /// 获取所有支持的语言环境
  static List<LocaleInfo> getSupportedLocales() {
    return [
      LocaleInfo.enUS(),
      LocaleInfo.zhCN(),
      LocaleInfo.jaJP(),
      LocaleInfo.koKR(),
      LocaleInfo.arSA(),
      _createZhTW(),
      _createEsES(),
      _createFrFR(),
      _createDeDE(),
      _createRuRU(),
    ];
  }

  /// 获取默认语言环境
  static LocaleInfo getDefaultLocale() {
    return LocaleInfo.enUS();
  }

  /// 根据语言环境获取语言信息
  static LocaleInfo? getLocaleInfo(Locale locale) {
    return getSupportedLocales().firstWhere(
      (info) => info.locale == locale,
      orElse: () => getDefaultLocale(),
    );
  }

  /// 检查是否为RTL语言
  static bool isRTLLanguage(String languageCode) {
    const rtlLanguages = ['ar', 'he', 'fa', 'ur'];
    return rtlLanguages.contains(languageCode);
  }

  static LocaleInfo _createZhTW() {
    return LocaleInfo(
      locale: const Locale('zh', 'TW'),
      name: 'Chinese (Traditional)',
      nativeName: '繁體中文',
      flag: '🇹🇼',
      isRTL: false,
      isEnabled: true,
      completeness: 0.9,
      lastUpdated: DateTime.now(),
    );
  }

  static LocaleInfo _createEsES() {
    return LocaleInfo(
      locale: const Locale('es', 'ES'),
      name: 'Spanish',
      nativeName: 'Español',
      flag: '🇪🇸',
      isRTL: false,
      isEnabled: true,
      completeness: 0.8,
      lastUpdated: DateTime.now(),
    );
  }

  static LocaleInfo _createFrFR() {
    return LocaleInfo(
      locale: const Locale('fr', 'FR'),
      name: 'French',
      nativeName: 'Français',
      flag: '🇫🇷',
      isRTL: false,
      isEnabled: true,
      completeness: 0.7,
      lastUpdated: DateTime.now(),
    );
  }

  static LocaleInfo _createDeDE() {
    return LocaleInfo(
      locale: const Locale('de', 'DE'),
      name: 'German',
      nativeName: 'Deutsch',
      flag: '🇩🇪',
      isRTL: false,
      isEnabled: true,
      completeness: 0.6,
      lastUpdated: DateTime.now(),
    );
  }

  static LocaleInfo _createRuRU() {
    return LocaleInfo(
      locale: const Locale('ru', 'RU'),
      name: 'Russian',
      nativeName: 'Русский',
      flag: '🇷🇺',
      isRTL: false,
      isEnabled: true,
      completeness: 0.5,
      lastUpdated: DateTime.now(),
    );
  }
}
