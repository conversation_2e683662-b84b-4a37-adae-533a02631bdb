import 'package:flutter/material.dart';
import '../entities/locale_info.dart';

/// 本地化仓库接口
/// 
/// **功能依赖**: 需要启用 internationalization 模块
/// **配置项**: FEATURE_INTERNATIONALIZATION
abstract class ILocalizationRepository {
  /// 获取当前语言环境
  Future<Locale> getCurrentLocale();
  
  /// 设置当前语言环境
  Future<void> setCurrentLocale(Locale locale);
  
  /// 获取支持的语言环境列表
  Future<List<LocaleInfo>> getSupportedLocales();
  
  /// 检查语言环境是否支持
  Future<bool> isLocaleSupported(Locale locale);
  
  /// 获取最佳匹配的语言环境
  Future<Locale> getBestMatchingLocale(Locale locale);
  
  /// 获取系统语言环境
  Future<Locale> getSystemLocale();
  
  /// 重置为系统语言环境
  Future<void> resetToSystemLocale();
  
  /// 获取本地化字符串
  Future<Map<String, String>> getLocalizedStrings(Locale locale);
  
  /// 获取特定键的本地化字符串
  Future<String?> getLocalizedString(Locale locale, String key);
  
  /// 缓存本地化字符串
  Future<void> cacheLocalizedStrings(Locale locale, Map<String, String> strings);
  
  /// 清除本地化缓存
  Future<void> clearLocalizationCache();
  
  /// 下载远程本地化资源
  Future<bool> downloadRemoteLocalizations(Locale locale);
  
  /// 检查本地化更新
  Future<bool> checkForLocalizationUpdates(Locale locale);
  
  /// 导入本地化文件
  Future<void> importLocalizationFile(Locale locale, Map<String, String> strings);
  
  /// 导出本地化文件
  Future<Map<String, String>> exportLocalizationFile(Locale locale);
  
  /// 获取语言环境变更流
  Stream<Locale> get localeStream;
}
