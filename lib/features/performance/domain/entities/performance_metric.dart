import 'package:equatable/equatable.dart';

/// 性能指标类型
enum PerformanceMetricType {
  /// 应用启动时间
  appStartup,
  
  /// 页面加载时间
  pageLoad,
  
  /// 网络请求时间
  networkRequest,
  
  /// 数据库操作时间
  databaseOperation,
  
  /// 内存使用量
  memoryUsage,
  
  /// CPU使用率
  cpuUsage,
  
  /// 帧率
  frameRate,
  
  /// 电池使用量
  batteryUsage,
  
  /// 自定义指标
  custom,
}

/// 性能指标严重程度
enum PerformanceSeverity {
  /// 正常
  normal,
  
  /// 警告
  warning,
  
  /// 严重
  critical,
}

/// 性能指标实体
/// 
/// **功能依赖**: 需要启用 performance 模块
/// **配置项**: FEATURE_PERFORMANCE
class PerformanceMetric extends Equatable {
  /// 指标ID
  final String id;
  
  /// 指标类型
  final PerformanceMetricType type;
  
  /// 指标名称
  final String name;
  
  /// 指标值
  final double value;
  
  /// 指标单位
  final String unit;
  
  /// 时间戳
  final DateTime timestamp;
  
  /// 持续时间（毫秒）
  final int? duration;
  
  /// 严重程度
  final PerformanceSeverity severity;
  
  /// 标签
  final Map<String, String>? tags;
  
  /// 上下文信息
  final Map<String, dynamic>? context;
  
  /// 设备信息
  final String? deviceInfo;
  
  /// 应用版本
  final String? appVersion;
  
  /// 用户ID
  final String? userId;
  
  /// 会话ID
  final String? sessionId;

  const PerformanceMetric({
    required this.id,
    required this.type,
    required this.name,
    required this.value,
    required this.unit,
    required this.timestamp,
    this.duration,
    this.severity = PerformanceSeverity.normal,
    this.tags,
    this.context,
    this.deviceInfo,
    this.appVersion,
    this.userId,
    this.sessionId,
  });

  /// 创建应用启动指标
  factory PerformanceMetric.appStartup({
    required String id,
    required double startupTime,
    Map<String, String>? tags,
    Map<String, dynamic>? context,
  }) {
    return PerformanceMetric(
      id: id,
      type: PerformanceMetricType.appStartup,
      name: 'App Startup Time',
      value: startupTime,
      unit: 'ms',
      timestamp: DateTime.now(),
      severity: startupTime > 3000 
          ? PerformanceSeverity.critical
          : startupTime > 1500 
              ? PerformanceSeverity.warning 
              : PerformanceSeverity.normal,
      tags: tags,
      context: context,
    );
  }

  /// 创建页面加载指标
  factory PerformanceMetric.pageLoad({
    required String id,
    required String pageName,
    required double loadTime,
    Map<String, String>? tags,
    Map<String, dynamic>? context,
  }) {
    return PerformanceMetric(
      id: id,
      type: PerformanceMetricType.pageLoad,
      name: 'Page Load Time: $pageName',
      value: loadTime,
      unit: 'ms',
      timestamp: DateTime.now(),
      severity: loadTime > 2000 
          ? PerformanceSeverity.critical
          : loadTime > 1000 
              ? PerformanceSeverity.warning 
              : PerformanceSeverity.normal,
      tags: {'page': pageName, ...?tags},
      context: context,
    );
  }

  /// 创建网络请求指标
  factory PerformanceMetric.networkRequest({
    required String id,
    required String url,
    required double responseTime,
    int? statusCode,
    Map<String, String>? tags,
    Map<String, dynamic>? context,
  }) {
    return PerformanceMetric(
      id: id,
      type: PerformanceMetricType.networkRequest,
      name: 'Network Request: $url',
      value: responseTime,
      unit: 'ms',
      timestamp: DateTime.now(),
      severity: responseTime > 5000 
          ? PerformanceSeverity.critical
          : responseTime > 2000 
              ? PerformanceSeverity.warning 
              : PerformanceSeverity.normal,
      tags: {
        'url': url,
        if (statusCode != null) 'status_code': statusCode.toString(),
        ...?tags,
      },
      context: context,
    );
  }

  /// 创建内存使用指标
  factory PerformanceMetric.memoryUsage({
    required String id,
    required double memoryUsage,
    Map<String, String>? tags,
    Map<String, dynamic>? context,
  }) {
    return PerformanceMetric(
      id: id,
      type: PerformanceMetricType.memoryUsage,
      name: 'Memory Usage',
      value: memoryUsage,
      unit: 'MB',
      timestamp: DateTime.now(),
      severity: memoryUsage > 500 
          ? PerformanceSeverity.critical
          : memoryUsage > 200 
              ? PerformanceSeverity.warning 
              : PerformanceSeverity.normal,
      tags: tags,
      context: context,
    );
  }

  /// 创建帧率指标
  factory PerformanceMetric.frameRate({
    required String id,
    required double fps,
    Map<String, String>? tags,
    Map<String, dynamic>? context,
  }) {
    return PerformanceMetric(
      id: id,
      type: PerformanceMetricType.frameRate,
      name: 'Frame Rate',
      value: fps,
      unit: 'fps',
      timestamp: DateTime.now(),
      severity: fps < 30 
          ? PerformanceSeverity.critical
          : fps < 45 
              ? PerformanceSeverity.warning 
              : PerformanceSeverity.normal,
      tags: tags,
      context: context,
    );
  }

  /// 是否为关键指标
  bool get isCritical => severity == PerformanceSeverity.critical;

  /// 是否为警告指标
  bool get isWarning => severity == PerformanceSeverity.warning;

  /// 是否为正常指标
  bool get isNormal => severity == PerformanceSeverity.normal;

  /// 获取格式化的值
  String get formattedValue {
    if (value == value.toInt()) {
      return '${value.toInt()} $unit';
    } else {
      return '${value.toStringAsFixed(2)} $unit';
    }
  }

  /// 复制并修改指标
  PerformanceMetric copyWith({
    String? id,
    PerformanceMetricType? type,
    String? name,
    double? value,
    String? unit,
    DateTime? timestamp,
    int? duration,
    PerformanceSeverity? severity,
    Map<String, String>? tags,
    Map<String, dynamic>? context,
    String? deviceInfo,
    String? appVersion,
    String? userId,
    String? sessionId,
  }) {
    return PerformanceMetric(
      id: id ?? this.id,
      type: type ?? this.type,
      name: name ?? this.name,
      value: value ?? this.value,
      unit: unit ?? this.unit,
      timestamp: timestamp ?? this.timestamp,
      duration: duration ?? this.duration,
      severity: severity ?? this.severity,
      tags: tags ?? this.tags,
      context: context ?? this.context,
      deviceInfo: deviceInfo ?? this.deviceInfo,
      appVersion: appVersion ?? this.appVersion,
      userId: userId ?? this.userId,
      sessionId: sessionId ?? this.sessionId,
    );
  }

  /// 转换为 JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'name': name,
      'value': value,
      'unit': unit,
      'timestamp': timestamp.toIso8601String(),
      'duration': duration,
      'severity': severity.name,
      'tags': tags,
      'context': context,
      'deviceInfo': deviceInfo,
      'appVersion': appVersion,
      'userId': userId,
      'sessionId': sessionId,
    };
  }

  /// 从 JSON 创建
  factory PerformanceMetric.fromJson(Map<String, dynamic> json) {
    return PerformanceMetric(
      id: json['id'] as String,
      type: PerformanceMetricType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => PerformanceMetricType.custom,
      ),
      name: json['name'] as String,
      value: (json['value'] as num).toDouble(),
      unit: json['unit'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      duration: json['duration'] as int?,
      severity: PerformanceSeverity.values.firstWhere(
        (e) => e.name == json['severity'],
        orElse: () => PerformanceSeverity.normal,
      ),
      tags: json['tags'] != null 
          ? Map<String, String>.from(json['tags'] as Map)
          : null,
      context: json['context'] as Map<String, dynamic>?,
      deviceInfo: json['deviceInfo'] as String?,
      appVersion: json['appVersion'] as String?,
      userId: json['userId'] as String?,
      sessionId: json['sessionId'] as String?,
    );
  }

  @override
  List<Object?> get props => [
        id,
        type,
        name,
        value,
        unit,
        timestamp,
        duration,
        severity,
        tags,
        context,
        deviceInfo,
        appVersion,
        userId,
        sessionId,
      ];

  @override
  String toString() {
    return 'PerformanceMetric(id: $id, type: $type, name: $name, value: $formattedValue, severity: $severity)';
  }
}
