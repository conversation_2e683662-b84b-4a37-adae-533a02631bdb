import '../entities/performance_metric.dart';

/// 性能监控仓库接口
/// 
/// **功能依赖**: 需要启用 performance 模块
/// **配置项**: FEATURE_PERFORMANCE
abstract class IPerformanceRepository {
  /// 记录性能指标
  Future<void> recordMetric(PerformanceMetric metric);
  
  /// 批量记录性能指标
  Future<void> recordMetrics(List<PerformanceMetric> metrics);
  
  /// 获取性能指标
  Future<List<PerformanceMetric>> getMetrics({
    PerformanceMetricType? type,
    DateTime? startTime,
    DateTime? endTime,
    int? limit,
    int? offset,
  });
  
  /// 获取指标统计信息
  Future<Map<String, dynamic>> getMetricStats({
    PerformanceMetricType? type,
    DateTime? startTime,
    DateTime? endTime,
  });
  
  /// 获取性能趋势
  Future<List<Map<String, dynamic>>> getPerformanceTrend({
    required PerformanceMetricType type,
    required DateTime startTime,
    required DateTime endTime,
    String interval = 'hour', // hour, day, week
  });
  
  /// 获取性能警告
  Future<List<PerformanceMetric>> getPerformanceWarnings({
    DateTime? startTime,
    DateTime? endTime,
  });
  
  /// 清理过期指标
  Future<void> cleanupExpiredMetrics(DateTime before);
  
  /// 导出性能数据
  Future<Map<String, dynamic>> exportPerformanceData({
    DateTime? startTime,
    DateTime? endTime,
  });
  
  /// 获取设备性能信息
  Future<Map<String, dynamic>> getDevicePerformanceInfo();
  
  /// 开始性能监控会话
  Future<String> startPerformanceSession();
  
  /// 结束性能监控会话
  Future<void> endPerformanceSession(String sessionId);
  
  /// 获取当前会话ID
  String? getCurrentSessionId();
}
