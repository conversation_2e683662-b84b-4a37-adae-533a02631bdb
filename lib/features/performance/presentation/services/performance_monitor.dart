import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';
import 'package:injectable/injectable.dart';
import 'package:uuid/uuid.dart';
import '../../domain/entities/performance_metric.dart';
import '../../domain/repositories/performance_repository.dart';

/// 性能监控服务
/// 
/// **功能依赖**: 需要启用 performance 模块
/// **配置项**: FEATURE_PERFORMANCE
@injectable
class PerformanceMonitor {
  final IPerformanceRepository _performanceRepository;
  final Uuid _uuid = const Uuid();
  
  // 监控状态
  bool _isMonitoring = false;
  String? _currentSessionId;
  
  // 计时器
  Timer? _memoryMonitorTimer;
  Timer? _frameRateMonitorTimer;
  
  // 帧率监控
  int _frameCount = 0;
  DateTime? _lastFrameTime;
  final List<double> _frameRates = [];
  
  // 内存监控
  final List<double> _memoryUsages = [];
  
  // 启动时间记录
  DateTime? _appStartTime;
  
  // 页面加载时间记录
  final Map<String, DateTime> _pageLoadStartTimes = {};

  PerformanceMonitor(this._performanceRepository);

  /// 开始性能监控
  Future<void> startMonitoring() async {
    if (_isMonitoring) return;
    
    try {
      _isMonitoring = true;
      _currentSessionId = await _performanceRepository.startPerformanceSession();
      
      // 开始各种监控
      _startMemoryMonitoring();
      _startFrameRateMonitoring();
      
      debugPrint('性能监控已启动，会话ID: $_currentSessionId');
    } catch (e) {
      debugPrint('启动性能监控失败: $e');
    }
  }

  /// 停止性能监控
  Future<void> stopMonitoring() async {
    if (!_isMonitoring) return;
    
    try {
      _isMonitoring = false;
      
      // 停止计时器
      _memoryMonitorTimer?.cancel();
      _frameRateMonitorTimer?.cancel();
      
      // 结束会话
      if (_currentSessionId != null) {
        await _performanceRepository.endPerformanceSession(_currentSessionId!);
      }
      
      debugPrint('性能监控已停止');
    } catch (e) {
      debugPrint('停止性能监控失败: $e');
    }
  }

  /// 记录应用启动时间
  Future<void> recordAppStartup() async {
    if (_appStartTime == null) return;
    
    try {
      final startupTime = DateTime.now().difference(_appStartTime!).inMilliseconds.toDouble();
      
      final metric = PerformanceMetric.appStartup(
        id: _uuid.v4(),
        startupTime: startupTime,
        tags: {
          'platform': Platform.operatingSystem,
          'debug_mode': kDebugMode.toString(),
        },
      );
      
      await _performanceRepository.recordMetric(metric);
    } catch (e) {
      debugPrint('记录应用启动时间失败: $e');
    }
  }

  /// 开始页面加载计时
  void startPageLoad(String pageName) {
    _pageLoadStartTimes[pageName] = DateTime.now();
  }

  /// 结束页面加载计时
  Future<void> endPageLoad(String pageName) async {
    final startTime = _pageLoadStartTimes[pageName];
    if (startTime == null) return;
    
    try {
      final loadTime = DateTime.now().difference(startTime).inMilliseconds.toDouble();
      
      final metric = PerformanceMetric.pageLoad(
        id: _uuid.v4(),
        pageName: pageName,
        loadTime: loadTime,
        tags: {
          'session_id': _currentSessionId ?? '',
        },
      );
      
      await _performanceRepository.recordMetric(metric);
      _pageLoadStartTimes.remove(pageName);
    } catch (e) {
      debugPrint('记录页面加载时间失败: $e');
    }
  }

  /// 记录网络请求性能
  Future<void> recordNetworkRequest({
    required String url,
    required Duration responseTime,
    int? statusCode,
    Map<String, String>? additionalTags,
  }) async {
    try {
      final metric = PerformanceMetric.networkRequest(
        id: _uuid.v4(),
        url: url,
        responseTime: responseTime.inMilliseconds.toDouble(),
        statusCode: statusCode,
        tags: {
          'session_id': _currentSessionId ?? '',
          ...?additionalTags,
        },
      );
      
      await _performanceRepository.recordMetric(metric);
    } catch (e) {
      debugPrint('记录网络请求性能失败: $e');
    }
  }

  /// 记录数据库操作性能
  Future<void> recordDatabaseOperation({
    required String operation,
    required Duration duration,
    Map<String, String>? additionalTags,
  }) async {
    try {
      final metric = PerformanceMetric(
        id: _uuid.v4(),
        type: PerformanceMetricType.databaseOperation,
        name: 'Database Operation: $operation',
        value: duration.inMilliseconds.toDouble(),
        unit: 'ms',
        timestamp: DateTime.now(),
        severity: duration.inMilliseconds > 1000 
            ? PerformanceSeverity.critical
            : duration.inMilliseconds > 500 
                ? PerformanceSeverity.warning 
                : PerformanceSeverity.normal,
        tags: {
          'operation': operation,
          'session_id': _currentSessionId ?? '',
          ...?additionalTags,
        },
      );
      
      await _performanceRepository.recordMetric(metric);
    } catch (e) {
      debugPrint('记录数据库操作性能失败: $e');
    }
  }

  /// 记录自定义性能指标
  Future<void> recordCustomMetric({
    required String name,
    required double value,
    required String unit,
    PerformanceSeverity severity = PerformanceSeverity.normal,
    Map<String, String>? tags,
    Map<String, dynamic>? context,
  }) async {
    try {
      final metric = PerformanceMetric(
        id: _uuid.v4(),
        type: PerformanceMetricType.custom,
        name: name,
        value: value,
        unit: unit,
        timestamp: DateTime.now(),
        severity: severity,
        tags: {
          'session_id': _currentSessionId ?? '',
          ...?tags,
        },
        context: context,
      );
      
      await _performanceRepository.recordMetric(metric);
    } catch (e) {
      debugPrint('记录自定义性能指标失败: $e');
    }
  }

  /// 获取性能统计
  Future<Map<String, dynamic>> getPerformanceStats({
    PerformanceMetricType? type,
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    try {
      return await _performanceRepository.getMetricStats(
        type: type,
        startTime: startTime,
        endTime: endTime,
      );
    } catch (e) {
      debugPrint('获取性能统计失败: $e');
      return {};
    }
  }

  /// 获取性能警告
  Future<List<PerformanceMetric>> getPerformanceWarnings({
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    try {
      return await _performanceRepository.getPerformanceWarnings(
        startTime: startTime,
        endTime: endTime,
      );
    } catch (e) {
      debugPrint('获取性能警告失败: $e');
      return [];
    }
  }

  /// 清理过期数据
  Future<void> cleanupExpiredData({Duration? retentionPeriod}) async {
    try {
      final cutoffTime = DateTime.now().subtract(
        retentionPeriod ?? const Duration(days: 30)
      );
      
      await _performanceRepository.cleanupExpiredMetrics(cutoffTime);
    } catch (e) {
      debugPrint('清理过期数据失败: $e');
    }
  }

  /// 导出性能数据
  Future<Map<String, dynamic>> exportPerformanceData({
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    try {
      return await _performanceRepository.exportPerformanceData(
        startTime: startTime,
        endTime: endTime,
      );
    } catch (e) {
      debugPrint('导出性能数据失败: $e');
      return {};
    }
  }

  /// 设置应用启动时间
  void setAppStartTime(DateTime startTime) {
    _appStartTime = startTime;
  }

  /// 开始内存监控
  void _startMemoryMonitoring() {
    _memoryMonitorTimer = Timer.periodic(
      const Duration(seconds: 30),
      (_) => _recordMemoryUsage(),
    );
  }

  /// 开始帧率监控
  void _startFrameRateMonitoring() {
    SchedulerBinding.instance.addPersistentFrameCallback(_onFrame);
    
    _frameRateMonitorTimer = Timer.periodic(
      const Duration(seconds: 5),
      (_) => _recordFrameRate(),
    );
  }

  /// 帧回调
  void _onFrame(Duration timestamp) {
    if (!_isMonitoring) return;
    
    _frameCount++;
    
    final now = DateTime.now();
    if (_lastFrameTime != null) {
      final frameDuration = now.difference(_lastFrameTime!);
      if (frameDuration.inMilliseconds > 0) {
        final fps = 1000 / frameDuration.inMilliseconds;
        _frameRates.add(fps);
        
        // 限制帧率记录数量
        if (_frameRates.length > 300) { // 5分钟的数据
          _frameRates.removeAt(0);
        }
      }
    }
    
    _lastFrameTime = now;
  }

  /// 记录内存使用情况
  Future<void> _recordMemoryUsage() async {
    try {
      // TODO: 实现实际的内存使用量获取
      // 这里使用模拟数据
      final memoryUsage = 100.0 + (_memoryUsages.length * 5.0) % 200.0;
      
      final metric = PerformanceMetric.memoryUsage(
        id: _uuid.v4(),
        memoryUsage: memoryUsage,
        tags: {
          'session_id': _currentSessionId ?? '',
        },
      );
      
      await _performanceRepository.recordMetric(metric);
      
      _memoryUsages.add(memoryUsage);
      if (_memoryUsages.length > 120) { // 保留1小时的数据
        _memoryUsages.removeAt(0);
      }
    } catch (e) {
      debugPrint('记录内存使用失败: $e');
    }
  }

  /// 记录帧率
  Future<void> _recordFrameRate() async {
    if (_frameRates.isEmpty) return;
    
    try {
      final averageFps = _frameRates.reduce((a, b) => a + b) / _frameRates.length;
      
      final metric = PerformanceMetric.frameRate(
        id: _uuid.v4(),
        fps: averageFps,
        tags: {
          'session_id': _currentSessionId ?? '',
        },
      );
      
      await _performanceRepository.recordMetric(metric);
      _frameRates.clear();
    } catch (e) {
      debugPrint('记录帧率失败: $e');
    }
  }

  /// 是否正在监控
  bool get isMonitoring => _isMonitoring;

  /// 当前会话ID
  String? get currentSessionId => _currentSessionId;

  /// 清理资源
  void dispose() {
    stopMonitoring();
  }
}
