import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import 'package:uuid/uuid.dart';
import '../../../../core/database/database_service.dart';
import '../../domain/entities/performance_metric.dart';
import '../../domain/repositories/performance_repository.dart';

/// 性能监控仓库实现
/// 
/// **功能依赖**: 需要启用 performance 模块
/// **配置项**: FEATURE_PERFORMANCE
@Injectable(as: IPerformanceRepository)
class PerformanceRepositoryImpl implements IPerformanceRepository {
  final IDatabaseService _databaseService;
  final Uuid _uuid = const Uuid();
  
  // 当前会话ID
  String? _currentSessionId;
  
  // 内存缓存
  final List<PerformanceMetric> _metricsCache = [];
  static const int _maxCacheSize = 1000;
  
  // 存储键
  static const String _metricsKey = 'performance_metrics';
  static const String _sessionKey = 'performance_session';

  PerformanceRepositoryImpl(this._databaseService);

  @override
  Future<void> recordMetric(PerformanceMetric metric) async {
    try {
      // 添加设备和会话信息
      final enrichedMetric = await _enrichMetric(metric);
      
      // 添加到缓存
      _metricsCache.add(enrichedMetric);
      
      // 限制缓存大小
      if (_metricsCache.length > _maxCacheSize) {
        _metricsCache.removeAt(0);
      }
      
      // 持久化存储
      await _persistMetric(enrichedMetric);
      
      // 如果是关键指标，立即处理
      if (enrichedMetric.isCritical) {
        await _handleCriticalMetric(enrichedMetric);
      }
    } catch (e) {
      debugPrint('记录性能指标失败: $e');
    }
  }

  @override
  Future<void> recordMetrics(List<PerformanceMetric> metrics) async {
    try {
      final enrichedMetrics = <PerformanceMetric>[];
      
      for (final metric in metrics) {
        final enriched = await _enrichMetric(metric);
        enrichedMetrics.add(enriched);
        _metricsCache.add(enriched);
      }
      
      // 限制缓存大小
      while (_metricsCache.length > _maxCacheSize) {
        _metricsCache.removeAt(0);
      }
      
      // 批量持久化
      await _persistMetrics(enrichedMetrics);
    } catch (e) {
      debugPrint('批量记录性能指标失败: $e');
    }
  }

  @override
  Future<List<PerformanceMetric>> getMetrics({
    PerformanceMetricType? type,
    DateTime? startTime,
    DateTime? endTime,
    int? limit,
    int? offset,
  }) async {
    try {
      // 从数据库获取指标
      final storedMetrics = await _getStoredMetrics();
      
      // 合并缓存中的指标
      final allMetrics = [...storedMetrics, ..._metricsCache];
      
      // 过滤指标
      var filteredMetrics = allMetrics.where((metric) {
        if (type != null && metric.type != type) return false;
        if (startTime != null && metric.timestamp.isBefore(startTime)) return false;
        if (endTime != null && metric.timestamp.isAfter(endTime)) return false;
        return true;
      }).toList();
      
      // 按时间排序
      filteredMetrics.sort((a, b) => b.timestamp.compareTo(a.timestamp));
      
      // 应用分页
      if (offset != null) {
        filteredMetrics = filteredMetrics.skip(offset).toList();
      }
      if (limit != null) {
        filteredMetrics = filteredMetrics.take(limit).toList();
      }
      
      return filteredMetrics;
    } catch (e) {
      debugPrint('获取性能指标失败: $e');
      return [];
    }
  }

  @override
  Future<Map<String, dynamic>> getMetricStats({
    PerformanceMetricType? type,
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    try {
      final metrics = await getMetrics(
        type: type,
        startTime: startTime,
        endTime: endTime,
      );
      
      if (metrics.isEmpty) {
        return {
          'count': 0,
          'average': 0.0,
          'min': 0.0,
          'max': 0.0,
          'critical_count': 0,
          'warning_count': 0,
          'normal_count': 0,
        };
      }
      
      final values = metrics.map((m) => m.value).toList();
      final sum = values.reduce((a, b) => a + b);
      
      return {
        'count': metrics.length,
        'average': sum / metrics.length,
        'min': values.reduce((a, b) => a < b ? a : b),
        'max': values.reduce((a, b) => a > b ? a : b),
        'critical_count': metrics.where((m) => m.isCritical).length,
        'warning_count': metrics.where((m) => m.isWarning).length,
        'normal_count': metrics.where((m) => m.isNormal).length,
      };
    } catch (e) {
      debugPrint('获取指标统计失败: $e');
      return {};
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getPerformanceTrend({
    required PerformanceMetricType type,
    required DateTime startTime,
    required DateTime endTime,
    String interval = 'hour',
  }) async {
    try {
      final metrics = await getMetrics(
        type: type,
        startTime: startTime,
        endTime: endTime,
      );
      
      // 根据间隔分组数据
      final groupedData = <String, List<PerformanceMetric>>{};
      
      for (final metric in metrics) {
        final key = _getIntervalKey(metric.timestamp, interval);
        groupedData.putIfAbsent(key, () => []).add(metric);
      }
      
      // 计算每个时间段的统计信息
      final trend = <Map<String, dynamic>>[];
      
      for (final entry in groupedData.entries) {
        final values = entry.value.map((m) => m.value).toList();
        final sum = values.reduce((a, b) => a + b);
        
        trend.add({
          'timestamp': entry.key,
          'count': values.length,
          'average': sum / values.length,
          'min': values.reduce((a, b) => a < b ? a : b),
          'max': values.reduce((a, b) => a > b ? a : b),
        });
      }
      
      // 按时间排序
      trend.sort((a, b) => a['timestamp'].compareTo(b['timestamp']));
      
      return trend;
    } catch (e) {
      debugPrint('获取性能趋势失败: $e');
      return [];
    }
  }

  @override
  Future<List<PerformanceMetric>> getPerformanceWarnings({
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    try {
      final metrics = await getMetrics(
        startTime: startTime,
        endTime: endTime,
      );
      
      return metrics.where((metric) => 
        metric.severity == PerformanceSeverity.warning ||
        metric.severity == PerformanceSeverity.critical
      ).toList();
    } catch (e) {
      debugPrint('获取性能警告失败: $e');
      return [];
    }
  }

  @override
  Future<void> cleanupExpiredMetrics(DateTime before) async {
    try {
      // 清理缓存中的过期指标
      _metricsCache.removeWhere((metric) => metric.timestamp.isBefore(before));
      
      // 清理数据库中的过期指标
      final storedMetrics = await _getStoredMetrics();
      final validMetrics = storedMetrics.where((metric) => 
        metric.timestamp.isAfter(before)
      ).toList();
      
      await _saveStoredMetrics(validMetrics);
    } catch (e) {
      debugPrint('清理过期指标失败: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> exportPerformanceData({
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    try {
      final metrics = await getMetrics(
        startTime: startTime,
        endTime: endTime,
      );
      
      final stats = await getMetricStats(
        startTime: startTime,
        endTime: endTime,
      );
      
      return {
        'export_time': DateTime.now().toIso8601String(),
        'start_time': startTime?.toIso8601String(),
        'end_time': endTime?.toIso8601String(),
        'metrics_count': metrics.length,
        'statistics': stats,
        'metrics': metrics.map((m) => m.toJson()).toList(),
      };
    } catch (e) {
      debugPrint('导出性能数据失败: $e');
      return {};
    }
  }

  @override
  Future<Map<String, dynamic>> getDevicePerformanceInfo() async {
    try {
      return {
        'platform': Platform.operatingSystem,
        'platform_version': Platform.operatingSystemVersion,
        'dart_version': Platform.version,
        'is_debug_mode': kDebugMode,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('获取设备性能信息失败: $e');
      return {};
    }
  }

  @override
  Future<String> startPerformanceSession() async {
    try {
      _currentSessionId = _uuid.v4();
      await _databaseService.set(_sessionKey, _currentSessionId);
      return _currentSessionId!;
    } catch (e) {
      debugPrint('开始性能监控会话失败: $e');
      _currentSessionId = _uuid.v4();
      return _currentSessionId!;
    }
  }

  @override
  Future<void> endPerformanceSession(String sessionId) async {
    try {
      if (_currentSessionId == sessionId) {
        _currentSessionId = null;
        await _databaseService.delete(_sessionKey);
      }
    } catch (e) {
      debugPrint('结束性能监控会话失败: $e');
    }
  }

  @override
  String? getCurrentSessionId() => _currentSessionId;

  /// 丰富指标信息
  Future<PerformanceMetric> _enrichMetric(PerformanceMetric metric) async {
    final deviceInfo = await getDevicePerformanceInfo();
    
    return metric.copyWith(
      deviceInfo: json.encode(deviceInfo),
      sessionId: _currentSessionId,
      appVersion: '1.0.0', // TODO: 从应用配置获取
    );
  }

  /// 持久化单个指标
  Future<void> _persistMetric(PerformanceMetric metric) async {
    try {
      final storedMetrics = await _getStoredMetrics();
      storedMetrics.add(metric);
      await _saveStoredMetrics(storedMetrics);
    } catch (e) {
      debugPrint('持久化指标失败: $e');
    }
  }

  /// 批量持久化指标
  Future<void> _persistMetrics(List<PerformanceMetric> metrics) async {
    try {
      final storedMetrics = await _getStoredMetrics();
      storedMetrics.addAll(metrics);
      await _saveStoredMetrics(storedMetrics);
    } catch (e) {
      debugPrint('批量持久化指标失败: $e');
    }
  }

  /// 获取存储的指标
  Future<List<PerformanceMetric>> _getStoredMetrics() async {
    try {
      final metricsJson = await _databaseService.get<String>(_metricsKey);
      if (metricsJson == null) return [];
      
      final List<dynamic> metricsList = json.decode(metricsJson);
      return metricsList
          .map((metricJson) => PerformanceMetric.fromJson(metricJson as Map<String, dynamic>))
          .toList();
    } catch (e) {
      debugPrint('获取存储指标失败: $e');
      return [];
    }
  }

  /// 保存指标到存储
  Future<void> _saveStoredMetrics(List<PerformanceMetric> metrics) async {
    try {
      // 限制存储的指标数量
      final limitedMetrics = metrics.length > 5000 
          ? metrics.sublist(metrics.length - 5000)
          : metrics;
      
      final metricsJson = json.encode(
        limitedMetrics.map((metric) => metric.toJson()).toList()
      );
      await _databaseService.set(_metricsKey, metricsJson);
    } catch (e) {
      debugPrint('保存指标失败: $e');
    }
  }

  /// 处理关键指标
  Future<void> _handleCriticalMetric(PerformanceMetric metric) async {
    try {
      // TODO: 实现关键指标处理逻辑
      // 例如：发送警报、记录日志、触发优化建议等
      debugPrint('关键性能指标: ${metric.name} = ${metric.formattedValue}');
    } catch (e) {
      debugPrint('处理关键指标失败: $e');
    }
  }

  /// 获取时间间隔键
  String _getIntervalKey(DateTime timestamp, String interval) {
    switch (interval) {
      case 'hour':
        return '${timestamp.year}-${timestamp.month.toString().padLeft(2, '0')}-${timestamp.day.toString().padLeft(2, '0')} ${timestamp.hour.toString().padLeft(2, '0')}:00';
      case 'day':
        return '${timestamp.year}-${timestamp.month.toString().padLeft(2, '0')}-${timestamp.day.toString().padLeft(2, '0')}';
      case 'week':
        final weekStart = timestamp.subtract(Duration(days: timestamp.weekday - 1));
        return '${weekStart.year}-${weekStart.month.toString().padLeft(2, '0')}-${weekStart.day.toString().padLeft(2, '0')}';
      default:
        return timestamp.toIso8601String();
    }
  }
}

/// NoOp性能监控仓库实现
/// 
/// 当performance模块禁用时使用的空实现
/// 所有性能监控操作不执行实际逻辑
@Injectable(as: IPerformanceRepository)
@Environment('noop')
class NoOpPerformanceRepository implements IPerformanceRepository {
  const NoOpPerformanceRepository();

  @override
  Future<void> recordMetric(PerformanceMetric metric) async {
    // NoOp: 性能监控功能未启用
  }

  @override
  Future<void> recordMetrics(List<PerformanceMetric> metrics) async {
    // NoOp: 性能监控功能未启用
  }

  @override
  Future<List<PerformanceMetric>> getMetrics({
    PerformanceMetricType? type,
    DateTime? startTime,
    DateTime? endTime,
    int? limit,
    int? offset,
  }) async => [];

  @override
  Future<Map<String, dynamic>> getMetricStats({
    PerformanceMetricType? type,
    DateTime? startTime,
    DateTime? endTime,
  }) async => {};

  @override
  Future<List<Map<String, dynamic>>> getPerformanceTrend({
    required PerformanceMetricType type,
    required DateTime startTime,
    required DateTime endTime,
    String interval = 'hour',
  }) async => [];

  @override
  Future<List<PerformanceMetric>> getPerformanceWarnings({
    DateTime? startTime,
    DateTime? endTime,
  }) async => [];

  @override
  Future<void> cleanupExpiredMetrics(DateTime before) async {
    // NoOp: 性能监控功能未启用
  }

  @override
  Future<Map<String, dynamic>> exportPerformanceData({
    DateTime? startTime,
    DateTime? endTime,
  }) async => {};

  @override
  Future<Map<String, dynamic>> getDevicePerformanceInfo() async => {};

  @override
  Future<String> startPerformanceSession() async => '';

  @override
  Future<void> endPerformanceSession(String sessionId) async {
    // NoOp: 性能监控功能未启用
  }

  @override
  String? getCurrentSessionId() => null;
}
