import 'package:equatable/equatable.dart';
import 'user.dart';

/// 认证令牌实体
/// 
/// **功能依赖**: 需要启用 authentication 模块
/// **配置项**: FEATURE_AUTHENTICATION
class AuthToken extends Equatable {
  const AuthToken({
    required this.accessToken,
    required this.refreshToken,
    required this.tokenType,
    required this.expiresIn,
    this.scope,
    this.issuedAt,
  });

  /// 访问令牌
  final String accessToken;
  
  /// 刷新令牌
  final String refreshToken;
  
  /// 令牌类型（通常是 "Bearer"）
  final String tokenType;
  
  /// 过期时间（秒）
  final int expiresIn;
  
  /// 令牌作用域
  final String? scope;
  
  /// 签发时间
  final DateTime? issuedAt;

  /// 获取过期时间
  DateTime get expiresAt {
    final issued = issuedAt ?? DateTime.now();
    return issued.add(Duration(seconds: expiresIn));
  }

  /// 检查令牌是否已过期
  bool get isExpired {
    return DateTime.now().isAfter(expiresAt);
  }

  /// 检查令牌是否即将过期（5分钟内）
  bool get isExpiringSoon {
    final fiveMinutesFromNow = DateTime.now().add(const Duration(minutes: 5));
    return fiveMinutesFromNow.isAfter(expiresAt);
  }

  /// 获取剩余有效时间
  Duration get remainingTime {
    final now = DateTime.now();
    if (now.isAfter(expiresAt)) {
      return Duration.zero;
    }
    return expiresAt.difference(now);
  }

  /// 复制令牌实体
  AuthToken copyWith({
    String? accessToken,
    String? refreshToken,
    String? tokenType,
    int? expiresIn,
    String? scope,
    DateTime? issuedAt,
  }) {
    return AuthToken(
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      tokenType: tokenType ?? this.tokenType,
      expiresIn: expiresIn ?? this.expiresIn,
      scope: scope ?? this.scope,
      issuedAt: issuedAt ?? this.issuedAt,
    );
  }

  @override
  List<Object?> get props => [
        accessToken,
        refreshToken,
        tokenType,
        expiresIn,
        scope,
        issuedAt,
      ];

  @override
  String toString() {
    return 'AuthToken(tokenType: $tokenType, expiresIn: $expiresIn, isExpired: $isExpired)';
  }
}

/// 认证结果实体
class AuthResult extends Equatable {
  const AuthResult({
    required this.user,
    required this.token,
    this.isFirstLogin = false,
    this.requiresPasswordChange = false,
    this.requiresEmailVerification = false,
    this.requiresTwoFactorAuth = false,
  });

  /// 用户信息
  final User user;
  
  /// 认证令牌
  final AuthToken token;
  
  /// 是否首次登录
  final bool isFirstLogin;
  
  /// 是否需要修改密码
  final bool requiresPasswordChange;
  
  /// 是否需要邮箱验证
  final bool requiresEmailVerification;
  
  /// 是否需要双因素认证
  final bool requiresTwoFactorAuth;

  /// 检查是否需要额外操作
  bool get requiresAdditionalAction {
    return requiresPasswordChange || 
           requiresEmailVerification || 
           requiresTwoFactorAuth;
  }

  /// 复制认证结果
  AuthResult copyWith({
    User? user,
    AuthToken? token,
    bool? isFirstLogin,
    bool? requiresPasswordChange,
    bool? requiresEmailVerification,
    bool? requiresTwoFactorAuth,
  }) {
    return AuthResult(
      user: user ?? this.user,
      token: token ?? this.token,
      isFirstLogin: isFirstLogin ?? this.isFirstLogin,
      requiresPasswordChange: requiresPasswordChange ?? this.requiresPasswordChange,
      requiresEmailVerification: requiresEmailVerification ?? this.requiresEmailVerification,
      requiresTwoFactorAuth: requiresTwoFactorAuth ?? this.requiresTwoFactorAuth,
    );
  }

  @override
  List<Object?> get props => [
        user,
        token,
        isFirstLogin,
        requiresPasswordChange,
        requiresEmailVerification,
        requiresTwoFactorAuth,
      ];

  @override
  String toString() {
    return 'AuthResult(user: ${user.email}, requiresAdditionalAction: $requiresAdditionalAction)';
  }
}
