import 'package:equatable/equatable.dart';

/// 用户实体
/// 
/// **功能依赖**: 需要启用 authentication 模块
/// **配置项**: FEATURE_AUTHENTICATION
class User extends Equatable {
  const User({
    required this.id,
    required this.email,
    required this.name,
    this.avatar,
    this.phone,
    this.roles = const [],
    this.permissions = const [],
    this.lastLoginAt,
    this.createdAt,
    this.updatedAt,
    this.isActive = true,
    this.isEmailVerified = false,
    this.isPhoneVerified = false,
    this.metadata = const {},
  });

  /// 用户ID
  final String id;
  
  /// 邮箱地址
  final String email;
  
  /// 用户名称
  final String name;
  
  /// 头像URL
  final String? avatar;
  
  /// 手机号码
  final String? phone;
  
  /// 用户角色列表
  final List<String> roles;
  
  /// 用户权限列表
  final List<String> permissions;
  
  /// 最后登录时间
  final DateTime? lastLoginAt;
  
  /// 创建时间
  final DateTime? createdAt;
  
  /// 更新时间
  final DateTime? updatedAt;
  
  /// 是否激活
  final bool isActive;
  
  /// 邮箱是否已验证
  final bool isEmailVerified;
  
  /// 手机是否已验证
  final bool isPhoneVerified;
  
  /// 扩展元数据
  final Map<String, dynamic> metadata;

  /// 检查用户是否有指定角色
  bool hasRole(String role) {
    return roles.contains(role);
  }

  /// 检查用户是否有指定权限
  bool hasPermission(String permission) {
    return permissions.contains(permission);
  }

  /// 检查用户是否有任一指定角色
  bool hasAnyRole(List<String> roleList) {
    return roleList.any((role) => roles.contains(role));
  }

  /// 检查用户是否有任一指定权限
  bool hasAnyPermission(List<String> permissionList) {
    return permissionList.any((permission) => permissions.contains(permission));
  }

  /// 检查用户是否有所有指定角色
  bool hasAllRoles(List<String> roleList) {
    return roleList.every((role) => roles.contains(role));
  }

  /// 检查用户是否有所有指定权限
  bool hasAllPermissions(List<String> permissionList) {
    return permissionList.every((permission) => permissions.contains(permission));
  }

  /// 复制用户实体
  User copyWith({
    String? id,
    String? email,
    String? name,
    String? avatar,
    String? phone,
    List<String>? roles,
    List<String>? permissions,
    DateTime? lastLoginAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
    bool? isEmailVerified,
    bool? isPhoneVerified,
    Map<String, dynamic>? metadata,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      avatar: avatar ?? this.avatar,
      phone: phone ?? this.phone,
      roles: roles ?? this.roles,
      permissions: permissions ?? this.permissions,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isPhoneVerified: isPhoneVerified ?? this.isPhoneVerified,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
        id,
        email,
        name,
        avatar,
        phone,
        roles,
        permissions,
        lastLoginAt,
        createdAt,
        updatedAt,
        isActive,
        isEmailVerified,
        isPhoneVerified,
        metadata,
      ];

  @override
  String toString() {
    return 'User(id: $id, email: $email, name: $name, isActive: $isActive)';
  }
}
