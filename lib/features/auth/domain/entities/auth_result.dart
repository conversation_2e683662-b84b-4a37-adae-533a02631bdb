import 'package:equatable/equatable.dart';
import 'user.dart';
import 'auth_token.dart';

/// 认证结果实体
/// 
/// 包含认证操作的完整结果信息
/// **功能依赖**: 需要启用 authentication 模块
/// **配置项**: FEATURE_AUTHENTICATION
class AuthResult extends Equatable {
  const AuthResult({
    required this.user,
    required this.token,
    this.isFirstLogin = false,
    this.requiresPasswordChange = false,
    this.requiresEmailVerification = false,
    this.requiresTwoFactorAuth = false,
    this.permissions = const [],
    this.roles = const [],
    this.sessionId,
    this.expiresAt,
    this.refreshToken,
    this.metadata = const {},
  });

  /// 用户信息
  final User user;

  /// 认证令牌
  final AuthToken token;

  /// 是否首次登录
  final bool isFirstLogin;

  /// 是否需要修改密码
  final bool requiresPasswordChange;

  /// 是否需要邮箱验证
  final bool requiresEmailVerification;

  /// 是否需要双因素认证
  final bool requiresTwoFactorAuth;

  /// 用户权限列表
  final List<String> permissions;

  /// 用户角色列表
  final List<String> roles;

  /// 会话ID
  final String? sessionId;

  /// 令牌过期时间
  final DateTime? expiresAt;

  /// 刷新令牌
  final String? refreshToken;

  /// 额外元数据
  final Map<String, dynamic> metadata;

  /// 是否认证成功
  bool get isAuthenticated => token.accessToken.isNotEmpty;

  /// 是否需要额外验证步骤
  bool get requiresAdditionalVerification =>
      requiresPasswordChange ||
      requiresEmailVerification ||
      requiresTwoFactorAuth;

  /// 是否为完整认证（无需额外步骤）
  bool get isFullyAuthenticated =>
      isAuthenticated && !requiresAdditionalVerification;

  /// 获取用户显示名称
  String get displayName => user.name.isNotEmpty ? user.name : user.email;

  /// 检查是否有特定权限
  bool hasPermission(String permission) {
    return permissions.contains(permission);
  }

  /// 检查是否有特定角色
  bool hasRole(String role) {
    return roles.contains(role);
  }

  /// 检查是否有任一权限
  bool hasAnyPermission(List<String> permissionList) {
    return permissionList.any((permission) => hasPermission(permission));
  }

  /// 检查是否有任一角色
  bool hasAnyRole(List<String> roleList) {
    return roleList.any((role) => hasRole(role));
  }

  /// 复制并更新认证结果
  AuthResult copyWith({
    User? user,
    AuthToken? token,
    bool? isFirstLogin,
    bool? requiresPasswordChange,
    bool? requiresEmailVerification,
    bool? requiresTwoFactorAuth,
    List<String>? permissions,
    List<String>? roles,
    String? sessionId,
    DateTime? expiresAt,
    String? refreshToken,
    Map<String, dynamic>? metadata,
  }) {
    return AuthResult(
      user: user ?? this.user,
      token: token ?? this.token,
      isFirstLogin: isFirstLogin ?? this.isFirstLogin,
      requiresPasswordChange: requiresPasswordChange ?? this.requiresPasswordChange,
      requiresEmailVerification: requiresEmailVerification ?? this.requiresEmailVerification,
      requiresTwoFactorAuth: requiresTwoFactorAuth ?? this.requiresTwoFactorAuth,
      permissions: permissions ?? this.permissions,
      roles: roles ?? this.roles,
      sessionId: sessionId ?? this.sessionId,
      expiresAt: expiresAt ?? this.expiresAt,
      refreshToken: refreshToken ?? this.refreshToken,
      metadata: metadata ?? this.metadata,
    );
  }

  /// 转换为JSON映射
  Map<String, dynamic> toJson() {
    return {
      'user': user.toJson(),
      'token': token.toJson(),
      'isFirstLogin': isFirstLogin,
      'requiresPasswordChange': requiresPasswordChange,
      'requiresEmailVerification': requiresEmailVerification,
      'requiresTwoFactorAuth': requiresTwoFactorAuth,
      'permissions': permissions,
      'roles': roles,
      'sessionId': sessionId,
      'expiresAt': expiresAt?.toIso8601String(),
      'refreshToken': refreshToken,
      'metadata': metadata,
    };
  }

  /// 从JSON映射创建认证结果
  factory AuthResult.fromJson(Map<String, dynamic> json) {
    return AuthResult(
      user: User.fromJson(json['user'] as Map<String, dynamic>),
      token: AuthToken.fromJson(json['token'] as Map<String, dynamic>),
      isFirstLogin: json['isFirstLogin'] as bool? ?? false,
      requiresPasswordChange: json['requiresPasswordChange'] as bool? ?? false,
      requiresEmailVerification: json['requiresEmailVerification'] as bool? ?? false,
      requiresTwoFactorAuth: json['requiresTwoFactorAuth'] as bool? ?? false,
      permissions: (json['permissions'] as List<dynamic>?)?.cast<String>() ?? [],
      roles: (json['roles'] as List<dynamic>?)?.cast<String>() ?? [],
      sessionId: json['sessionId'] as String?,
      expiresAt: json['expiresAt'] != null 
          ? DateTime.parse(json['expiresAt'] as String)
          : null,
      refreshToken: json['refreshToken'] as String?,
      metadata: (json['metadata'] as Map<String, dynamic>?) ?? {},
    );
  }

  @override
  List<Object?> get props => [
        user,
        token,
        isFirstLogin,
        requiresPasswordChange,
        requiresEmailVerification,
        requiresTwoFactorAuth,
        permissions,
        roles,
        sessionId,
        expiresAt,
        refreshToken,
        metadata,
      ];

  @override
  String toString() {
    return 'AuthResult('
        'user: $user, '
        'isAuthenticated: $isAuthenticated, '
        'isFirstLogin: $isFirstLogin, '
        'requiresAdditionalVerification: $requiresAdditionalVerification, '
        'permissions: ${permissions.length}, '
        'roles: ${roles.length}'
        ')';
  }
}

/// 认证结果构建器
/// 
/// 用于构建复杂的认证结果
class AuthResultBuilder {
  User? _user;
  AuthToken? _token;
  bool _isFirstLogin = false;
  bool _requiresPasswordChange = false;
  bool _requiresEmailVerification = false;
  bool _requiresTwoFactorAuth = false;
  List<String> _permissions = [];
  List<String> _roles = [];
  String? _sessionId;
  DateTime? _expiresAt;
  String? _refreshToken;
  Map<String, dynamic> _metadata = {};

  AuthResultBuilder setUser(User user) {
    _user = user;
    return this;
  }

  AuthResultBuilder setToken(AuthToken token) {
    _token = token;
    return this;
  }

  AuthResultBuilder setFirstLogin(bool isFirstLogin) {
    _isFirstLogin = isFirstLogin;
    return this;
  }

  AuthResultBuilder requirePasswordChange() {
    _requiresPasswordChange = true;
    return this;
  }

  AuthResultBuilder requireEmailVerification() {
    _requiresEmailVerification = true;
    return this;
  }

  AuthResultBuilder requireTwoFactorAuth() {
    _requiresTwoFactorAuth = true;
    return this;
  }

  AuthResultBuilder addPermissions(List<String> permissions) {
    _permissions.addAll(permissions);
    return this;
  }

  AuthResultBuilder addRoles(List<String> roles) {
    _roles.addAll(roles);
    return this;
  }

  AuthResultBuilder setSessionId(String sessionId) {
    _sessionId = sessionId;
    return this;
  }

  AuthResultBuilder setExpiresAt(DateTime expiresAt) {
    _expiresAt = expiresAt;
    return this;
  }

  AuthResultBuilder setRefreshToken(String refreshToken) {
    _refreshToken = refreshToken;
    return this;
  }

  AuthResultBuilder addMetadata(String key, dynamic value) {
    _metadata[key] = value;
    return this;
  }

  AuthResult build() {
    if (_user == null || _token == null) {
      throw ArgumentError('User and token are required');
    }

    return AuthResult(
      user: _user!,
      token: _token!,
      isFirstLogin: _isFirstLogin,
      requiresPasswordChange: _requiresPasswordChange,
      requiresEmailVerification: _requiresEmailVerification,
      requiresTwoFactorAuth: _requiresTwoFactorAuth,
      permissions: _permissions,
      roles: _roles,
      sessionId: _sessionId,
      expiresAt: _expiresAt,
      refreshToken: _refreshToken,
      metadata: _metadata,
    );
  }
}
