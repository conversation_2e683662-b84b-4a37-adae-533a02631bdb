import 'package:equatable/equatable.dart';

/// 密码重置请求实体
/// 
/// 包含密码重置操作所需的信息
/// **功能依赖**: 需要启用 authentication 模块
/// **配置项**: FEATURE_AUTHENTICATION
class PasswordResetRequest extends Equatable {
  const PasswordResetRequest({
    required this.token,
    required this.newPassword,
    this.confirmPassword,
    this.email,
    this.userId,
    this.metadata = const {},
  });

  /// 重置令牌
  final String token;

  /// 新密码
  final String newPassword;

  /// 确认密码
  final String? confirmPassword;

  /// 邮箱地址
  final String? email;

  /// 用户ID
  final String? userId;

  /// 额外元数据
  final Map<String, dynamic> metadata;

  /// 验证密码重置请求
  bool get isValid {
    // 检查必填字段
    if (token.isEmpty || newPassword.isEmpty) {
      return false;
    }

    // 检查密码确认
    if (confirmPassword != null && confirmPassword != newPassword) {
      return false;
    }

    // 检查密码强度
    if (!_isPasswordStrong(newPassword)) {
      return false;
    }

    return true;
  }

  /// 获取验证错误信息
  List<String> get validationErrors {
    final errors = <String>[];

    if (token.isEmpty) {
      errors.add('重置令牌不能为空');
    }

    if (newPassword.isEmpty) {
      errors.add('新密码不能为空');
    } else if (!_isPasswordStrong(newPassword)) {
      errors.add('密码强度不足：至少8位，包含大小写字母、数字和特殊字符');
    }

    if (confirmPassword != null && confirmPassword != newPassword) {
      errors.add('确认密码与新密码不匹配');
    }

    return errors;
  }

  /// 检查密码强度
  bool _isPasswordStrong(String password) {
    if (password.length < 8) return false;
    
    final hasUppercase = password.contains(RegExp(r'[A-Z]'));
    final hasLowercase = password.contains(RegExp(r'[a-z]'));
    final hasDigits = password.contains(RegExp(r'[0-9]'));
    final hasSpecialCharacters = password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));
    
    return hasUppercase && hasLowercase && hasDigits && hasSpecialCharacters;
  }

  /// 复制并更新密码重置请求
  PasswordResetRequest copyWith({
    String? token,
    String? newPassword,
    String? confirmPassword,
    String? email,
    String? userId,
    Map<String, dynamic>? metadata,
  }) {
    return PasswordResetRequest(
      token: token ?? this.token,
      newPassword: newPassword ?? this.newPassword,
      confirmPassword: confirmPassword ?? this.confirmPassword,
      email: email ?? this.email,
      userId: userId ?? this.userId,
      metadata: metadata ?? this.metadata,
    );
  }

  /// 转换为JSON映射
  Map<String, dynamic> toJson() {
    return {
      'token': token,
      'newPassword': newPassword,
      'confirmPassword': confirmPassword,
      'email': email,
      'userId': userId,
      'metadata': metadata,
    };
  }

  /// 从JSON映射创建密码重置请求
  factory PasswordResetRequest.fromJson(Map<String, dynamic> json) {
    return PasswordResetRequest(
      token: json['token'] as String,
      newPassword: json['newPassword'] as String,
      confirmPassword: json['confirmPassword'] as String?,
      email: json['email'] as String?,
      userId: json['userId'] as String?,
      metadata: (json['metadata'] as Map<String, dynamic>?) ?? {},
    );
  }

  @override
  List<Object?> get props => [
        token,
        newPassword,
        confirmPassword,
        email,
        userId,
        metadata,
      ];

  @override
  String toString() {
    return 'PasswordResetRequest('
        'token: ${token.isNotEmpty ? '***' : 'empty'}, '
        'hasNewPassword: ${newPassword.isNotEmpty}, '
        'email: $email, '
        'userId: $userId'
        ')';
  }
}

/// 密码修改请求实体
/// 
/// 包含密码修改操作所需的信息
class PasswordChangeRequest extends Equatable {
  const PasswordChangeRequest({
    required this.currentPassword,
    required this.newPassword,
    this.confirmPassword,
    this.userId,
    this.metadata = const {},
  });

  /// 当前密码
  final String currentPassword;

  /// 新密码
  final String newPassword;

  /// 确认密码
  final String? confirmPassword;

  /// 用户ID
  final String? userId;

  /// 额外元数据
  final Map<String, dynamic> metadata;

  /// 验证密码修改请求
  bool get isValid {
    // 检查必填字段
    if (currentPassword.isEmpty || newPassword.isEmpty) {
      return false;
    }

    // 检查密码确认
    if (confirmPassword != null && confirmPassword != newPassword) {
      return false;
    }

    // 检查新旧密码不能相同
    if (currentPassword == newPassword) {
      return false;
    }

    // 检查密码强度
    if (!_isPasswordStrong(newPassword)) {
      return false;
    }

    return true;
  }

  /// 获取验证错误信息
  List<String> get validationErrors {
    final errors = <String>[];

    if (currentPassword.isEmpty) {
      errors.add('当前密码不能为空');
    }

    if (newPassword.isEmpty) {
      errors.add('新密码不能为空');
    } else if (!_isPasswordStrong(newPassword)) {
      errors.add('密码强度不足：至少8位，包含大小写字母、数字和特殊字符');
    }

    if (currentPassword == newPassword) {
      errors.add('新密码不能与当前密码相同');
    }

    if (confirmPassword != null && confirmPassword != newPassword) {
      errors.add('确认密码与新密码不匹配');
    }

    return errors;
  }

  /// 检查密码强度
  bool _isPasswordStrong(String password) {
    if (password.length < 8) return false;
    
    final hasUppercase = password.contains(RegExp(r'[A-Z]'));
    final hasLowercase = password.contains(RegExp(r'[a-z]'));
    final hasDigits = password.contains(RegExp(r'[0-9]'));
    final hasSpecialCharacters = password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));
    
    return hasUppercase && hasLowercase && hasDigits && hasSpecialCharacters;
  }

  /// 复制并更新密码修改请求
  PasswordChangeRequest copyWith({
    String? currentPassword,
    String? newPassword,
    String? confirmPassword,
    String? userId,
    Map<String, dynamic>? metadata,
  }) {
    return PasswordChangeRequest(
      currentPassword: currentPassword ?? this.currentPassword,
      newPassword: newPassword ?? this.newPassword,
      confirmPassword: confirmPassword ?? this.confirmPassword,
      userId: userId ?? this.userId,
      metadata: metadata ?? this.metadata,
    );
  }

  /// 转换为JSON映射
  Map<String, dynamic> toJson() {
    return {
      'currentPassword': currentPassword,
      'newPassword': newPassword,
      'confirmPassword': confirmPassword,
      'userId': userId,
      'metadata': metadata,
    };
  }

  /// 从JSON映射创建密码修改请求
  factory PasswordChangeRequest.fromJson(Map<String, dynamic> json) {
    return PasswordChangeRequest(
      currentPassword: json['currentPassword'] as String,
      newPassword: json['newPassword'] as String,
      confirmPassword: json['confirmPassword'] as String?,
      userId: json['userId'] as String?,
      metadata: (json['metadata'] as Map<String, dynamic>?) ?? {},
    );
  }

  @override
  List<Object?> get props => [
        currentPassword,
        newPassword,
        confirmPassword,
        userId,
        metadata,
      ];

  @override
  String toString() {
    return 'PasswordChangeRequest('
        'hasCurrentPassword: ${currentPassword.isNotEmpty}, '
        'hasNewPassword: ${newPassword.isNotEmpty}, '
        'userId: $userId'
        ')';
  }
}
