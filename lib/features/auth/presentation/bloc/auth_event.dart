import 'package:equatable/equatable.dart';
import '../../domain/entities/user.dart';
import '../../domain/repositories/auth_repository.dart';

/// 认证事件基类
///
/// **功能依赖**: 需要启用 authentication 模块
/// **配置项**: FEATURE_AUTHENTICATION
abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object?> get props => [];
}

/// 登录请求事件
class AuthLoginRequested extends AuthEvent {
  const AuthLoginRequested({
    required this.email,
    required this.password,
    this.rememberMe = false,
  });

  final String email;
  final String password;
  final bool rememberMe;

  @override
  List<Object?> get props => [email, password, rememberMe];
}

/// 登出请求事件
class AuthLogoutRequested extends AuthEvent {
  const AuthLogoutRequested();
}

/// 注册请求事件
class AuthRegisterRequested extends AuthEvent {
  const AuthRegisterRequested({
    required this.email,
    required this.password,
    required this.confirmPassword,
    required this.name,
    this.phone,
    this.acceptTerms = false,
    this.metadata = const {},
  });

  final String email;
  final String password;
  final String confirmPassword;
  final String name;
  final String? phone;
  final bool acceptTerms;
  final Map<String, dynamic> metadata;

  @override
  List<Object?> get props => [
        email,
        password,
        confirmPassword,
        name,
        phone,
        acceptTerms,
        metadata,
      ];
}

/// 检查认证状态事件
class AuthCheckRequested extends AuthEvent {
  const AuthCheckRequested();
}

/// 刷新令牌请求事件
class AuthRefreshTokenRequested extends AuthEvent {
  const AuthRefreshTokenRequested(this.refreshToken);

  final String refreshToken;

  @override
  List<Object> get props => [refreshToken];
}

/// 忘记密码请求事件
class AuthForgotPasswordRequested extends AuthEvent {
  const AuthForgotPasswordRequested(this.email);

  final String email;

  @override
  List<Object> get props => [email];
}

/// 重置密码请求事件
class AuthResetPasswordRequested extends AuthEvent {
  const AuthResetPasswordRequested(this.request);

  final PasswordResetRequest request;

  @override
  List<Object> get props => [request];
}

/// 修改密码请求事件
class AuthChangePasswordRequested extends AuthEvent {
  const AuthChangePasswordRequested(this.request);

  final PasswordChangeRequest request;

  @override
  List<Object> get props => [request];
}

/// 验证邮箱请求事件
class AuthVerifyEmailRequested extends AuthEvent {
  const AuthVerifyEmailRequested(this.token);

  final String token;

  @override
  List<Object> get props => [token];
}

/// 重新发送邮箱验证请求事件
class AuthResendEmailVerificationRequested extends AuthEvent {
  const AuthResendEmailVerificationRequested();
}

/// 验证手机号请求事件
class AuthVerifyPhoneRequested extends AuthEvent {
  const AuthVerifyPhoneRequested({
    required this.phone,
    required this.code,
  });

  final String phone;
  final String code;

  @override
  List<Object> get props => [phone, code];
}

/// 发送手机验证码请求事件
class AuthSendPhoneCodeRequested extends AuthEvent {
  const AuthSendPhoneCodeRequested(this.phone);

  final String phone;

  @override
  List<Object> get props => [phone];
}

/// 启用双因素认证请求事件
class AuthEnableTwoFactorRequested extends AuthEvent {
  const AuthEnableTwoFactorRequested({
    required this.secret,
    required this.code,
  });

  final String secret;
  final String code;

  @override
  List<Object> get props => [secret, code];
}

/// 禁用双因素认证请求事件
class AuthDisableTwoFactorRequested extends AuthEvent {
  const AuthDisableTwoFactorRequested(this.code);

  final String code;

  @override
  List<Object> get props => [code];
}

/// 验证双因素认证请求事件
class AuthVerifyTwoFactorRequested extends AuthEvent {
  const AuthVerifyTwoFactorRequested(this.code);

  final String code;

  @override
  List<Object> get props => [code];
}

/// 获取双因素认证密钥请求事件
class AuthGetTwoFactorSecretRequested extends AuthEvent {
  const AuthGetTwoFactorSecretRequested();
}

/// 社交登录请求事件
class AuthSocialLoginRequested extends AuthEvent {
  const AuthSocialLoginRequested({
    required this.provider,
    required this.accessToken,
  });

  final String provider;
  final String accessToken;

  @override
  List<Object> get props => [provider, accessToken];
}

/// 绑定社交账号请求事件
class AuthBindSocialAccountRequested extends AuthEvent {
  const AuthBindSocialAccountRequested({
    required this.provider,
    required this.accessToken,
  });

  final String provider;
  final String accessToken;

  @override
  List<Object> get props => [provider, accessToken];
}

/// 解绑社交账号请求事件
class AuthUnbindSocialAccountRequested extends AuthEvent {
  const AuthUnbindSocialAccountRequested(this.provider);

  final String provider;

  @override
  List<Object> get props => [provider];
}

/// 删除账号请求事件
class AuthDeleteAccountRequested extends AuthEvent {
  const AuthDeleteAccountRequested(this.password);

  final String password;

  @override
  List<Object> get props => [password];
}

/// 用户信息变更事件
class AuthUserChanged extends AuthEvent {
  const AuthUserChanged(this.user);

  final User user;

  @override
  List<Object> get props => [user];
}

/// 清除错误事件
class AuthClearError extends AuthEvent {
  const AuthClearError();
}
