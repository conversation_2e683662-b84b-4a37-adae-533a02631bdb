import 'package:equatable/equatable.dart';
import '../../domain/entities/user.dart';
import '../../domain/entities/auth_token.dart';

/// 认证状态基类
///
/// **功能依赖**: 需要启用 authentication 模块
/// **配置项**: FEATURE_AUTHENTICATION
abstract class AuthState extends Equatable {
  const AuthState();

  @override
  List<Object?> get props => [];
}

/// 初始状态
class AuthInitial extends AuthState {
  const AuthInitial();

  @override
  List<Object> get props => [];
}

/// 加载中状态
class AuthLoading extends AuthState {
  const AuthLoading();

  @override
  List<Object> get props => [];
}

/// 已认证状态
class AuthAuthenticated extends AuthState {
  const AuthAuthenticated({
    required this.user,
    required this.token,
    this.isFirstLogin = false,
    this.requiresPasswordChange = false,
    this.requiresEmailVerification = false,
    this.requiresTwoFactorAuth = false,
  });

  /// 用户信息
  final User user;
  
  /// 认证令牌
  final AuthToken token;
  
  /// 是否首次登录
  final bool isFirstLogin;
  
  /// 是否需要修改密码
  final bool requiresPasswordChange;
  
  /// 是否需要邮箱验证
  final bool requiresEmailVerification;
  
  /// 是否需要双因素认证
  final bool requiresTwoFactorAuth;

  /// 检查是否需要额外操作
  bool get requiresAdditionalAction {
    return requiresPasswordChange || 
           requiresEmailVerification || 
           requiresTwoFactorAuth;
  }

  /// 复制状态
  AuthAuthenticated copyWith({
    User? user,
    AuthToken? token,
    bool? isFirstLogin,
    bool? requiresPasswordChange,
    bool? requiresEmailVerification,
    bool? requiresTwoFactorAuth,
  }) {
    return AuthAuthenticated(
      user: user ?? this.user,
      token: token ?? this.token,
      isFirstLogin: isFirstLogin ?? this.isFirstLogin,
      requiresPasswordChange: requiresPasswordChange ?? this.requiresPasswordChange,
      requiresEmailVerification: requiresEmailVerification ?? this.requiresEmailVerification,
      requiresTwoFactorAuth: requiresTwoFactorAuth ?? this.requiresTwoFactorAuth,
    );
  }

  @override
  List<Object?> get props => [
        user,
        token,
        isFirstLogin,
        requiresPasswordChange,
        requiresEmailVerification,
        requiresTwoFactorAuth,
      ];

  @override
  String toString() {
    return 'AuthAuthenticated(user: ${user.email}, requiresAdditionalAction: $requiresAdditionalAction)';
  }
}

/// 未认证状态
class AuthUnauthenticated extends AuthState {
  const AuthUnauthenticated();

  @override
  List<Object> get props => [];
}

/// 认证错误状态
class AuthError extends AuthState {
  const AuthError(this.message, {this.code});

  final String message;
  final String? code;

  @override
  List<Object?> get props => [message, code];

  @override
  String toString() {
    return 'AuthError(message: $message, code: $code)';
  }
}

/// 密码重置邮件已发送状态
class AuthPasswordResetEmailSent extends AuthState {
  const AuthPasswordResetEmailSent();

  @override
  List<Object> get props => [];
}

/// 密码重置成功状态
class AuthPasswordResetSuccess extends AuthState {
  const AuthPasswordResetSuccess();

  @override
  List<Object> get props => [];
}

/// 邮箱验证成功状态
class AuthEmailVerificationSuccess extends AuthState {
  const AuthEmailVerificationSuccess();

  @override
  List<Object> get props => [];
}

/// 手机验证成功状态
class AuthPhoneVerificationSuccess extends AuthState {
  const AuthPhoneVerificationSuccess();

  @override
  List<Object> get props => [];
}

/// 手机验证码已发送状态
class AuthPhoneCodeSent extends AuthState {
  const AuthPhoneCodeSent();

  @override
  List<Object> get props => [];
}

/// 双因素认证密钥获取成功状态
class AuthTwoFactorSecretReceived extends AuthState {
  const AuthTwoFactorSecretReceived(this.secret);

  final String secret;

  @override
  List<Object> get props => [secret];
}

/// 双因素认证启用成功状态
class AuthTwoFactorEnabled extends AuthState {
  const AuthTwoFactorEnabled();

  @override
  List<Object> get props => [];
}

/// 双因素认证禁用成功状态
class AuthTwoFactorDisabled extends AuthState {
  const AuthTwoFactorDisabled();

  @override
  List<Object> get props => [];
}

/// 社交账号绑定成功状态
class AuthSocialAccountBound extends AuthState {
  const AuthSocialAccountBound(this.provider);

  final String provider;

  @override
  List<Object> get props => [provider];
}

/// 社交账号解绑成功状态
class AuthSocialAccountUnbound extends AuthState {
  const AuthSocialAccountUnbound(this.provider);

  final String provider;

  @override
  List<Object> get props => [provider];
}

/// 账号删除成功状态
class AuthAccountDeleted extends AuthState {
  const AuthAccountDeleted();

  @override
  List<Object> get props => [];
}

/// 密码修改成功状态
class AuthPasswordChanged extends AuthState {
  const AuthPasswordChanged();

  @override
  List<Object> get props => [];
}
