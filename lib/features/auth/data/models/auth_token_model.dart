import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/auth_token.dart';
import '../../domain/entities/password_reset_request.dart';
import 'user_model.dart';

part 'auth_token_model.g.dart';

/// 认证令牌数据模型
/// 
/// **功能依赖**: 需要启用 authentication 模块
/// **配置项**: FEATURE_AUTHENTICATION
@JsonSerializable()
class AuthTokenModel {
  const AuthTokenModel({
    required this.accessToken,
    required this.refreshToken,
    required this.tokenType,
    required this.expiresIn,
    this.scope,
    this.issuedAt,
  });

  /// 访问令牌
  @Json<PERSON>ey(name: 'access_token')
  final String accessToken;
  
  /// 刷新令牌
  @JsonKey(name: 'refresh_token')
  final String refreshToken;
  
  /// 令牌类型
  @JsonKey(name: 'token_type')
  final String tokenType;
  
  /// 过期时间（秒）
  @JsonKey(name: 'expires_in')
  final int expiresIn;
  
  /// 令牌作用域
  final String? scope;
  
  /// 签发时间
  @JsonKey(name: 'issued_at')
  final DateTime? issuedAt;

  /// 从JSON创建认证令牌模型
  factory AuthTokenModel.fromJson(Map<String, dynamic> json) => 
      _$AuthTokenModelFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$AuthTokenModelToJson(this);

  /// 转换为认证令牌实体
  AuthToken toEntity() {
    return AuthToken(
      accessToken: accessToken,
      refreshToken: refreshToken,
      tokenType: tokenType,
      expiresIn: expiresIn,
      scope: scope,
      issuedAt: issuedAt,
    );
  }

  /// 从认证令牌实体创建模型
  factory AuthTokenModel.fromEntity(AuthToken token) {
    return AuthTokenModel(
      accessToken: token.accessToken,
      refreshToken: token.refreshToken,
      tokenType: token.tokenType,
      expiresIn: token.expiresIn,
      scope: token.scope,
      issuedAt: token.issuedAt,
    );
  }

  /// 复制认证令牌模型
  AuthTokenModel copyWith({
    String? accessToken,
    String? refreshToken,
    String? tokenType,
    int? expiresIn,
    String? scope,
    DateTime? issuedAt,
  }) {
    return AuthTokenModel(
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      tokenType: tokenType ?? this.tokenType,
      expiresIn: expiresIn ?? this.expiresIn,
      scope: scope ?? this.scope,
      issuedAt: issuedAt ?? this.issuedAt,
    );
  }

  @override
  String toString() {
    return 'AuthTokenModel(tokenType: $tokenType, expiresIn: $expiresIn)';
  }
}

/// 认证响应模型
@JsonSerializable()
class AuthResponseModel {
  const AuthResponseModel({
    required this.user,
    required this.token,
    this.isFirstLogin = false,
    this.requiresPasswordChange = false,
    this.requiresEmailVerification = false,
    this.requiresTwoFactorAuth = false,
  });

  /// 用户信息
  final UserModel user;
  
  /// 认证令牌
  final AuthTokenModel token;
  
  /// 是否首次登录
  @JsonKey(name: 'is_first_login')
  final bool isFirstLogin;
  
  /// 是否需要修改密码
  @JsonKey(name: 'requires_password_change')
  final bool requiresPasswordChange;
  
  /// 是否需要邮箱验证
  @JsonKey(name: 'requires_email_verification')
  final bool requiresEmailVerification;
  
  /// 是否需要双因素认证
  @JsonKey(name: 'requires_two_factor_auth')
  final bool requiresTwoFactorAuth;

  /// 从JSON创建认证响应模型
  factory AuthResponseModel.fromJson(Map<String, dynamic> json) => 
      _$AuthResponseModelFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$AuthResponseModelToJson(this);

  /// 转换为认证结果实体
  AuthResult toEntity() {
    return AuthResult(
      user: user.toEntity(),
      token: token.toEntity(),
      isFirstLogin: isFirstLogin,
      requiresPasswordChange: requiresPasswordChange,
      requiresEmailVerification: requiresEmailVerification,
      requiresTwoFactorAuth: requiresTwoFactorAuth,
    );
  }

  /// 从认证结果实体创建模型
  factory AuthResponseModel.fromEntity(AuthResult result) {
    return AuthResponseModel(
      user: UserModel.fromEntity(result.user),
      token: AuthTokenModel.fromEntity(result.token),
      isFirstLogin: result.isFirstLogin,
      requiresPasswordChange: result.requiresPasswordChange,
      requiresEmailVerification: result.requiresEmailVerification,
      requiresTwoFactorAuth: result.requiresTwoFactorAuth,
    );
  }

  /// 复制认证响应模型
  AuthResponseModel copyWith({
    UserModel? user,
    AuthTokenModel? token,
    bool? isFirstLogin,
    bool? requiresPasswordChange,
    bool? requiresEmailVerification,
    bool? requiresTwoFactorAuth,
  }) {
    return AuthResponseModel(
      user: user ?? this.user,
      token: token ?? this.token,
      isFirstLogin: isFirstLogin ?? this.isFirstLogin,
      requiresPasswordChange: requiresPasswordChange ?? this.requiresPasswordChange,
      requiresEmailVerification: requiresEmailVerification ?? this.requiresEmailVerification,
      requiresTwoFactorAuth: requiresTwoFactorAuth ?? this.requiresTwoFactorAuth,
    );
  }

  @override
  String toString() {
    return 'AuthResponseModel(user: ${user.email}, requiresAdditionalAction: ${requiresPasswordChange || requiresEmailVerification || requiresTwoFactorAuth})';
  }
}

/// 密码重置请求模型
@JsonSerializable()
class PasswordResetRequestModel {
  const PasswordResetRequestModel({
    required this.token,
    required this.newPassword,
    required this.confirmPassword,
  });

  /// 重置令牌
  final String token;
  
  /// 新密码
  @JsonKey(name: 'new_password')
  final String newPassword;
  
  /// 确认密码
  @JsonKey(name: 'confirm_password')
  final String confirmPassword;

  /// 从JSON创建密码重置请求模型
  factory PasswordResetRequestModel.fromJson(Map<String, dynamic> json) => 
      _$PasswordResetRequestModelFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$PasswordResetRequestModelToJson(this);

  /// 转换为密码重置请求实体
  PasswordResetRequest toEntity() {
    return PasswordResetRequest(
      token: token,
      newPassword: newPassword,
      confirmPassword: confirmPassword,
    );
  }

  /// 从密码重置请求实体创建模型
  factory PasswordResetRequestModel.fromEntity(PasswordResetRequest request) {
    return PasswordResetRequestModel(
      token: request.token,
      newPassword: request.newPassword,
      confirmPassword: request.confirmPassword,
    );
  }
}

/// 密码修改请求模型
@JsonSerializable()
class PasswordChangeRequestModel {
  const PasswordChangeRequestModel({
    required this.currentPassword,
    required this.newPassword,
    required this.confirmPassword,
  });

  /// 当前密码
  @JsonKey(name: 'current_password')
  final String currentPassword;
  
  /// 新密码
  @JsonKey(name: 'new_password')
  final String newPassword;
  
  /// 确认密码
  @JsonKey(name: 'confirm_password')
  final String confirmPassword;

  /// 从JSON创建密码修改请求模型
  factory PasswordChangeRequestModel.fromJson(Map<String, dynamic> json) => 
      _$PasswordChangeRequestModelFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$PasswordChangeRequestModelToJson(this);

  /// 转换为密码修改请求实体
  PasswordChangeRequest toEntity() {
    return PasswordChangeRequest(
      currentPassword: currentPassword,
      newPassword: newPassword,
      confirmPassword: confirmPassword,
    );
  }

  /// 从密码修改请求实体创建模型
  factory PasswordChangeRequestModel.fromEntity(PasswordChangeRequest request) {
    return PasswordChangeRequestModel(
      currentPassword: request.currentPassword,
      newPassword: request.newPassword,
      confirmPassword: request.confirmPassword,
    );
  }
}
