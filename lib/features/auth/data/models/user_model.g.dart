// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserModel _$UserModelFromJson(Map<String, dynamic> json) => $checkedCreate(
      'UserModel',
      json,
      ($checkedConvert) {
        final val = UserModel(
          id: $checkedConvert('id', (v) => v as String),
          email: $checkedConvert('email', (v) => v as String),
          name: $checkedConvert('name', (v) => v as String),
          avatar: $checkedConvert('avatar', (v) => v as String?),
          phone: $checkedConvert('phone', (v) => v as String?),
          roles: $checkedConvert(
              'roles',
              (v) =>
                  (v as List<dynamic>?)?.map((e) => e as String).toList() ??
                  const []),
          permissions: $checkedConvert(
              'permissions',
              (v) =>
                  (v as List<dynamic>?)?.map((e) => e as String).toList() ??
                  const []),
          lastLoginAt: $checkedConvert('last_login_at',
              (v) => v == null ? null : DateTime.parse(v as String)),
          createdAt: $checkedConvert('created_at',
              (v) => v == null ? null : DateTime.parse(v as String)),
          updatedAt: $checkedConvert('updated_at',
              (v) => v == null ? null : DateTime.parse(v as String)),
          isActive: $checkedConvert('is_active', (v) => v as bool? ?? true),
          isEmailVerified:
              $checkedConvert('is_email_verified', (v) => v as bool? ?? false),
          isPhoneVerified:
              $checkedConvert('is_phone_verified', (v) => v as bool? ?? false),
          metadata: $checkedConvert(
              'metadata', (v) => v as Map<String, dynamic>? ?? const {}),
        );
        return val;
      },
      fieldKeyMap: const {
        'lastLoginAt': 'last_login_at',
        'createdAt': 'created_at',
        'updatedAt': 'updated_at',
        'isActive': 'is_active',
        'isEmailVerified': 'is_email_verified',
        'isPhoneVerified': 'is_phone_verified'
      },
    );

Map<String, dynamic> _$UserModelToJson(UserModel instance) => <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
      'name': instance.name,
      if (instance.avatar case final value?) 'avatar': value,
      if (instance.phone case final value?) 'phone': value,
      'roles': instance.roles,
      'permissions': instance.permissions,
      if (instance.lastLoginAt?.toIso8601String() case final value?)
        'last_login_at': value,
      if (instance.createdAt?.toIso8601String() case final value?)
        'created_at': value,
      if (instance.updatedAt?.toIso8601String() case final value?)
        'updated_at': value,
      'is_active': instance.isActive,
      'is_email_verified': instance.isEmailVerified,
      'is_phone_verified': instance.isPhoneVerified,
      'metadata': instance.metadata,
    };

LoginRequestModel _$LoginRequestModelFromJson(Map<String, dynamic> json) =>
    $checkedCreate(
      'LoginRequestModel',
      json,
      ($checkedConvert) {
        final val = LoginRequestModel(
          email: $checkedConvert('email', (v) => v as String),
          password: $checkedConvert('password', (v) => v as String),
          rememberMe:
              $checkedConvert('remember_me', (v) => v as bool? ?? false),
          deviceInfo:
              $checkedConvert('device_info', (v) => v as Map<String, dynamic>?),
        );
        return val;
      },
      fieldKeyMap: const {
        'rememberMe': 'remember_me',
        'deviceInfo': 'device_info'
      },
    );

Map<String, dynamic> _$LoginRequestModelToJson(LoginRequestModel instance) =>
    <String, dynamic>{
      'email': instance.email,
      'password': instance.password,
      'remember_me': instance.rememberMe,
      if (instance.deviceInfo case final value?) 'device_info': value,
    };

RegisterRequestModel _$RegisterRequestModelFromJson(
        Map<String, dynamic> json) =>
    $checkedCreate(
      'RegisterRequestModel',
      json,
      ($checkedConvert) {
        final val = RegisterRequestModel(
          email: $checkedConvert('email', (v) => v as String),
          password: $checkedConvert('password', (v) => v as String),
          name: $checkedConvert('name', (v) => v as String),
          phone: $checkedConvert('phone', (v) => v as String?),
          acceptTerms:
              $checkedConvert('accept_terms', (v) => v as bool? ?? false),
          metadata: $checkedConvert(
              'metadata', (v) => v as Map<String, dynamic>? ?? const {}),
        );
        return val;
      },
      fieldKeyMap: const {'acceptTerms': 'accept_terms'},
    );

Map<String, dynamic> _$RegisterRequestModelToJson(
        RegisterRequestModel instance) =>
    <String, dynamic>{
      'email': instance.email,
      'password': instance.password,
      'name': instance.name,
      if (instance.phone case final value?) 'phone': value,
      'accept_terms': instance.acceptTerms,
      'metadata': instance.metadata,
    };
