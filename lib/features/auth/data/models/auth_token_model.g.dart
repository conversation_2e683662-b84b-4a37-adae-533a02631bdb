// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_token_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AuthTokenModel _$AuthTokenModelFromJson(Map<String, dynamic> json) =>
    $checkedCreate(
      'AuthTokenModel',
      json,
      ($checkedConvert) {
        final val = AuthTokenModel(
          accessToken: $checkedConvert('access_token', (v) => v as String),
          refreshToken: $checkedConvert('refresh_token', (v) => v as String),
          tokenType: $checkedConvert('token_type', (v) => v as String),
          expiresIn: $checkedConvert('expires_in', (v) => (v as num).toInt()),
          scope: $checkedConvert('scope', (v) => v as String?),
          issuedAt: $checkedConvert('issued_at',
              (v) => v == null ? null : DateTime.parse(v as String)),
        );
        return val;
      },
      fieldKeyMap: const {
        'accessToken': 'access_token',
        'refreshToken': 'refresh_token',
        'tokenType': 'token_type',
        'expiresIn': 'expires_in',
        'issuedAt': 'issued_at'
      },
    );

Map<String, dynamic> _$AuthTokenModelToJson(AuthTokenModel instance) =>
    <String, dynamic>{
      'access_token': instance.accessToken,
      'refresh_token': instance.refreshToken,
      'token_type': instance.tokenType,
      'expires_in': instance.expiresIn,
      if (instance.scope case final value?) 'scope': value,
      if (instance.issuedAt?.toIso8601String() case final value?)
        'issued_at': value,
    };

AuthResponseModel _$AuthResponseModelFromJson(Map<String, dynamic> json) =>
    $checkedCreate(
      'AuthResponseModel',
      json,
      ($checkedConvert) {
        final val = AuthResponseModel(
          user: $checkedConvert(
              'user', (v) => UserModel.fromJson(v as Map<String, dynamic>)),
          token: $checkedConvert('token',
              (v) => AuthTokenModel.fromJson(v as Map<String, dynamic>)),
          isFirstLogin:
              $checkedConvert('is_first_login', (v) => v as bool? ?? false),
          requiresPasswordChange: $checkedConvert(
              'requires_password_change', (v) => v as bool? ?? false),
          requiresEmailVerification: $checkedConvert(
              'requires_email_verification', (v) => v as bool? ?? false),
          requiresTwoFactorAuth: $checkedConvert(
              'requires_two_factor_auth', (v) => v as bool? ?? false),
        );
        return val;
      },
      fieldKeyMap: const {
        'isFirstLogin': 'is_first_login',
        'requiresPasswordChange': 'requires_password_change',
        'requiresEmailVerification': 'requires_email_verification',
        'requiresTwoFactorAuth': 'requires_two_factor_auth'
      },
    );

Map<String, dynamic> _$AuthResponseModelToJson(AuthResponseModel instance) =>
    <String, dynamic>{
      'user': instance.user.toJson(),
      'token': instance.token.toJson(),
      'is_first_login': instance.isFirstLogin,
      'requires_password_change': instance.requiresPasswordChange,
      'requires_email_verification': instance.requiresEmailVerification,
      'requires_two_factor_auth': instance.requiresTwoFactorAuth,
    };

PasswordResetRequestModel _$PasswordResetRequestModelFromJson(
        Map<String, dynamic> json) =>
    $checkedCreate(
      'PasswordResetRequestModel',
      json,
      ($checkedConvert) {
        final val = PasswordResetRequestModel(
          token: $checkedConvert('token', (v) => v as String),
          newPassword: $checkedConvert('new_password', (v) => v as String),
          confirmPassword:
              $checkedConvert('confirm_password', (v) => v as String),
        );
        return val;
      },
      fieldKeyMap: const {
        'newPassword': 'new_password',
        'confirmPassword': 'confirm_password'
      },
    );

Map<String, dynamic> _$PasswordResetRequestModelToJson(
        PasswordResetRequestModel instance) =>
    <String, dynamic>{
      'token': instance.token,
      'new_password': instance.newPassword,
      'confirm_password': instance.confirmPassword,
    };

PasswordChangeRequestModel _$PasswordChangeRequestModelFromJson(
        Map<String, dynamic> json) =>
    $checkedCreate(
      'PasswordChangeRequestModel',
      json,
      ($checkedConvert) {
        final val = PasswordChangeRequestModel(
          currentPassword:
              $checkedConvert('current_password', (v) => v as String),
          newPassword: $checkedConvert('new_password', (v) => v as String),
          confirmPassword:
              $checkedConvert('confirm_password', (v) => v as String),
        );
        return val;
      },
      fieldKeyMap: const {
        'currentPassword': 'current_password',
        'newPassword': 'new_password',
        'confirmPassword': 'confirm_password'
      },
    );

Map<String, dynamic> _$PasswordChangeRequestModelToJson(
        PasswordChangeRequestModel instance) =>
    <String, dynamic>{
      'current_password': instance.currentPassword,
      'new_password': instance.newPassword,
      'confirm_password': instance.confirmPassword,
    };
