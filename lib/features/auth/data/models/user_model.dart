import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/user.dart';

part 'user_model.g.dart';

/// 用户数据模型
/// 
/// **功能依赖**: 需要启用 authentication 模块
/// **配置项**: FEATURE_AUTHENTICATION
@JsonSerializable()
class UserModel {
  const UserModel({
    required this.id,
    required this.email,
    required this.name,
    this.avatar,
    this.phone,
    this.roles = const [],
    this.permissions = const [],
    this.lastLoginAt,
    this.createdAt,
    this.updatedAt,
    this.isActive = true,
    this.isEmailVerified = false,
    this.isPhoneVerified = false,
    this.metadata = const {},
  });

  /// 用户ID
  final String id;
  
  /// 邮箱地址
  final String email;
  
  /// 用户名称
  final String name;
  
  /// 头像URL
  final String? avatar;
  
  /// 手机号码
  final String? phone;
  
  /// 用户角色列表
  final List<String> roles;
  
  /// 用户权限列表
  final List<String> permissions;
  
  /// 最后登录时间
  @JsonKey(name: 'last_login_at')
  final DateTime? lastLoginAt;
  
  /// 创建时间
  @JsonKey(name: 'created_at')
  final DateTime? createdAt;
  
  /// 更新时间
  @JsonKey(name: 'updated_at')
  final DateTime? updatedAt;
  
  /// 是否激活
  @JsonKey(name: 'is_active')
  final bool isActive;
  
  /// 邮箱是否已验证
  @JsonKey(name: 'is_email_verified')
  final bool isEmailVerified;
  
  /// 手机是否已验证
  @JsonKey(name: 'is_phone_verified')
  final bool isPhoneVerified;
  
  /// 扩展元数据
  final Map<String, dynamic> metadata;

  /// 从JSON创建用户模型
  factory UserModel.fromJson(Map<String, dynamic> json) => _$UserModelFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$UserModelToJson(this);

  /// 转换为用户实体
  User toEntity() {
    return User(
      id: id,
      email: email,
      name: name,
      avatar: avatar,
      phone: phone,
      roles: roles,
      permissions: permissions,
      lastLoginAt: lastLoginAt,
      createdAt: createdAt,
      updatedAt: updatedAt,
      isActive: isActive,
      isEmailVerified: isEmailVerified,
      isPhoneVerified: isPhoneVerified,
      metadata: metadata,
    );
  }

  /// 从用户实体创建模型
  factory UserModel.fromEntity(User user) {
    return UserModel(
      id: user.id,
      email: user.email,
      name: user.name,
      avatar: user.avatar,
      phone: user.phone,
      roles: user.roles,
      permissions: user.permissions,
      lastLoginAt: user.lastLoginAt,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      isActive: user.isActive,
      isEmailVerified: user.isEmailVerified,
      isPhoneVerified: user.isPhoneVerified,
      metadata: user.metadata,
    );
  }

  /// 复制用户模型
  UserModel copyWith({
    String? id,
    String? email,
    String? name,
    String? avatar,
    String? phone,
    List<String>? roles,
    List<String>? permissions,
    DateTime? lastLoginAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
    bool? isEmailVerified,
    bool? isPhoneVerified,
    Map<String, dynamic>? metadata,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      avatar: avatar ?? this.avatar,
      phone: phone ?? this.phone,
      roles: roles ?? this.roles,
      permissions: permissions ?? this.permissions,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isPhoneVerified: isPhoneVerified ?? this.isPhoneVerified,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  String toString() {
    return 'UserModel(id: $id, email: $email, name: $name, isActive: $isActive)';
  }
}

/// 登录请求模型
@JsonSerializable()
class LoginRequestModel {
  const LoginRequestModel({
    required this.email,
    required this.password,
    this.rememberMe = false,
    this.deviceInfo,
  });

  /// 邮箱地址
  final String email;
  
  /// 密码
  final String password;
  
  /// 是否记住登录状态
  @JsonKey(name: 'remember_me')
  final bool rememberMe;
  
  /// 设备信息
  @JsonKey(name: 'device_info')
  final Map<String, dynamic>? deviceInfo;

  /// 从JSON创建登录请求模型
  factory LoginRequestModel.fromJson(Map<String, dynamic> json) => 
      _$LoginRequestModelFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$LoginRequestModelToJson(this);
}

/// 注册请求模型
@JsonSerializable()
class RegisterRequestModel {
  const RegisterRequestModel({
    required this.email,
    required this.password,
    required this.name,
    this.phone,
    this.acceptTerms = false,
    this.metadata = const {},
  });

  /// 邮箱地址
  final String email;
  
  /// 密码
  final String password;
  
  /// 用户名称
  final String name;
  
  /// 手机号码
  final String? phone;
  
  /// 是否接受服务条款
  @JsonKey(name: 'accept_terms')
  final bool acceptTerms;
  
  /// 扩展元数据
  final Map<String, dynamic> metadata;

  /// 从JSON创建注册请求模型
  factory RegisterRequestModel.fromJson(Map<String, dynamic> json) => 
      _$RegisterRequestModelFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$RegisterRequestModelToJson(this);
}
