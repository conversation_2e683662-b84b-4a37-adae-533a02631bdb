import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/auth_result.dart';
import '../../domain/entities/user.dart';
import '../../domain/entities/auth_token.dart';
import 'user_model.dart';
import 'auth_token_model.dart';

part 'auth_result_model.g.dart';

/// 认证结果数据模型
/// 
/// 用于JSON序列化和反序列化
/// **功能依赖**: 需要启用 authentication 模块
/// **配置项**: FEATURE_AUTHENTICATION
@JsonSerializable()
class AuthResultModel extends AuthResult {
  const AuthResultModel({
    required UserModel user,
    required AuthTokenModel token,
    bool isFirstLogin = false,
    bool requiresPasswordChange = false,
    bool requiresEmailVerification = false,
    bool requiresTwoFactorAuth = false,
    List<String> permissions = const [],
    List<String> roles = const [],
    String? sessionId,
    DateTime? expiresAt,
    String? refreshToken,
    Map<String, dynamic> metadata = const {},
  }) : super(
          user: user,
          token: token,
          isFirstLogin: isFirstLogin,
          requiresPasswordChange: requiresPasswordChange,
          requiresEmailVerification: requiresEmailVerification,
          requiresTwoFactorAuth: requiresTwoFactorAuth,
          permissions: permissions,
          roles: roles,
          sessionId: sessionId,
          expiresAt: expiresAt,
          refreshToken: refreshToken,
          metadata: metadata,
        );

  /// 从JSON创建认证结果模型
  factory AuthResultModel.fromJson(Map<String, dynamic> json) =>
      _$AuthResultModelFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$AuthResultModelToJson(this);

  /// 从认证结果实体创建模型
  factory AuthResultModel.fromEntity(AuthResult entity) {
    return AuthResultModel(
      user: entity.user is UserModel 
          ? entity.user as UserModel
          : UserModel.fromEntity(entity.user),
      token: entity.token is AuthTokenModel
          ? entity.token as AuthTokenModel
          : AuthTokenModel.fromEntity(entity.token),
      isFirstLogin: entity.isFirstLogin,
      requiresPasswordChange: entity.requiresPasswordChange,
      requiresEmailVerification: entity.requiresEmailVerification,
      requiresTwoFactorAuth: entity.requiresTwoFactorAuth,
      permissions: entity.permissions,
      roles: entity.roles,
      sessionId: entity.sessionId,
      expiresAt: entity.expiresAt,
      refreshToken: entity.refreshToken,
      metadata: entity.metadata,
    );
  }

  /// 转换为认证结果实体
  AuthResult toEntity() {
    return AuthResult(
      user: (user as UserModel).toEntity(),
      token: (token as AuthTokenModel).toEntity(),
      isFirstLogin: isFirstLogin,
      requiresPasswordChange: requiresPasswordChange,
      requiresEmailVerification: requiresEmailVerification,
      requiresTwoFactorAuth: requiresTwoFactorAuth,
      permissions: permissions,
      roles: roles,
      sessionId: sessionId,
      expiresAt: expiresAt,
      refreshToken: refreshToken,
      metadata: metadata,
    );
  }

  /// 复制并更新认证结果模型
  AuthResultModel copyWith({
    UserModel? user,
    AuthTokenModel? token,
    bool? isFirstLogin,
    bool? requiresPasswordChange,
    bool? requiresEmailVerification,
    bool? requiresTwoFactorAuth,
    List<String>? permissions,
    List<String>? roles,
    String? sessionId,
    DateTime? expiresAt,
    String? refreshToken,
    Map<String, dynamic>? metadata,
  }) {
    return AuthResultModel(
      user: user ?? this.user as UserModel,
      token: token ?? this.token as AuthTokenModel,
      isFirstLogin: isFirstLogin ?? this.isFirstLogin,
      requiresPasswordChange: requiresPasswordChange ?? this.requiresPasswordChange,
      requiresEmailVerification: requiresEmailVerification ?? this.requiresEmailVerification,
      requiresTwoFactorAuth: requiresTwoFactorAuth ?? this.requiresTwoFactorAuth,
      permissions: permissions ?? this.permissions,
      roles: roles ?? this.roles,
      sessionId: sessionId ?? this.sessionId,
      expiresAt: expiresAt ?? this.expiresAt,
      refreshToken: refreshToken ?? this.refreshToken,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  String toString() {
    return 'AuthResultModel('
        'user: $user, '
        'isAuthenticated: $isAuthenticated, '
        'isFirstLogin: $isFirstLogin, '
        'requiresAdditionalVerification: $requiresAdditionalVerification, '
        'permissions: ${permissions.length}, '
        'roles: ${roles.length}'
        ')';
  }
}

/// 认证结果响应模型
/// 
/// 用于处理API响应
@JsonSerializable()
class AuthResultResponseModel {
  const AuthResultResponseModel({
    required this.success,
    required this.data,
    this.message,
    this.errors = const [],
    this.metadata = const {},
  });

  /// 是否成功
  final bool success;

  /// 认证结果数据
  final AuthResultModel? data;

  /// 响应消息
  final String? message;

  /// 错误信息列表
  final List<String> errors;

  /// 额外元数据
  final Map<String, dynamic> metadata;

  /// 从JSON创建响应模型
  factory AuthResultResponseModel.fromJson(Map<String, dynamic> json) =>
      _$AuthResultResponseModelFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$AuthResultResponseModelToJson(this);

  /// 是否有错误
  bool get hasErrors => errors.isNotEmpty;

  /// 是否有数据
  bool get hasData => data != null;

  /// 获取第一个错误信息
  String? get firstError => errors.isNotEmpty ? errors.first : null;

  @override
  String toString() {
    return 'AuthResultResponseModel('
        'success: $success, '
        'hasData: $hasData, '
        'hasErrors: $hasErrors, '
        'message: $message'
        ')';
  }
}

/// 认证结果列表响应模型
/// 
/// 用于处理多个认证结果的API响应
@JsonSerializable()
class AuthResultListResponseModel {
  const AuthResultListResponseModel({
    required this.success,
    required this.data,
    this.message,
    this.errors = const [],
    this.pagination,
    this.metadata = const {},
  });

  /// 是否成功
  final bool success;

  /// 认证结果列表
  final List<AuthResultModel> data;

  /// 响应消息
  final String? message;

  /// 错误信息列表
  final List<String> errors;

  /// 分页信息
  final Map<String, dynamic>? pagination;

  /// 额外元数据
  final Map<String, dynamic> metadata;

  /// 从JSON创建列表响应模型
  factory AuthResultListResponseModel.fromJson(Map<String, dynamic> json) =>
      _$AuthResultListResponseModelFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$AuthResultListResponseModelToJson(this);

  /// 是否有错误
  bool get hasErrors => errors.isNotEmpty;

  /// 是否有数据
  bool get hasData => data.isNotEmpty;

  /// 获取第一个错误信息
  String? get firstError => errors.isNotEmpty ? errors.first : null;

  /// 数据总数
  int get totalCount => data.length;

  @override
  String toString() {
    return 'AuthResultListResponseModel('
        'success: $success, '
        'totalCount: $totalCount, '
        'hasErrors: $hasErrors, '
        'message: $message'
        ')';
  }
}
