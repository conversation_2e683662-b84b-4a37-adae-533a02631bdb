import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';
import 'package:dio/dio.dart';
import '../../../../core/errors/failures.dart';
import '../../domain/entities/user.dart';
import '../../domain/entities/auth_token.dart';
import '../../domain/repositories/auth_repository.dart';
import '../datasources/auth_local_datasource.dart';
import '../datasources/auth_remote_datasource.dart';
import '../models/user_model.dart';
import '../models/auth_token_model.dart';

/// 认证仓库实现
/// 
/// **功能依赖**: 需要启用 authentication 模块
/// **配置项**: FEATURE_AUTHENTICATION
@Injectable(as: IAuthRepository)
class AuthRepositoryImpl implements IAuthRepository {
  AuthRepositoryImpl(
    this._remoteDataSource,
    this._localDataSource,
  );

  final AuthRemoteDataSource _remoteDataSource;
  final AuthLocalDataSource _localDataSource;

  @override
  Future<Either<Failure, AuthResult>> login(
    String email,
    String password, {
    bool rememberMe = false,
  }) async {
    try {
      // 创建登录请求
      final request = LoginRequestModel(
        email: email,
        password: password,
        rememberMe: rememberMe,
      );

      // 发送登录请求
      final response = await _remoteDataSource.login(request);
      
      // 保存认证数据到本地
      await _saveAuthDataLocally(response, rememberMe, email);
      
      // 返回认证结果
      return Right(response.toEntity());
    } on DioException catch (e) {
      return Left(_mapDioExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure('登录失败: $e'));
    }
  }

  @override
  Future<Either<Failure, AuthResult>> register(UserRegistration registration) async {
    try {
      // 创建注册请求
      final request = RegisterRequestModel(
        email: registration.email,
        password: registration.password,
        name: registration.name,
        phone: registration.phone,
        acceptTerms: registration.acceptTerms,
        metadata: registration.metadata,
      );

      // 发送注册请求
      final response = await _remoteDataSource.register(request);
      
      // 保存认证数据到本地
      await _saveAuthDataLocally(response, false, registration.email);
      
      // 返回认证结果
      return Right(response.toEntity());
    } on DioException catch (e) {
      return Left(_mapDioExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure('注册失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> logout() async {
    try {
      // 发送登出请求到服务器
      await _remoteDataSource.logout();
      
      // 清除本地认证数据
      await _localDataSource.clearAuthData();
      
      return const Right(null);
    } on DioException catch (e) {
      // 即使服务器登出失败，也要清除本地数据
      await _localDataSource.clearAuthData();
      return Left(_mapDioExceptionToFailure(e));
    } catch (e) {
      // 即使出现异常，也要清除本地数据
      await _localDataSource.clearAuthData();
      return Left(UnknownFailure('登出失败: $e'));
    }
  }

  @override
  Future<Either<Failure, AuthToken>> refreshToken(String refreshToken) async {
    try {
      // 发送刷新令牌请求
      final tokenModel = await _remoteDataSource.refreshToken(refreshToken);
      
      // 保存新的令牌到本地
      await _localDataSource.saveAuthToken(tokenModel);
      
      return Right(tokenModel.toEntity());
    } on DioException catch (e) {
      return Left(_mapDioExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure('刷新令牌失败: $e'));
    }
  }

  @override
  Future<Either<Failure, User?>> getCurrentUser() async {
    try {
      // 首先尝试从本地获取用户信息
      final localUser = await _localDataSource.getUser();
      if (localUser != null) {
        return Right(localUser.toEntity());
      }

      // 如果本地没有，从服务器获取
      final remoteUser = await _remoteDataSource.getCurrentUser();
      
      // 保存到本地
      await _localDataSource.saveUser(remoteUser);
      
      return Right(remoteUser.toEntity());
    } on DioException catch (e) {
      // 如果网络请求失败，尝试返回本地用户信息
      final localUser = await _localDataSource.getUser();
      if (localUser != null) {
        return Right(localUser.toEntity());
      }
      return Left(_mapDioExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure('获取用户信息失败: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> isAuthenticated() async {
    try {
      final isLoggedIn = await _localDataSource.isLoggedIn();
      
      if (!isLoggedIn) {
        return const Right(false);
      }

      // 检查令牌是否过期
      final token = await _localDataSource.getAuthToken();
      if (token != null && token.toEntity().isExpired) {
        // 尝试刷新令牌
        final refreshToken = await _localDataSource.getRefreshToken();
        if (refreshToken != null) {
          final refreshResult = await this.refreshToken(refreshToken);
          return refreshResult.fold(
            (failure) => const Right(false),
            (newToken) => const Right(true),
          );
        }
        return const Right(false);
      }

      return const Right(true);
    } catch (e) {
      return Left(UnknownFailure('检查认证状态失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> forgotPassword(String email) async {
    try {
      await _remoteDataSource.forgotPassword(email);
      return const Right(null);
    } on DioException catch (e) {
      return Left(_mapDioExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure('发送重置密码邮件失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> resetPassword(PasswordResetRequest request) async {
    try {
      final requestModel = PasswordResetRequestModel.fromEntity(request);
      await _remoteDataSource.resetPassword(requestModel);
      return const Right(null);
    } on DioException catch (e) {
      return Left(_mapDioExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure('重置密码失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> changePassword(PasswordChangeRequest request) async {
    try {
      final requestModel = PasswordChangeRequestModel.fromEntity(request);
      await _remoteDataSource.changePassword(requestModel);
      return const Right(null);
    } on DioException catch (e) {
      return Left(_mapDioExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure('修改密码失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> verifyEmail(String token) async {
    try {
      await _remoteDataSource.verifyEmail(token);
      
      // 更新本地用户信息
      final user = await _localDataSource.getUser();
      if (user != null) {
        final updatedUser = user.copyWith(isEmailVerified: true);
        await _localDataSource.saveUser(updatedUser);
      }
      
      return const Right(null);
    } on DioException catch (e) {
      return Left(_mapDioExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure('邮箱验证失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> resendEmailVerification() async {
    try {
      await _remoteDataSource.resendEmailVerification();
      return const Right(null);
    } on DioException catch (e) {
      return Left(_mapDioExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure('重新发送验证邮件失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> verifyPhone(String phone, String code) async {
    try {
      await _remoteDataSource.verifyPhone(phone, code);

      // 更新本地用户信息
      final user = await _localDataSource.getUser();
      if (user != null) {
        final updatedUser = user.copyWith(
          isPhoneVerified: true,
          phone: phone,
        );
        await _localDataSource.saveUser(updatedUser);
      }

      return const Right(null);
    } on DioException catch (e) {
      return Left(_mapDioExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure('手机验证失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> sendPhoneVerificationCode(String phone) async {
    try {
      await _remoteDataSource.sendPhoneVerificationCode(phone);
      return const Right(null);
    } on DioException catch (e) {
      return Left(_mapDioExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure('发送手机验证码失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> enableTwoFactorAuth(String secret, String code) async {
    try {
      await _remoteDataSource.enableTwoFactorAuth(secret, code);
      return const Right(null);
    } on DioException catch (e) {
      return Left(_mapDioExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure('启用双因素认证失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> disableTwoFactorAuth(String code) async {
    try {
      await _remoteDataSource.disableTwoFactorAuth(code);
      return const Right(null);
    } on DioException catch (e) {
      return Left(_mapDioExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure('禁用双因素认证失败: $e'));
    }
  }

  @override
  Future<Either<Failure, AuthResult>> verifyTwoFactorAuth(String code) async {
    try {
      final response = await _remoteDataSource.verifyTwoFactorAuth(code);

      // 更新本地认证数据
      await _saveAuthDataLocally(response, false, response.user.email);

      return Right(response.toEntity());
    } on DioException catch (e) {
      return Left(_mapDioExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure('双因素认证验证失败: $e'));
    }
  }

  @override
  Future<Either<Failure, String>> getTwoFactorAuthSecret() async {
    try {
      final secret = await _remoteDataSource.getTwoFactorAuthSecret();
      return Right(secret);
    } on DioException catch (e) {
      return Left(_mapDioExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure('获取双因素认证密钥失败: $e'));
    }
  }

  @override
  Future<Either<Failure, AuthResult>> socialLogin(String provider, String accessToken) async {
    try {
      final response = await _remoteDataSource.socialLogin(provider, accessToken);

      // 保存认证数据到本地
      await _saveAuthDataLocally(response, false, response.user.email);

      return Right(response.toEntity());
    } on DioException catch (e) {
      return Left(_mapDioExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure('社交登录失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> bindSocialAccount(String provider, String accessToken) async {
    try {
      await _remoteDataSource.bindSocialAccount(provider, accessToken);
      return const Right(null);
    } on DioException catch (e) {
      return Left(_mapDioExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure('绑定社交账号失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> unbindSocialAccount(String provider) async {
    try {
      await _remoteDataSource.unbindSocialAccount(provider);
      return const Right(null);
    } on DioException catch (e) {
      return Left(_mapDioExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure('解绑社交账号失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteAccount(String password) async {
    try {
      await _remoteDataSource.deleteAccount(password);

      // 清除本地认证数据
      await _localDataSource.clearAuthData();

      return const Right(null);
    } on DioException catch (e) {
      return Left(_mapDioExceptionToFailure(e));
    } catch (e) {
      return Left(UnknownFailure('删除账号失败: $e'));
    }
  }

  /// 保存认证数据到本地
  Future<void> _saveAuthDataLocally(
    AuthResponseModel response,
    bool rememberMe,
    String email,
  ) async {
    await Future.wait([
      _localDataSource.saveUser(response.user),
      _localDataSource.saveAuthToken(response.token),
      _localDataSource.saveRefreshToken(response.token.refreshToken),
      _localDataSource.saveRememberMe(rememberMe),
      _localDataSource.saveLastLoginEmail(email),
    ]);
  }

  /// 将DioException映射为业务错误
  Failure _mapDioExceptionToFailure(DioException exception) {
    switch (exception.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return const NetworkFailure(
          message: '网络连接超时，请检查网络设置',
          code: 'NETWORK_TIMEOUT',
        );

      case DioExceptionType.connectionError:
        return const NetworkFailure(
          message: '网络连接失败，请检查网络设置',
          code: 'NETWORK_ERROR',
        );

      case DioExceptionType.badResponse:
        return _handleBadResponse(exception.response!);

      case DioExceptionType.cancel:
        return const NetworkFailure(
          message: '请求已取消',
          code: 'REQUEST_CANCELLED',
        );

      case DioExceptionType.unknown:
      default:
        return NetworkFailure(
          message: '未知网络错误: ${exception.message}',
          code: 'UNKNOWN_ERROR',
        );
    }
  }

  /// 处理HTTP响应错误
  Failure _handleBadResponse(Response response) {
    final statusCode = response.statusCode ?? 0;
    final data = response.data;

    String message = '请求失败';
    String code = 'HTTP_$statusCode';

    if (data is Map<String, dynamic>) {
      message = data['message'] ?? data['error'] ?? message;
      code = data['code'] ?? code;
    }

    switch (statusCode) {
      case 400:
        return ValidationFailure(
          message: message.isNotEmpty ? message : '请求参数错误',
          code: code,
          details: data is Map ? data : null,
        );

      case 401:
        return const AuthFailure(
          message: '认证失败，请重新登录',
          code: 'UNAUTHORIZED',
        );

      case 403:
        return const AuthFailure(
          message: '权限不足，无法访问该资源',
          code: 'FORBIDDEN',
        );

      case 422:
        return ValidationFailure(
          message: message.isNotEmpty ? message : '数据验证失败',
          code: code,
          details: data is Map ? data : null,
        );

      case 429:
        return const NetworkFailure(
          message: '请求过于频繁，请稍后再试',
          code: 'RATE_LIMITED',
        );

      case 500:
      case 502:
      case 503:
      case 504:
        return ServerFailure(
          message: message.isNotEmpty ? message : '服务器错误，请稍后再试',
          code: code,
        );

      default:
        return ServerFailure(
          message: message.isNotEmpty ? message : '服务器响应异常',
          code: code,
        );
    }
  }
