import 'package:injectable/injectable.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:hive/hive.dart';
import 'dart:convert';
import '../models/user_model.dart';
import '../models/auth_token_model.dart';

/// 认证本地数据源接口
abstract class AuthLocalDataSource {
  /// 保存用户信息
  Future<void> saveUser(UserModel user);
  
  /// 获取用户信息
  Future<UserModel?> getUser();
  
  /// 删除用户信息
  Future<void> deleteUser();
  
  /// 保存认证令牌
  Future<void> saveAuthToken(AuthTokenModel token);
  
  /// 获取认证令牌
  Future<AuthTokenModel?> getAuthToken();
  
  /// 删除认证令牌
  Future<void> deleteAuthToken();
  
  /// 保存刷新令牌
  Future<void> saveRefreshToken(String refreshToken);
  
  /// 获取刷新令牌
  Future<String?> getRefreshToken();
  
  /// 删除刷新令牌
  Future<void> deleteRefreshToken();
  
  /// 检查是否已登录
  Future<bool> isLoggedIn();
  
  /// 清除所有认证数据
  Future<void> clearAuthData();
  
  /// 保存记住登录状态
  Future<void> saveRememberMe(bool rememberMe);
  
  /// 获取记住登录状态
  Future<bool> getRememberMe();
  
  /// 保存最后登录邮箱
  Future<void> saveLastLoginEmail(String email);
  
  /// 获取最后登录邮箱
  Future<String?> getLastLoginEmail();
}

/// 认证本地数据源实现
/// 
/// **功能依赖**: 需要启用 authentication 模块
/// **配置项**: FEATURE_AUTHENTICATION
@Injectable(as: AuthLocalDataSource)
class AuthLocalDataSourceImpl implements AuthLocalDataSource {
  AuthLocalDataSourceImpl(this._secureStorage, this._hiveBox);

  final FlutterSecureStorage _secureStorage;
  final Box _hiveBox;

  // 安全存储键名
  static const String _accessTokenKey = 'auth_access_token';
  static const String _refreshTokenKey = 'auth_refresh_token';
  
  // Hive存储键名
  static const String _userKey = 'auth_user';
  static const String _rememberMeKey = 'auth_remember_me';
  static const String _lastLoginEmailKey = 'auth_last_login_email';

  @override
  Future<void> saveUser(UserModel user) async {
    try {
      final userJson = json.encode(user.toJson());
      await _hiveBox.put(_userKey, userJson);
    } catch (e) {
      throw Exception('保存用户信息失败: $e');
    }
  }

  @override
  Future<UserModel?> getUser() async {
    try {
      final userJson = _hiveBox.get(_userKey) as String?;
      if (userJson != null) {
        final userMap = json.decode(userJson) as Map<String, dynamic>;
        return UserModel.fromJson(userMap);
      }
      return null;
    } catch (e) {
      throw Exception('获取用户信息失败: $e');
    }
  }

  @override
  Future<void> deleteUser() async {
    try {
      await _hiveBox.delete(_userKey);
    } catch (e) {
      throw Exception('删除用户信息失败: $e');
    }
  }

  @override
  Future<void> saveAuthToken(AuthTokenModel token) async {
    try {
      await _secureStorage.write(
        key: _accessTokenKey,
        value: token.accessToken,
      );
      
      // 同时保存令牌的完整信息到Hive（除了敏感的token值）
      final tokenInfo = {
        'token_type': token.tokenType,
        'expires_in': token.expiresIn,
        'scope': token.scope,
        'issued_at': token.issuedAt?.toIso8601String(),
      };
      await _hiveBox.put('auth_token_info', json.encode(tokenInfo));
    } catch (e) {
      throw Exception('保存认证令牌失败: $e');
    }
  }

  @override
  Future<AuthTokenModel?> getAuthToken() async {
    try {
      final accessToken = await _secureStorage.read(key: _accessTokenKey);
      final refreshToken = await _secureStorage.read(key: _refreshTokenKey);
      
      if (accessToken != null && refreshToken != null) {
        // 获取令牌信息
        final tokenInfoJson = _hiveBox.get('auth_token_info') as String?;
        if (tokenInfoJson != null) {
          final tokenInfo = json.decode(tokenInfoJson) as Map<String, dynamic>;
          
          return AuthTokenModel(
            accessToken: accessToken,
            refreshToken: refreshToken,
            tokenType: tokenInfo['token_type'] ?? 'Bearer',
            expiresIn: tokenInfo['expires_in'] ?? 3600,
            scope: tokenInfo['scope'],
            issuedAt: tokenInfo['issued_at'] != null 
                ? DateTime.parse(tokenInfo['issued_at'])
                : null,
          );
        }
        
        // 如果没有详细信息，返回基本令牌
        return AuthTokenModel(
          accessToken: accessToken,
          refreshToken: refreshToken,
          tokenType: 'Bearer',
          expiresIn: 3600,
        );
      }
      
      return null;
    } catch (e) {
      throw Exception('获取认证令牌失败: $e');
    }
  }

  @override
  Future<void> deleteAuthToken() async {
    try {
      await Future.wait([
        _secureStorage.delete(key: _accessTokenKey),
        _hiveBox.delete('auth_token_info'),
      ]);
    } catch (e) {
      throw Exception('删除认证令牌失败: $e');
    }
  }

  @override
  Future<void> saveRefreshToken(String refreshToken) async {
    try {
      await _secureStorage.write(
        key: _refreshTokenKey,
        value: refreshToken,
      );
    } catch (e) {
      throw Exception('保存刷新令牌失败: $e');
    }
  }

  @override
  Future<String?> getRefreshToken() async {
    try {
      return await _secureStorage.read(key: _refreshTokenKey);
    } catch (e) {
      throw Exception('获取刷新令牌失败: $e');
    }
  }

  @override
  Future<void> deleteRefreshToken() async {
    try {
      await _secureStorage.delete(key: _refreshTokenKey);
    } catch (e) {
      throw Exception('删除刷新令牌失败: $e');
    }
  }

  @override
  Future<bool> isLoggedIn() async {
    try {
      final accessToken = await _secureStorage.read(key: _accessTokenKey);
      final user = await getUser();
      return accessToken != null && user != null;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<void> clearAuthData() async {
    try {
      await Future.wait([
        deleteUser(),
        deleteAuthToken(),
        deleteRefreshToken(),
        _hiveBox.delete('auth_token_info'),
      ]);
    } catch (e) {
      throw Exception('清除认证数据失败: $e');
    }
  }

  @override
  Future<void> saveRememberMe(bool rememberMe) async {
    try {
      await _hiveBox.put(_rememberMeKey, rememberMe);
    } catch (e) {
      throw Exception('保存记住登录状态失败: $e');
    }
  }

  @override
  Future<bool> getRememberMe() async {
    try {
      return _hiveBox.get(_rememberMeKey, defaultValue: false) as bool;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<void> saveLastLoginEmail(String email) async {
    try {
      await _hiveBox.put(_lastLoginEmailKey, email);
    } catch (e) {
      throw Exception('保存最后登录邮箱失败: $e');
    }
  }

  @override
  Future<String?> getLastLoginEmail() async {
    try {
      return _hiveBox.get(_lastLoginEmailKey) as String?;
    } catch (e) {
      return null;
    }
  }
}
