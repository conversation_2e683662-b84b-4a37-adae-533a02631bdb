import 'package:injectable/injectable.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import '../config/environment_config.dart';

/// 数据库管理器
/// 
/// 统一管理Hive数据库的初始化、配置和生命周期
/// **功能依赖**: 需要启用 database 模块
/// **配置项**: FEATURE_DATABASE
@singleton
class DatabaseManager {
  DatabaseManager(this._config, this._secureStorage);

  final EnvironmentConfig _config;
  final FlutterSecureStorage _secureStorage;
  
  static const String _encryptionKeyName = 'hive_encryption_key';
  static const String _defaultBoxName = 'app_data';
  static const String _cacheBoxName = 'cache_data';
  static const String _userBoxName = 'user_data';
  static const String _settingsBoxName = 'settings_data';

  bool _isInitialized = false;
  final Map<String, Box> _openBoxes = {};

  /// 初始化数据库
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // 初始化Hive
      await Hive.initFlutter();

      // 设置数据库路径
      final appDocDir = await getApplicationDocumentsDirectory();
      final dbPath = '${appDocDir.path}/database';
      await Directory(dbPath).create(recursive: true);
      
      // 注册适配器
      await _registerAdapters();

      // 获取或生成加密密钥
      final encryptionKey = await _getOrCreateEncryptionKey();

      // 打开默认数据库盒子
      await _openDefaultBoxes(encryptionKey);

      _isInitialized = true;
      print('✅ Database initialized successfully');
    } catch (e) {
      print('❌ Database initialization failed: $e');
      rethrow;
    }
  }

  /// 注册Hive适配器
  Future<void> _registerAdapters() async {
    // 在这里注册自定义的Hive适配器
    // 例如：Hive.registerAdapter(UserModelAdapter());
    
    // 注册基础类型适配器（如果需要）
    if (!Hive.isAdapterRegistered(0)) {
      // Hive.registerAdapter(CustomAdapter());
    }
  }

  /// 获取或创建加密密钥
  Future<List<int>> _getOrCreateEncryptionKey() async {
    try {
      // 尝试从安全存储中获取密钥
      final keyString = await _secureStorage.read(key: _encryptionKeyName);
      
      if (keyString != null) {
        return keyString.split(',').map(int.parse).toList();
      }

      // 生成新的256位密钥
      final key = Hive.generateSecureKey();
      
      // 保存到安全存储
      await _secureStorage.write(
        key: _encryptionKeyName,
        value: key.join(','),
      );

      return key;
    } catch (e) {
      print('Warning: Failed to manage encryption key: $e');
      // 如果安全存储失败，使用默认密钥（不推荐用于生产环境）
      return Hive.generateSecureKey();
    }
  }

  /// 打开默认数据库盒子
  Future<void> _openDefaultBoxes(List<int> encryptionKey) async {
    final encryptionCipher = HiveAesCipher(encryptionKey);

    // 打开默认盒子
    _openBoxes[_defaultBoxName] = await Hive.openBox(
      _defaultBoxName,
      encryptionCipher: encryptionCipher,
    );

    // 打开缓存盒子（不加密，提高性能）
    _openBoxes[_cacheBoxName] = await Hive.openBox(_cacheBoxName);

    // 打开用户数据盒子（加密）
    _openBoxes[_userBoxName] = await Hive.openBox(
      _userBoxName,
      encryptionCipher: encryptionCipher,
    );

    // 打开设置盒子（加密）
    _openBoxes[_settingsBoxName] = await Hive.openBox(
      _settingsBoxName,
      encryptionCipher: encryptionCipher,
    );
  }

  /// 获取默认数据盒子
  Box get defaultBox {
    _ensureInitialized();
    return _openBoxes[_defaultBoxName]!;
  }

  /// 获取缓存数据盒子
  Box get cacheBox {
    _ensureInitialized();
    return _openBoxes[_cacheBoxName]!;
  }

  /// 获取用户数据盒子
  Box get userBox {
    _ensureInitialized();
    return _openBoxes[_userBoxName]!;
  }

  /// 获取设置数据盒子
  Box get settingsBox {
    _ensureInitialized();
    return _openBoxes[_settingsBoxName]!;
  }

  /// 打开自定义盒子
  Future<Box> openBox(
    String name, {
    bool encrypted = true,
    HiveCipher? encryptionCipher,
  }) async {
    _ensureInitialized();

    if (_openBoxes.containsKey(name)) {
      return _openBoxes[name]!;
    }

    HiveCipher? cipher = encryptionCipher;
    if (encrypted && cipher == null) {
      final encryptionKey = await _getOrCreateEncryptionKey();
      cipher = HiveAesCipher(encryptionKey);
    }

    final box = await Hive.openBox(
      name,
      encryptionCipher: cipher,
    );

    _openBoxes[name] = box;
    return box;
  }

  /// 关闭指定盒子
  Future<void> closeBox(String name) async {
    if (_openBoxes.containsKey(name)) {
      await _openBoxes[name]!.close();
      _openBoxes.remove(name);
    }
  }

  /// 删除指定盒子
  Future<void> deleteBox(String name) async {
    await closeBox(name);
    await Hive.deleteBoxFromDisk(name);
  }

  /// 清空指定盒子
  Future<void> clearBox(String name) async {
    if (_openBoxes.containsKey(name)) {
      await _openBoxes[name]!.clear();
    }
  }

  /// 压缩数据库
  Future<void> compactDatabase() async {
    _ensureInitialized();

    for (final box in _openBoxes.values) {
      await box.compact();
    }
  }

  /// 获取数据库统计信息
  DatabaseStats getDatabaseStats() {
    _ensureInitialized();

    int totalEntries = 0;
    int totalBoxes = _openBoxes.length;
    Map<String, int> boxSizes = {};

    for (final entry in _openBoxes.entries) {
      final boxName = entry.key;
      final box = entry.value;
      final size = box.length;
      
      totalEntries += size;
      boxSizes[boxName] = size;
    }

    return DatabaseStats(
      totalBoxes: totalBoxes,
      totalEntries: totalEntries,
      boxSizes: boxSizes,
    );
  }

  /// 备份数据库
  Future<String> backupDatabase() async {
    _ensureInitialized();

    final appDocDir = await getApplicationDocumentsDirectory();
    final backupDir = Directory('${appDocDir.path}/backup');
    await backupDir.create(recursive: true);

    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final backupPath = '${backupDir.path}/backup_$timestamp.hive';

    // 这里可以实现具体的备份逻辑
    // 例如：将所有盒子的数据导出到JSON文件

    return backupPath;
  }

  /// 恢复数据库
  Future<void> restoreDatabase(String backupPath) async {
    _ensureInitialized();

    // 这里可以实现具体的恢复逻辑
    // 例如：从备份文件恢复数据到各个盒子
  }

  /// 清理过期数据
  Future<void> cleanupExpiredData() async {
    _ensureInitialized();

    final now = DateTime.now();
    final cacheBox = this.cacheBox;

    // 清理过期的缓存数据
    final keysToDelete = <dynamic>[];
    for (final key in cacheBox.keys) {
      final data = cacheBox.get(key);
      if (data is Map && data.containsKey('expires_at')) {
        final expiresAt = DateTime.parse(data['expires_at'] as String);
        if (now.isAfter(expiresAt)) {
          keysToDelete.add(key);
        }
      }
    }

    for (final key in keysToDelete) {
      await cacheBox.delete(key);
    }

    print('Cleaned up ${keysToDelete.length} expired cache entries');
  }

  /// 关闭所有数据库连接
  Future<void> close() async {
    for (final box in _openBoxes.values) {
      await box.close();
    }
    _openBoxes.clear();
    _isInitialized = false;
  }

  /// 确保数据库已初始化
  void _ensureInitialized() {
    if (!_isInitialized) {
      throw StateError('Database not initialized. Call initialize() first.');
    }
  }
}

/// 数据库统计信息
class DatabaseStats {
  const DatabaseStats({
    required this.totalBoxes,
    required this.totalEntries,
    required this.boxSizes,
  });

  final int totalBoxes;
  final int totalEntries;
  final Map<String, int> boxSizes;

  @override
  String toString() {
    return 'DatabaseStats(boxes: $totalBoxes, entries: $totalEntries, sizes: $boxSizes)';
  }
}
