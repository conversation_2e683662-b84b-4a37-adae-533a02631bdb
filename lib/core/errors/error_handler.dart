/// 错误处理器
/// 
/// 提供统一的错误处理和转换功能
library;

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'exceptions.dart';
import 'failures.dart';

/// 错误处理器
class ErrorHandler {
  ErrorHandler._();

  /// 将异常转换为Failure
  static Failure handleException(dynamic exception) {
    if (exception is Failure) {
      return exception;
    }

    if (exception is DioException) {
      return _handleDioException(exception);
    }

    if (exception is AppException) {
      return _handleAppException(exception);
    }

    // 处理系统异常
    if (exception is FormatException) {
      return FormatFailure(
        message: 'Data format error: ${exception.message}',
        expectedFormat: 'valid format',
        actualFormat: exception.source ?? 'unknown',
      );
    }

    if (exception is ArgumentError) {
      return ValidationFailure(
        message: 'Invalid argument: ${exception.message}',
      );
    }

    if (exception is StateError) {
      return BusinessLogicFailure(
        message: 'State error: ${exception.message}',
      );
    }

    if (exception is TimeoutException) {
      return TimeoutFailure(
        message: 'Operation timeout: ${exception.message}',
      );
    }

    // 默认为未知错误
    return UnknownFailure(
      message: 'Unknown error: ${exception.toString()}',
    );
  }

  /// 处理Dio异常
  static Failure _handleDioException(DioException exception) {
    switch (exception.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return TimeoutFailure(
          message: 'Network timeout: ${exception.message}',
          code: 'NETWORK_TIMEOUT',
        );

      case DioExceptionType.connectionError:
        return NetworkFailure(
          message: 'Network connection error: ${exception.message}',
          code: 'CONNECTION_ERROR',
        );

      case DioExceptionType.badResponse:
        final statusCode = exception.response?.statusCode;
        final message = exception.response?.data?['message'] ?? 
                       exception.response?.statusMessage ?? 
                       'Server error';

        if (statusCode == 400) {
          return ValidationFailure(
            message: 'Bad request: $message',
            code: 'BAD_REQUEST',
          );
        } else if (statusCode == 401) {
          return AuthenticationFailure(
            message: 'Authentication failed: $message',
            code: 'UNAUTHORIZED',
          );
        } else if (statusCode == 403) {
          return AuthorizationFailure(
            message: 'Access forbidden: $message',
            code: 'FORBIDDEN',
          );
        } else if (statusCode == 404) {
          return DataFailure(
            message: 'Resource not found: $message',
            code: 'NOT_FOUND',
          );
        } else if (statusCode == 422) {
          return ValidationFailure(
            message: 'Validation error: $message',
            code: 'VALIDATION_ERROR',
          );
        } else if (statusCode != null && statusCode >= 500) {
          return ServerFailure(
            message: 'Server error: $message',
            code: 'SERVER_ERROR',
            statusCode: statusCode,
          );
        } else {
          return NetworkFailure(
            message: 'Network error: $message',
            code: 'NETWORK_ERROR',
            statusCode: statusCode,
          );
        }

      case DioExceptionType.cancel:
        return NetworkFailure(
          message: 'Request cancelled',
          code: 'REQUEST_CANCELLED',
        );

      case DioExceptionType.badCertificate:
        return NetworkFailure(
          message: 'Certificate error: ${exception.message}',
          code: 'CERTIFICATE_ERROR',
        );

      case DioExceptionType.unknown:
      default:
        return UnknownFailure(
          message: 'Unknown network error: ${exception.message}',
          code: 'UNKNOWN_NETWORK_ERROR',
        );
    }
  }

  /// 处理应用异常
  static Failure _handleAppException(AppException exception) {
    switch (exception.runtimeType) {
      case NetworkException:
        final networkException = exception as NetworkException;
        return NetworkFailure(
          message: networkException.message,
          code: networkException.code,
          details: networkException.details,
          statusCode: networkException.statusCode,
        );

      case ServerException:
        final serverException = exception as ServerException;
        return ServerFailure(
          message: serverException.message,
          code: serverException.code,
          details: serverException.details,
          statusCode: serverException.statusCode,
        );

      case CacheException:
        return CacheFailure(
          message: exception.message,
          code: exception.code,
          details: exception.details,
        );

      case DatabaseException:
        return DatabaseFailure(
          message: exception.message,
          code: exception.code,
          details: exception.details,
        );

      case ValidationException:
        final validationException = exception as ValidationException;
        return ValidationFailure(
          message: validationException.message,
          code: validationException.code,
          details: validationException.details,
          fieldErrors: validationException.fieldErrors,
        );

      case AuthenticationException:
        return AuthenticationFailure(
          message: exception.message,
          code: exception.code,
          details: exception.details,
        );

      case AuthorizationException:
        return AuthorizationFailure(
          message: exception.message,
          code: exception.code,
          details: exception.details,
        );

      case BusinessLogicException:
        return BusinessLogicFailure(
          message: exception.message,
          code: exception.code,
          details: exception.details,
        );

      case ConfigurationException:
        return ConfigurationFailure(
          message: exception.message,
          code: exception.code,
          details: exception.details,
        );

      case PermissionException:
        return PermissionFailure(
          message: exception.message,
          code: exception.code,
          details: exception.details,
        );

      case TimeoutException:
        return TimeoutFailure(
          message: exception.message,
          code: exception.code,
          details: exception.details,
        );

      case FeatureDisabledException:
        final featureException = exception as FeatureDisabledException;
        return FeatureDisabledFailure(
          message: featureException.message,
          code: featureException.code,
          details: featureException.details,
          featureName: featureException.featureName,
        );

      case DependencyException:
        final dependencyException = exception as DependencyException;
        return DependencyFailure(
          message: dependencyException.message,
          code: dependencyException.code,
          details: dependencyException.details,
          dependencyName: dependencyException.dependencyName,
        );

      case ConcurrencyException:
        return ConcurrencyFailure(
          message: exception.message,
          code: exception.code,
          details: exception.details,
        );

      case ResourceException:
        final resourceException = exception as ResourceException;
        return ResourceFailure(
          message: resourceException.message,
          code: resourceException.code,
          details: resourceException.details,
          resourceType: resourceException.resourceType,
        );

      case FormatException:
        final formatException = exception as FormatException;
        return FormatFailure(
          message: formatException.message,
          code: formatException.code,
          details: formatException.details,
          expectedFormat: formatException.expectedFormat,
          actualFormat: formatException.actualFormat,
        );

      case VersionIncompatibilityException:
        final versionException = exception as VersionIncompatibilityException;
        return VersionIncompatibilityFailure(
          message: versionException.message,
          code: versionException.code,
          details: versionException.details,
          requiredVersion: versionException.requiredVersion,
          currentVersion: versionException.currentVersion,
        );

      case DataException:
        return DataFailure(
          message: exception.message,
          code: exception.code,
          details: exception.details,
        );

      case ParseException:
        return FormatFailure(
          message: exception.message,
          code: exception.code,
          details: exception.details,
          expectedFormat: 'valid format',
          actualFormat: (exception as ParseException).source ?? 'unknown',
        );

      default:
        return UnknownFailure(
          message: exception.message,
          code: exception.code,
          details: exception.details,
        );
    }
  }

  /// 记录错误日志
  static void logError(dynamic error, [StackTrace? stackTrace]) {
    if (kDebugMode) {
      print('Error: $error');
      if (stackTrace != null) {
        print('StackTrace: $stackTrace');
      }
    }
    
    // 在生产环境中，这里可以集成崩溃报告服务
    // 如 Firebase Crashlytics, Sentry 等
  }

  /// 获取用户友好的错误消息
  static String getUserFriendlyMessage(Failure failure) {
    switch (failure.runtimeType) {
      case NetworkFailure:
        return '网络连接失败，请检查网络设置';
      case ServerFailure:
        return '服务器错误，请稍后重试';
      case TimeoutFailure:
        return '请求超时，请稍后重试';
      case AuthenticationFailure:
        return '身份验证失败，请重新登录';
      case AuthorizationFailure:
        return '权限不足，无法执行此操作';
      case ValidationFailure:
        return '输入信息有误，请检查后重试';
      case CacheFailure:
        return '缓存错误，请清除缓存后重试';
      case DatabaseFailure:
        return '数据库错误，请稍后重试';
      case FeatureDisabledFailure:
        return '此功能暂未开放';
      default:
        return failure.message.isNotEmpty ? failure.message : '未知错误，请稍后重试';
    }
  }
}
