import 'package:equatable/equatable.dart';

/// 失败基础类
/// 
/// 所有业务失败都应该继承此类
/// 用于函数式错误处理，配合Either使用
abstract class Failure extends Equatable {
  const Failure({
    required this.message,
    this.code,
    this.details,
  });
  
  /// 错误消息
  final String message;
  
  /// 错误代码
  final String? code;
  
  /// 错误详情
  final Map<String, dynamic>? details;
  
  @override
  List<Object?> get props => [message, code, details];
  
  @override
  String toString() => 'Failure(message: $message, code: $code)';
}

/// 网络失败
class NetworkFailure extends Failure {
  const NetworkFailure({
    required super.message,
    super.code,
    super.details,
    this.statusCode,
  });
  
  final int? statusCode;
  
  @override
  List<Object?> get props => [...super.props, statusCode];
}

/// 服务器失败
class ServerFailure extends Failure {
  const ServerFailure({
    required super.message,
    super.code,
    super.details,
    this.statusCode,
  });
  
  final int? statusCode;
  
  @override
  List<Object?> get props => [...super.props, statusCode];
}

/// 缓存失败
class CacheFailure extends Failure {
  const CacheFailure({
    required super.message,
    super.code,
    super.details,
  });
}

/// 数据库失败
class DatabaseFailure extends Failure {
  const DatabaseFailure({
    required super.message,
    super.code,
    super.details,
  });
}

/// 验证失败
class ValidationFailure extends Failure {
  const ValidationFailure({
    required super.message,
    super.code,
    super.details,
    this.fieldErrors,
  });
  
  final Map<String, List<String>>? fieldErrors;
  
  @override
  List<Object?> get props => [...super.props, fieldErrors];
}

/// 认证失败
class AuthenticationFailure extends Failure {
  const AuthenticationFailure({
    required super.message,
    super.code,
    super.details,
  });
}

/// 授权失败
class AuthorizationFailure extends Failure {
  const AuthorizationFailure({
    required super.message,
    super.code,
    super.details,
  });
}

/// 业务逻辑失败
class BusinessLogicFailure extends Failure {
  const BusinessLogicFailure({
    required super.message,
    super.code,
    super.details,
  });
}

/// 配置失败
class ConfigurationFailure extends Failure {
  const ConfigurationFailure({
    required super.message,
    super.code,
    super.details,
  });
}

/// 权限失败
class PermissionFailure extends Failure {
  const PermissionFailure({
    required super.message,
    super.code,
    super.details,
  });
}

/// 超时失败
class TimeoutFailure extends Failure {
  const TimeoutFailure({
    required super.message,
    super.code,
    super.details,
  });
}

/// 未知失败
class UnknownFailure extends Failure {
  const UnknownFailure({
    required super.message,
    super.code,
    super.details,
  });
}

/// 功能未启用失败
class FeatureDisabledFailure extends Failure {
  const FeatureDisabledFailure({
    required super.message,
    super.code,
    super.details,
    required this.featureName,
  });
  
  final String featureName;
  
  @override
  List<Object?> get props => [...super.props, featureName];
}

/// 依赖失败
class DependencyFailure extends Failure {
  const DependencyFailure({
    required super.message,
    super.code,
    super.details,
    required this.dependencyName,
  });
  
  final String dependencyName;
  
  @override
  List<Object?> get props => [...super.props, dependencyName];
}

/// 并发失败
class ConcurrencyFailure extends Failure {
  const ConcurrencyFailure({
    required super.message,
    super.code,
    super.details,
  });
}

/// 资源不足失败
class ResourceFailure extends Failure {
  const ResourceFailure({
    required super.message,
    super.code,
    super.details,
    required this.resourceType,
  });
  
  final String resourceType;
  
  @override
  List<Object?> get props => [...super.props, resourceType];
}

/// 格式失败
class FormatFailure extends Failure {
  const FormatFailure({
    required super.message,
    super.code,
    super.details,
    required this.expectedFormat,
    required this.actualFormat,
  });
  
  final String expectedFormat;
  final String actualFormat;
  
  @override
  List<Object?> get props => [...super.props, expectedFormat, actualFormat];
}

/// 版本不兼容失败
class VersionIncompatibilityFailure extends Failure {
  const VersionIncompatibilityFailure({
    required super.message,
    super.code,
    super.details,
    required this.requiredVersion,
    required this.currentVersion,
  });

  final String requiredVersion;
  final String currentVersion;

  @override
  List<Object?> get props => [...super.props, requiredVersion, currentVersion];
}

/// 数据失败
class DataFailure extends Failure {
  const DataFailure({
    required super.message,
    super.code,
    super.details,
  });
}

/// 认证失败（别名，用于兼容）
class AuthFailure extends AuthenticationFailure {
  const AuthFailure({
    required super.message,
    super.code,
    super.details,
  });
}
