/// 应用启动优化器
/// 
/// 提供应用启动时的性能优化功能，包括预加载、预热和延迟初始化
library;

import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get_it/get_it.dart';
import 'package:enterprise_flutter/core/config/feature_config.dart';
import 'package:enterprise_flutter/core/logging/logger.dart';

/// 应用启动优化器
class AppStartupOptimizer {
  static bool _isOptimized = false;
  
  /// 优化应用启动
  static Future<void> optimizeStartup() async {
    if (_isOptimized) return;
    
    final stopwatch = Stopwatch()..start();
    Logger.i('开始应用启动优化...');
    
    try {
      // 1. 预加载关键资源
      await _preloadCriticalAssets();
      
      // 2. 初始化核心服务
      await _initializeCoreServices();
      
      // 3. 预热关键组件
      await _warmupCriticalComponents();
      
      // 4. 延迟初始化非关键服务
      _scheduleNonCriticalInitialization();
      
      _isOptimized = true;
      stopwatch.stop();
      Logger.i('应用启动优化完成，耗时: ${stopwatch.elapsedMilliseconds}ms');
    } catch (e) {
      stopwatch.stop();
      Logger.e('应用启动优化失败: $e');
      rethrow;
    }
  }
  
  /// 预加载关键资源
  static Future<void> _preloadCriticalAssets() async {
    final stopwatch = Stopwatch()..start();
    
    try {
      // 获取当前上下文
      final context = WidgetsBinding.instance.rootElement?.context;
      if (context == null) {
        Logger.w('无法获取上下文，跳过图片预加载');
        return;
      }
      
      // 预加载关键图片
      final imagePreloadFutures = <Future>[];
      
      // 预加载应用图标和启动图
      final criticalImages = [
        'assets/images/logo.png',
        'assets/images/splash.png',
        'assets/images/app_icon.png',
      ];
      
      for (final imagePath in criticalImages) {
        try {
          imagePreloadFutures.add(
            precacheImage(AssetImage(imagePath), context)
          );
        } catch (e) {
          Logger.w('预加载图片失败: $imagePath, 错误: $e');
        }
      }
      
      // 等待所有图片预加载完成
      await Future.wait(imagePreloadFutures);
      
      // 预加载字体
      await _preloadFonts();
      
      stopwatch.stop();
      Logger.i('关键资源预加载完成，耗时: ${stopwatch.elapsedMilliseconds}ms');
    } catch (e) {
      stopwatch.stop();
      Logger.e('关键资源预加载失败: $e');
    }
  }
  
  /// 预加载字体
  static Future<void> _preloadFonts() async {
    try {
      // 预加载系统字体
      const fontFamilies = [
        'Roboto',
        'SF Pro Display',
        'PingFang SC',
      ];
      
      for (final fontFamily in fontFamilies) {
        try {
          await rootBundle.load('fonts/$fontFamily.ttf');
        } catch (e) {
          // 字体文件可能不存在，忽略错误
          Logger.d('字体文件不存在: $fontFamily');
        }
      }
      
      Logger.d('字体预加载完成');
    } catch (e) {
      Logger.w('字体预加载失败: $e');
    }
  }
  
  /// 初始化核心服务
  static Future<void> _initializeCoreServices() async {
    final stopwatch = Stopwatch()..start();
    
    try {
      final futures = <Future>[];
      
      // 并行初始化核心服务
      if (FeatureConfig.instance.isFeatureEnabled(Features.authentication)) {
        futures.add(_initializeService('IAuthenticationService'));
      }
      
      if (FeatureConfig.instance.isFeatureEnabled(Features.database)) {
        futures.add(_initializeService('IDatabaseService'));
      }
      
      if (FeatureConfig.instance.isFeatureEnabled(Features.network)) {
        futures.add(_initializeService('INetworkService'));
      }
      
      // 等待所有核心服务初始化完成
      await Future.wait(futures);
      
      stopwatch.stop();
      Logger.i('核心服务初始化完成，耗时: ${stopwatch.elapsedMilliseconds}ms');
    } catch (e) {
      stopwatch.stop();
      Logger.e('核心服务初始化失败: $e');
    }
  }
  
  /// 初始化单个服务
  static Future<void> _initializeService(String serviceName) async {
    try {
      final service = GetIt.instance.get(instanceName: serviceName);
      if (service != null && service is Initializable) {
        await service.initialize();
        Logger.d('服务初始化完成: $serviceName');
      }
    } catch (e) {
      Logger.w('服务初始化失败: $serviceName, 错误: $e');
    }
  }
  
  /// 预热关键组件
  static Future<void> _warmupCriticalComponents() async {
    final stopwatch = Stopwatch()..start();
    
    try {
      // 预热路由系统
      await _warmupRouting();
      
      // 预热状态管理
      await _warmupStateManagement();
      
      // 预热主题系统
      await _warmupThemeSystem();
      
      stopwatch.stop();
      Logger.i('关键组件预热完成，耗时: ${stopwatch.elapsedMilliseconds}ms');
    } catch (e) {
      stopwatch.stop();
      Logger.e('关键组件预热失败: $e');
    }
  }
  
  /// 预热路由系统
  static Future<void> _warmupRouting() async {
    try {
      // 预热路由配置
      Logger.d('路由系统预热完成');
    } catch (e) {
      Logger.w('路由系统预热失败: $e');
    }
  }
  
  /// 预热状态管理
  static Future<void> _warmupStateManagement() async {
    try {
      // 预热状态管理器
      Logger.d('状态管理预热完成');
    } catch (e) {
      Logger.w('状态管理预热失败: $e');
    }
  }
  
  /// 预热主题系统
  static Future<void> _warmupThemeSystem() async {
    try {
      // 预热主题配置
      Logger.d('主题系统预热完成');
    } catch (e) {
      Logger.w('主题系统预热失败: $e');
    }
  }
  
  /// 延迟初始化非关键服务
  static void _scheduleNonCriticalInitialization() {
    // 延迟 500ms 初始化非关键服务
    Timer(const Duration(milliseconds: 500), () async {
      await _initializeNonCriticalServices();
    });
  }
  
  /// 初始化非关键服务
  static Future<void> _initializeNonCriticalServices() async {
    final stopwatch = Stopwatch()..start();
    
    try {
      final futures = <Future>[];
      
      // 分析服务
      if (FeatureConfig.instance.isFeatureEnabled(Features.analytics)) {
        futures.add(_initializeService('IAnalyticsService'));
      }
      
      // 推送通知服务
      if (FeatureConfig.instance.isFeatureEnabled(Features.pushNotifications)) {
        futures.add(_initializeService('IPushNotificationService'));
      }
      
      // 性能监控服务
      if (FeatureConfig.instance.isFeatureEnabled(Features.performanceMonitoring)) {
        futures.add(_initializeService('IPerformanceMonitoringService'));
      }
      
      // 等待所有非关键服务初始化完成
      await Future.wait(futures);
      
      stopwatch.stop();
      Logger.i('非关键服务初始化完成，耗时: ${stopwatch.elapsedMilliseconds}ms');
    } catch (e) {
      stopwatch.stop();
      Logger.e('非关键服务初始化失败: $e');
    }
  }
  
  /// 获取启动优化状态
  static bool get isOptimized => _isOptimized;
  
  /// 重置优化状态（用于测试）
  static void reset() {
    _isOptimized = false;
  }
}

/// 可初始化接口
abstract class Initializable {
  Future<void> initialize();
}
