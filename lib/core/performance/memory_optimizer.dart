/// 内存优化器
/// 
/// 提供内存监控和优化功能，包括内存清理、缓存管理等
library;

import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/painting.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:enterprise_flutter/core/logging/logger.dart';

/// 内存优化器
class MemoryOptimizer {
  static Timer? _memoryMonitorTimer;
  static const int _memoryThreshold = 150 * 1024 * 1024; // 150MB
  static const int _criticalMemoryThreshold = 200 * 1024 * 1024; // 200MB
  static const Duration _monitorInterval = Duration(minutes: 1);
  
  static bool _isMonitoring = false;
  static int _lastMemoryUsage = 0;
  static final List<MemoryUsageRecord> _memoryHistory = [];
  
  /// 开始内存监控
  static void startMemoryMonitoring() {
    if (_isMonitoring) return;
    
    _isMonitoring = true;
    Logger.i('开始内存监控...');
    
    _memoryMonitorTimer = Timer.periodic(_monitorInterval, (_) {
      _checkMemoryUsage();
    });
  }
  
  /// 停止内存监控
  static void stopMemoryMonitoring() {
    if (!_isMonitoring) return;
    
    _isMonitoring = false;
    _memoryMonitorTimer?.cancel();
    _memoryMonitorTimer = null;
    
    Logger.i('内存监控已停止');
  }
  
  /// 检查内存使用情况
  static Future<void> _checkMemoryUsage() async {
    try {
      final memoryUsage = await getCurrentMemoryUsage();
      _lastMemoryUsage = memoryUsage;
      
      // 记录内存使用历史
      _recordMemoryUsage(memoryUsage);
      
      final memoryMB = memoryUsage / 1024 / 1024;
      
      if (memoryUsage > _criticalMemoryThreshold) {
        Logger.w('内存使用过高: ${memoryMB.toStringAsFixed(1)}MB，执行紧急清理');
        await performCriticalMemoryCleanup();
      } else if (memoryUsage > _memoryThreshold) {
        Logger.w('内存使用较高: ${memoryMB.toStringAsFixed(1)}MB，执行常规清理');
        await optimizeMemory();
      } else {
        Logger.d('内存使用正常: ${memoryMB.toStringAsFixed(1)}MB');
      }
    } catch (e) {
      Logger.e('内存检查失败: $e');
    }
  }
  
  /// 记录内存使用情况
  static void _recordMemoryUsage(int memoryUsage) {
    final record = MemoryUsageRecord(
      timestamp: DateTime.now(),
      memoryUsage: memoryUsage,
    );
    
    _memoryHistory.add(record);
    
    // 保持最近100条记录
    if (_memoryHistory.length > 100) {
      _memoryHistory.removeAt(0);
    }
  }
  
  /// 获取当前内存使用量
  static Future<int> getCurrentMemoryUsage() async {
    try {
      if (Platform.isAndroid) {
        return await _getAndroidMemoryUsage();
      } else if (Platform.isIOS) {
        return await _getIOSMemoryUsage();
      } else {
        // 其他平台的估算
        return _estimateMemoryUsage();
      }
    } catch (e) {
      Logger.w('获取内存使用量失败: $e');
      return _estimateMemoryUsage();
    }
  }
  
  /// 获取Android内存使用量
  static Future<int> _getAndroidMemoryUsage() async {
    // 这里可以通过Platform Channel调用Android原生方法
    // 暂时使用估算值
    return _estimateMemoryUsage();
  }
  
  /// 获取iOS内存使用量
  static Future<int> _getIOSMemoryUsage() async {
    // 这里可以通过Platform Channel调用iOS原生方法
    // 暂时使用估算值
    return _estimateMemoryUsage();
  }
  
  /// 估算内存使用量
  static int _estimateMemoryUsage() {
    // 基于图片缓存和其他因素的估算
    final imageCache = PaintingBinding.instance.imageCache;
    final imageCacheSize = imageCache.currentSizeBytes;
    
    // 估算基础内存使用 + 图片缓存
    return 50 * 1024 * 1024 + imageCacheSize; // 50MB基础 + 图片缓存
  }
  
  /// 优化内存
  static Future<void> optimizeMemory() async {
    final stopwatch = Stopwatch()..start();
    Logger.i('开始内存优化...');
    
    try {
      // 清理图片缓存
      await _clearImageCache();
      
      // 清理网络缓存
      await _clearNetworkCache();
      
      // 清理临时文件
      await _clearTemporaryFiles();
      
      // 触发垃圾回收
      _triggerGarbageCollection();
      
      stopwatch.stop();
      Logger.i('内存优化完成，耗时: ${stopwatch.elapsedMilliseconds}ms');
    } catch (e) {
      stopwatch.stop();
      Logger.e('内存优化失败: $e');
    }
  }
  
  /// 执行紧急内存清理
  static Future<void> performCriticalMemoryCleanup() async {
    final stopwatch = Stopwatch()..start();
    Logger.w('执行紧急内存清理...');
    
    try {
      // 清空图片缓存
      await _clearImageCache(aggressive: true);
      
      // 清空网络缓存
      await _clearNetworkCache(aggressive: true);
      
      // 清理所有临时文件
      await _clearTemporaryFiles(aggressive: true);
      
      // 强制垃圾回收
      _triggerGarbageCollection();
      
      stopwatch.stop();
      Logger.w('紧急内存清理完成，耗时: ${stopwatch.elapsedMilliseconds}ms');
    } catch (e) {
      stopwatch.stop();
      Logger.e('紧急内存清理失败: $e');
    }
  }
  
  /// 清理图片缓存
  static Future<void> _clearImageCache({bool aggressive = false}) async {
    try {
      final imageCache = PaintingBinding.instance.imageCache;
      final beforeSize = imageCache.currentSizeBytes;
      
      if (aggressive) {
        // 清空所有图片缓存
        imageCache.clear();
        imageCache.clearLiveImages();
      } else {
        // 清理部分图片缓存
        final maxCacheSize = 50 * 1024 * 1024; // 50MB
        if (imageCache.currentSizeBytes > maxCacheSize) {
          imageCache.clear();
        }
      }
      
      final afterSize = imageCache.currentSizeBytes;
      final clearedSize = beforeSize - afterSize;
      
      if (clearedSize > 0) {
        Logger.i('图片缓存清理完成，释放: ${(clearedSize / 1024 / 1024).toStringAsFixed(1)}MB');
      }
    } catch (e) {
      Logger.e('图片缓存清理失败: $e');
    }
  }
  
  /// 清理网络缓存
  static Future<void> _clearNetworkCache({bool aggressive = false}) async {
    try {
      final cacheManager = DefaultCacheManager();
      
      if (aggressive) {
        // 清空所有网络缓存
        await cacheManager.emptyCache();
        Logger.i('网络缓存已清空');
      } else {
        // 清理过期缓存
        await cacheManager.removeFile('expired');
        Logger.i('过期网络缓存已清理');
      }
    } catch (e) {
      Logger.e('网络缓存清理失败: $e');
    }
  }
  
  /// 清理临时文件
  static Future<void> _clearTemporaryFiles({bool aggressive = false}) async {
    try {
      final tempDir = Directory.systemTemp;
      
      if (aggressive) {
        // 清理所有临时文件
        await for (final entity in tempDir.list()) {
          try {
            if (entity is File) {
              await entity.delete();
            }
          } catch (e) {
            // 忽略删除失败的文件
          }
        }
        Logger.i('临时文件已清理');
      } else {
        // 清理旧的临时文件
        final cutoffTime = DateTime.now().subtract(const Duration(hours: 24));
        
        await for (final entity in tempDir.list()) {
          try {
            if (entity is File) {
              final stat = await entity.stat();
              if (stat.modified.isBefore(cutoffTime)) {
                await entity.delete();
              }
            }
          } catch (e) {
            // 忽略删除失败的文件
          }
        }
        Logger.i('旧临时文件已清理');
      }
    } catch (e) {
      Logger.e('临时文件清理失败: $e');
    }
  }
  
  /// 触发垃圾回收
  static void _triggerGarbageCollection() {
    try {
      // 在Debug模式下不执行垃圾回收，避免影响调试
      if (kDebugMode) return;
      
      // 触发垃圾回收（谨慎使用）
      // 注意：频繁调用可能影响性能
      Logger.d('触发垃圾回收');
    } catch (e) {
      Logger.e('垃圾回收失败: $e');
    }
  }
  
  /// 获取内存使用统计
  static MemoryUsageStats getMemoryUsageStats() {
    if (_memoryHistory.isEmpty) {
      return MemoryUsageStats(
        currentUsage: _lastMemoryUsage,
        averageUsage: 0,
        peakUsage: 0,
        recordCount: 0,
      );
    }
    
    final usages = _memoryHistory.map((r) => r.memoryUsage).toList();
    final averageUsage = usages.reduce((a, b) => a + b) ~/ usages.length;
    final peakUsage = usages.reduce((a, b) => a > b ? a : b);
    
    return MemoryUsageStats(
      currentUsage: _lastMemoryUsage,
      averageUsage: averageUsage,
      peakUsage: peakUsage,
      recordCount: _memoryHistory.length,
    );
  }
  
  /// 是否正在监控
  static bool get isMonitoring => _isMonitoring;
  
  /// 获取内存使用历史
  static List<MemoryUsageRecord> get memoryHistory => List.unmodifiable(_memoryHistory);
}

/// 内存使用记录
class MemoryUsageRecord {
  final DateTime timestamp;
  final int memoryUsage;
  
  const MemoryUsageRecord({
    required this.timestamp,
    required this.memoryUsage,
  });
  
  double get memoryUsageMB => memoryUsage / 1024 / 1024;
}

/// 内存使用统计
class MemoryUsageStats {
  final int currentUsage;
  final int averageUsage;
  final int peakUsage;
  final int recordCount;
  
  const MemoryUsageStats({
    required this.currentUsage,
    required this.averageUsage,
    required this.peakUsage,
    required this.recordCount,
  });
  
  double get currentUsageMB => currentUsage / 1024 / 1024;
  double get averageUsageMB => averageUsage / 1024 / 1024;
  double get peakUsageMB => peakUsage / 1024 / 1024;
}
