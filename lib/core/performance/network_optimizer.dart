/// 网络性能优化器
/// 
/// 提供网络请求优化功能，包括连接池配置、缓存策略、重试机制等
library;

import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:dio_cache_interceptor/dio_cache_interceptor.dart';
import 'package:dio_retry_interceptor/dio_retry_interceptor.dart';
import 'package:enterprise_flutter/core/logging/logger.dart';

/// 网络优化器
class NetworkOptimizer {
  static const int _maxConnectionsPerHost = 5;
  static const Duration _connectionTimeout = Duration(seconds: 10);
  static const Duration _idleTimeout = Duration(seconds: 30);
  static const Duration _requestTimeout = Duration(seconds: 30);
  
  /// 配置优化的Dio实例
  static void configureOptimizedDio(Dio dio) {
    Logger.i('配置网络优化...');
    
    // 基础配置
    dio.options.connectTimeout = _connectionTimeout;
    dio.options.receiveTimeout = _requestTimeout;
    dio.options.sendTimeout = _requestTimeout;
    
    // 配置HTTP客户端
    _configureHttpClient(dio);
    
    // 添加缓存拦截器
    _addCacheInterceptor(dio);
    
    // 添加重试拦截器
    _addRetryInterceptor(dio);
    
    // 添加压缩拦截器
    _addCompressionInterceptor(dio);
    
    // 添加性能监控拦截器
    _addPerformanceInterceptor(dio);
    
    Logger.i('网络优化配置完成');
  }
  
  /// 配置HTTP客户端
  static void _configureHttpClient(Dio dio) {
    try {
      (dio.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate = (client) {
        // 连接池配置
        client.maxConnectionsPerHost = _maxConnectionsPerHost;
        client.connectionTimeout = _connectionTimeout;
        client.idleTimeout = _idleTimeout;
        
        // 启用HTTP/2
        client.autoUncompress = true;
        
        // 配置证书验证（生产环境）
        client.badCertificateCallback = (cert, host, port) {
          // 在生产环境中应该进行严格的证书验证
          return false;
        };
        
        return client;
      };
      
      Logger.d('HTTP客户端配置完成');
    } catch (e) {
      Logger.e('HTTP客户端配置失败: $e');
    }
  }
  
  /// 添加缓存拦截器
  static void _addCacheInterceptor(Dio dio) {
    try {
      final cacheOptions = CacheOptions(
        store: MemCacheStore(),
        policy: CachePolicy.request,
        hitCacheOnErrorExcept: [401, 403, 500, 502, 503, 504],
        maxStale: const Duration(days: 7),
        priority: CachePriority.normal,
        cipher: null,
        keyBuilder: CacheOptions.defaultCacheKeyBuilder,
        allowPostMethod: false,
      );
      
      dio.interceptors.add(CacheInterceptor(options: cacheOptions));
      Logger.d('缓存拦截器已添加');
    } catch (e) {
      Logger.e('添加缓存拦截器失败: $e');
    }
  }
  
  /// 添加重试拦截器
  static void _addRetryInterceptor(Dio dio) {
    try {
      dio.interceptors.add(
        RetryInterceptor(
          dio: dio,
          logPrint: (message) => Logger.d('重试: $message'),
          retries: 3,
          retryDelays: const [
            Duration(seconds: 1),
            Duration(seconds: 2),
            Duration(seconds: 3),
          ],
          retryEvaluator: (error, attempt) {
            // 只对特定错误进行重试
            if (error.type == DioExceptionType.connectionTimeout ||
                error.type == DioExceptionType.receiveTimeout ||
                error.type == DioExceptionType.sendTimeout) {
              return true;
            }
            
            // 对5xx服务器错误进行重试
            if (error.response?.statusCode != null) {
              final statusCode = error.response!.statusCode!;
              return statusCode >= 500 && statusCode < 600;
            }
            
            return false;
          },
        ),
      );
      
      Logger.d('重试拦截器已添加');
    } catch (e) {
      Logger.e('添加重试拦截器失败: $e');
    }
  }
  
  /// 添加压缩拦截器
  static void _addCompressionInterceptor(Dio dio) {
    try {
      dio.interceptors.add(
        InterceptorsWrapper(
          onRequest: (options, handler) {
            // 添加压缩请求头
            options.headers['Accept-Encoding'] = 'gzip, deflate, br';
            
            // 对大型请求体进行压缩
            if (options.data != null && _shouldCompressRequest(options.data)) {
              options.data = _compressRequestData(options.data);
              options.headers['Content-Encoding'] = 'gzip';
            }
            
            handler.next(options);
          },
          onResponse: (response, handler) {
            // 自动解压响应（Dio会自动处理）
            handler.next(response);
          },
        ),
      );
      
      Logger.d('压缩拦截器已添加');
    } catch (e) {
      Logger.e('添加压缩拦截器失败: $e');
    }
  }
  
  /// 添加性能监控拦截器
  static void _addPerformanceInterceptor(Dio dio) {
    try {
      dio.interceptors.add(
        InterceptorsWrapper(
          onRequest: (options, handler) {
            options.extra['start_time'] = DateTime.now().millisecondsSinceEpoch;
            handler.next(options);
          },
          onResponse: (response, handler) {
            final startTime = response.requestOptions.extra['start_time'] as int?;
            if (startTime != null) {
              final duration = DateTime.now().millisecondsSinceEpoch - startTime;
              _recordNetworkPerformance(response.requestOptions, duration, true);
            }
            handler.next(response);
          },
          onError: (error, handler) {
            final startTime = error.requestOptions.extra['start_time'] as int?;
            if (startTime != null) {
              final duration = DateTime.now().millisecondsSinceEpoch - startTime;
              _recordNetworkPerformance(error.requestOptions, duration, false);
            }
            handler.next(error);
          },
        ),
      );
      
      Logger.d('性能监控拦截器已添加');
    } catch (e) {
      Logger.e('添加性能监控拦截器失败: $e');
    }
  }
  
  /// 判断是否应该压缩请求
  static bool _shouldCompressRequest(dynamic data) {
    if (data is String) {
      return data.length > 1024; // 大于1KB的字符串
    }
    
    if (data is Map || data is List) {
      final jsonString = jsonEncode(data);
      return jsonString.length > 1024; // 大于1KB的JSON
    }
    
    return false;
  }
  
  /// 压缩请求数据
  static dynamic _compressRequestData(dynamic data) {
    try {
      String jsonString;
      
      if (data is String) {
        jsonString = data;
      } else {
        jsonString = jsonEncode(data);
      }
      
      final bytes = utf8.encode(jsonString);
      final compressed = gzip.encode(bytes);
      
      Logger.d('请求数据压缩: ${bytes.length} -> ${compressed.length} bytes');
      
      return compressed;
    } catch (e) {
      Logger.e('请求数据压缩失败: $e');
      return data;
    }
  }
  
  /// 记录网络性能
  static void _recordNetworkPerformance(
    RequestOptions options,
    int duration,
    bool success,
  ) {
    try {
      final url = options.uri.toString();
      final method = options.method;
      
      Logger.d('网络请求性能: $method $url - ${duration}ms - ${success ? '成功' : '失败'}');
      
      // 这里可以将性能数据发送到分析服务
      _sendPerformanceMetrics(method, url, duration, success);
    } catch (e) {
      Logger.e('记录网络性能失败: $e');
    }
  }
  
  /// 发送性能指标
  static void _sendPerformanceMetrics(
    String method,
    String url,
    int duration,
    bool success,
  ) {
    // 这里可以集成Firebase Performance、自定义分析服务等
    // 暂时只记录日志
    if (duration > 5000) { // 超过5秒的请求
      Logger.w('慢请求检测: $method $url - ${duration}ms');
    }
  }
}

/// 请求优化器
class RequestOptimizer {
  /// 优化请求数据
  static Map<String, dynamic> optimizeRequestData(Map<String, dynamic> data) {
    final optimized = Map<String, dynamic>.from(data);
    
    // 移除空值
    optimized.removeWhere((key, value) => value == null || value == '');
    
    // 压缩大型字符串
    for (final entry in optimized.entries.toList()) {
      if (entry.value is String && (entry.value as String).length > 1000) {
        optimized[entry.key] = _compressString(entry.value as String);
      }
    }
    
    return optimized;
  }
  
  /// 压缩字符串
  static String _compressString(String input) {
    try {
      final bytes = utf8.encode(input);
      final compressed = gzip.encode(bytes);
      return base64Encode(compressed);
    } catch (e) {
      Logger.e('字符串压缩失败: $e');
      return input;
    }
  }
  
  /// 预加载关键数据
  static Future<void> preloadCriticalData() async {
    Logger.i('开始预加载关键数据...');
    
    try {
      final futures = <Future>[];
      
      // 预加载用户配置
      futures.add(_preloadUserConfig());
      
      // 预加载应用配置
      futures.add(_preloadAppConfig());
      
      // 预加载静态数据
      futures.add(_preloadStaticData());
      
      await Future.wait(futures);
      
      Logger.i('关键数据预加载完成');
    } catch (e) {
      Logger.e('关键数据预加载失败: $e');
    }
  }
  
  /// 预加载用户配置
  static Future<void> _preloadUserConfig() async {
    try {
      // 这里实现用户配置的预加载逻辑
      Logger.d('用户配置预加载完成');
    } catch (e) {
      Logger.e('用户配置预加载失败: $e');
    }
  }
  
  /// 预加载应用配置
  static Future<void> _preloadAppConfig() async {
    try {
      // 这里实现应用配置的预加载逻辑
      Logger.d('应用配置预加载完成');
    } catch (e) {
      Logger.e('应用配置预加载失败: $e');
    }
  }
  
  /// 预加载静态数据
  static Future<void> _preloadStaticData() async {
    try {
      // 这里实现静态数据的预加载逻辑
      Logger.d('静态数据预加载完成');
    } catch (e) {
      Logger.e('静态数据预加载失败: $e');
    }
  }
}
