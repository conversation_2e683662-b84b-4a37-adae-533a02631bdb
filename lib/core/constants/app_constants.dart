/// 应用常量定义
/// 
/// 定义了应用中使用的所有常量，包括配置、路径、键值等
library;

/// 应用常量
class AppConstants {
  AppConstants._(); // 私有构造函数，防止实例化

  // 应用信息
  static const String appName = 'Flutter Enterprise App';
  static const String appVersion = '1.0.0';
  static const int buildNumber = 1;

  // 存储键
  static const String authTokenKey = 'auth_token';
  static const String refreshTokenKey = 'refresh_token';
  static const String userDataKey = 'user_data';
  static const String settingsKey = 'app_settings';
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language_code';
  static const String firstLaunchKey = 'first_launch';

  // 网络配置
  static const Duration defaultTimeout = Duration(seconds: 30);
  static const Duration connectTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const Duration sendTimeout = Duration(seconds: 30);
  static const int maxRetryAttempts = 3;

  // 缓存配置
  static const Duration cacheTimeout = Duration(hours: 1);
  static const int maxCacheSize = 100 * 1024 * 1024; // 100MB
  static const String cacheDirectory = 'app_cache';

  // 数据库配置
  static const String databaseName = 'app_database.db';
  static const int databaseVersion = 1;

  // 文件路径
  static const String documentsPath = 'documents';
  static const String imagesPath = 'images';
  static const String cachePath = 'cache';
  static const String tempPath = 'temp';

  // 分页配置
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;

  // 验证规则
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 128;
  static const int maxUsernameLength = 50;
  static const int maxEmailLength = 254;

  // 动画时长
  static const Duration shortAnimationDuration = Duration(milliseconds: 200);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 300);
  static const Duration longAnimationDuration = Duration(milliseconds: 500);

  // 防抖时长
  static const Duration debounceDelay = Duration(milliseconds: 500);
  static const Duration searchDebounceDelay = Duration(milliseconds: 300);

  // 错误重试
  static const Duration retryDelay = Duration(seconds: 1);
  static const Duration maxRetryDelay = Duration(seconds: 30);

  // 日志配置
  static const String logFileName = 'app.log';
  static const int maxLogFileSize = 10 * 1024 * 1024; // 10MB
  static const int maxLogFiles = 5;

  // 通知配置
  static const String notificationChannelId = 'default_channel';
  static const String notificationChannelName = 'Default Notifications';
  static const String notificationChannelDescription = 'Default notification channel';

  // 深度链接
  static const String deepLinkScheme = 'flutterapp';
  static const String deepLinkHost = 'app.example.com';

  // 社交登录
  static const String googleClientId = 'your-google-client-id';
  static const String facebookAppId = 'your-facebook-app-id';
  static const String appleServiceId = 'your-apple-service-id';

  // 分析统计
  static const String analyticsTrackingId = 'your-analytics-tracking-id';
  static const String crashlyticsApiKey = 'your-crashlytics-api-key';

  // 地图配置
  static const String googleMapsApiKey = 'your-google-maps-api-key';
  static const double defaultLatitude = 39.9042;
  static const double defaultLongitude = 116.4074;
  static const double defaultZoom = 15.0;

  // 支付配置
  static const String stripePublishableKey = 'your-stripe-publishable-key';
  static const String paypalClientId = 'your-paypal-client-id';

  // 文件上传
  static const int maxFileSize = 50 * 1024 * 1024; // 50MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
  static const List<String> allowedDocumentTypes = ['pdf', 'doc', 'docx', 'txt'];
  static const List<String> allowedVideoTypes = ['mp4', 'avi', 'mov', 'wmv'];

  // 安全配置
  static const int maxLoginAttempts = 5;
  static const Duration lockoutDuration = Duration(minutes: 15);
  static const Duration sessionTimeout = Duration(hours: 24);

  // 性能监控
  static const Duration performanceThreshold = Duration(milliseconds: 100);
  static const int maxMemoryUsage = 200 * 1024 * 1024; // 200MB
  static const double maxCpuUsage = 80.0; // 80%

  // 国际化
  static const String defaultLocale = 'zh_CN';
  static const List<String> supportedLocales = ['zh_CN', 'en_US', 'ja_JP', 'ko_KR'];

  // 主题配置
  static const String lightTheme = 'light';
  static const String darkTheme = 'dark';
  static const String systemTheme = 'system';

  // 字体配置
  static const String defaultFontFamily = 'Roboto';
  static const double defaultFontSize = 14.0;
  static const double smallFontSize = 12.0;
  static const double largeFontSize = 16.0;
  static const double titleFontSize = 20.0;

  // 间距配置
  static const double smallSpacing = 8.0;
  static const double mediumSpacing = 16.0;
  static const double largeSpacing = 24.0;
  static const double extraLargeSpacing = 32.0;

  // 圆角配置
  static const double smallRadius = 4.0;
  static const double mediumRadius = 8.0;
  static const double largeRadius = 12.0;
  static const double extraLargeRadius = 16.0;

  // 阴影配置
  static const double smallElevation = 2.0;
  static const double mediumElevation = 4.0;
  static const double largeElevation = 8.0;
  static const double extraLargeElevation = 16.0;

  // 透明度配置
  static const double lowOpacity = 0.3;
  static const double mediumOpacity = 0.5;
  static const double highOpacity = 0.7;
  static const double veryHighOpacity = 0.9;

  // 断点配置（响应式设计）
  static const double mobileBreakpoint = 600.0;
  static const double tabletBreakpoint = 900.0;
  static const double desktopBreakpoint = 1200.0;

  // 网格配置
  static const int mobileGridColumns = 2;
  static const int tabletGridColumns = 3;
  static const int desktopGridColumns = 4;

  // 列表配置
  static const double listItemHeight = 56.0;
  static const double listItemPadding = 16.0;
  static const double listSeparatorHeight = 1.0;

  // 按钮配置
  static const double buttonHeight = 48.0;
  static const double smallButtonHeight = 32.0;
  static const double largeButtonHeight = 56.0;
  static const double buttonBorderRadius = 8.0;

  // 输入框配置
  static const double inputHeight = 48.0;
  static const double inputBorderRadius = 8.0;
  static const double inputPadding = 16.0;

  // 卡片配置
  static const double cardElevation = 2.0;
  static const double cardBorderRadius = 8.0;
  static const double cardPadding = 16.0;

  // 对话框配置
  static const double dialogBorderRadius = 12.0;
  static const double dialogPadding = 24.0;
  static const double dialogMaxWidth = 400.0;

  // 底部导航配置
  static const double bottomNavHeight = 60.0;
  static const int maxBottomNavItems = 5;

  // 应用栏配置
  static const double appBarHeight = 56.0;
  static const double toolbarHeight = 56.0;

  // 抽屉配置
  static const double drawerWidth = 280.0;
  static const double drawerHeaderHeight = 160.0;

  // 标签页配置
  static const double tabHeight = 48.0;
  static const double tabIndicatorHeight = 2.0;

  // 滑动配置
  static const double swipeThreshold = 0.3;
  static const Duration swipeAnimationDuration = Duration(milliseconds: 300);

  // 刷新配置
  static const double refreshTriggerDistance = 80.0;
  static const Duration refreshDuration = Duration(seconds: 2);

  // 加载配置
  static const Duration loadingMinDuration = Duration(milliseconds: 500);
  static const Duration loadingMaxDuration = Duration(seconds: 30);

  // 搜索配置
  static const int minSearchLength = 2;
  static const int maxSearchResults = 50;
  static const Duration searchTimeout = Duration(seconds: 10);

  // 图片配置
  static const double defaultImageQuality = 0.8;
  static const int maxImageWidth = 1920;
  static const int maxImageHeight = 1080;
  static const int thumbnailSize = 200;

  // 视频配置
  static const Duration maxVideoDuration = Duration(minutes: 10);
  static const int maxVideoSize = 100 * 1024 * 1024; // 100MB
  static const String defaultVideoFormat = 'mp4';

  // 音频配置
  static const Duration maxAudioDuration = Duration(minutes: 5);
  static const int maxAudioSize = 10 * 1024 * 1024; // 10MB
  static const String defaultAudioFormat = 'mp3';

  // 位置配置
  static const double locationAccuracy = 100.0; // 米
  static const Duration locationTimeout = Duration(seconds: 30);
  static const Duration locationUpdateInterval = Duration(seconds: 10);

  // 蓝牙配置
  static const Duration bluetoothScanTimeout = Duration(seconds: 30);
  static const Duration bluetoothConnectTimeout = Duration(seconds: 10);

  // NFC配置
  static const Duration nfcTimeout = Duration(seconds: 10);
  static const int maxNfcDataSize = 8192; // 8KB

  // WebSocket配置
  static const Duration websocketPingInterval = Duration(seconds: 30);
  static const Duration websocketTimeout = Duration(seconds: 10);
  static const int maxWebsocketReconnectAttempts = 5;

  // 后台任务配置
  static const Duration backgroundTaskTimeout = Duration(minutes: 1);
  static const int maxBackgroundTasks = 10;

  // 测试配置
  static const Duration testTimeout = Duration(seconds: 30);
  static const int maxTestRetries = 3;
  static const String testDataPath = 'test_data';
}
