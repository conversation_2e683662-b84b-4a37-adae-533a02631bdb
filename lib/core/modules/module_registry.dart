/// 模块注册系统
/// 
/// 提供模块的注册、管理和生命周期控制
library;

import 'package:flutter/foundation.dart';
import '../config/feature_config.dart';
import '../constants/feature_constants.dart';
import '../di/injection.dart';

/// 模块接口
abstract class Module {
  /// 模块名称
  String get name;
  
  /// 模块版本
  String get version;
  
  /// 模块描述
  String get description;
  
  /// 模块依赖
  List<String> get dependencies;
  
  /// 模块是否已初始化
  bool get isInitialized;
  
  /// 初始化模块
  Future<void> initialize();
  
  /// 注册模块服务
  Future<void> registerServices();
  
  /// 注销模块服务
  Future<void> unregisterServices();
  
  /// 销毁模块
  Future<void> dispose();
}

/// 模块基础实现
abstract class ModuleBase implements Module {
  ModuleBase({
    required this.name,
    required this.version,
    required this.description,
    this.dependencies = const [],
  });

  @override
  final String name;

  @override
  final String version;

  @override
  final String description;

  @override
  final List<String> dependencies;

  bool _isInitialized = false;

  @override
  bool get isInitialized => _isInitialized;

  @override
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await onInitialize();
      _isInitialized = true;
      
      if (kDebugMode) {
        print('Module $name v$version initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Failed to initialize module $name: $e');
      }
      rethrow;
    }
  }

  @override
  Future<void> registerServices() async {
    if (!_isInitialized) {
      throw StateError('Module $name must be initialized before registering services');
    }
    
    await onRegisterServices();
  }

  @override
  Future<void> unregisterServices() async {
    await onUnregisterServices();
  }

  @override
  Future<void> dispose() async {
    try {
      await onDispose();
      _isInitialized = false;
      
      if (kDebugMode) {
        print('Module $name disposed successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Failed to dispose module $name: $e');
      }
    }
  }

  /// 子类实现的初始化逻辑
  Future<void> onInitialize();

  /// 子类实现的服务注册逻辑
  Future<void> onRegisterServices();

  /// 子类实现的服务注销逻辑
  Future<void> onUnregisterServices();

  /// 子类实现的销毁逻辑
  Future<void> onDispose();
}

/// 模块注册表
class ModuleRegistry {
  static ModuleRegistry? _instance;
  static ModuleRegistry get instance => _instance ??= ModuleRegistry._();

  ModuleRegistry._();

  final Map<String, Module> _modules = {};
  final Map<String, List<String>> _dependencyGraph = {};
  bool _isInitialized = false;

  /// 注册模块
  void registerModule(Module module) {
    if (_modules.containsKey(module.name)) {
      throw ArgumentError('Module ${module.name} is already registered');
    }

    _modules[module.name] = module;
    _dependencyGraph[module.name] = module.dependencies;

    if (kDebugMode) {
      print('Module ${module.name} registered');
    }
  }

  /// 注册多个模块
  void registerModules(List<Module> modules) {
    for (final module in modules) {
      registerModule(module);
    }
  }

  /// 获取模块
  Module? getModule(String name) {
    return _modules[name];
  }

  /// 获取所有模块
  List<Module> getAllModules() {
    return _modules.values.toList();
  }

  /// 获取已启用的模块
  List<Module> getEnabledModules() {
    final featureConfig = getIt<FeatureConfig>();
    return _modules.values
        .where((module) => featureConfig.isFeatureEnabled(module.name))
        .toList();
  }

  /// 初始化所有模块
  Future<void> initializeModules() async {
    if (_isInitialized) return;

    // 验证依赖关系
    _validateDependencies();

    // 获取初始化顺序
    final initOrder = _getInitializationOrder();

    // 按顺序初始化模块
    for (final moduleName in initOrder) {
      final module = _modules[moduleName];
      if (module != null) {
        final featureConfig = getIt<FeatureConfig>();
        
        // 只初始化启用的模块
        if (featureConfig.isFeatureEnabled(moduleName)) {
          await module.initialize();
          await module.registerServices();
        }
      }
    }

    _isInitialized = true;

    if (kDebugMode) {
      print('All modules initialized successfully');
      print('Enabled modules: ${getEnabledModules().map((m) => m.name).join(', ')}');
    }
  }

  /// 销毁所有模块
  Future<void> disposeModules() async {
    if (!_isInitialized) return;

    // 按相反顺序销毁模块
    final disposeOrder = _getInitializationOrder().reversed.toList();

    for (final moduleName in disposeOrder) {
      final module = _modules[moduleName];
      if (module != null && module.isInitialized) {
        await module.unregisterServices();
        await module.dispose();
      }
    }

    _isInitialized = false;

    if (kDebugMode) {
      print('All modules disposed');
    }
  }

  /// 刷新模块状态
  Future<void> refreshModules() async {
    if (!_isInitialized) return;

    final featureConfig = getIt<FeatureConfig>();

    for (final module in _modules.values) {
      final shouldBeEnabled = featureConfig.isFeatureEnabled(module.name);
      final isCurrentlyEnabled = module.isInitialized;

      if (shouldBeEnabled && !isCurrentlyEnabled) {
        // 启用模块
        await module.initialize();
        await module.registerServices();
      } else if (!shouldBeEnabled && isCurrentlyEnabled) {
        // 禁用模块
        await module.unregisterServices();
        await module.dispose();
      }
    }

    if (kDebugMode) {
      print('Modules refreshed');
      print('Enabled modules: ${getEnabledModules().map((m) => m.name).join(', ')}');
    }
  }

  /// 验证依赖关系
  void _validateDependencies() {
    // 检查循环依赖
    for (final moduleName in _modules.keys) {
      if (_hasCircularDependency(moduleName, [])) {
        throw StateError('Circular dependency detected for module: $moduleName');
      }
    }

    // 检查依赖的模块是否存在
    for (final entry in _dependencyGraph.entries) {
      final moduleName = entry.key;
      final dependencies = entry.value;

      for (final dependency in dependencies) {
        if (!_modules.containsKey(dependency)) {
          throw StateError('Module $moduleName depends on unknown module: $dependency');
        }
      }
    }
  }

  /// 检查循环依赖
  bool _hasCircularDependency(String moduleName, List<String> visited) {
    if (visited.contains(moduleName)) {
      return true;
    }

    final dependencies = _dependencyGraph[moduleName] ?? [];
    final newVisited = [...visited, moduleName];

    for (final dependency in dependencies) {
      if (_hasCircularDependency(dependency, newVisited)) {
        return true;
      }
    }

    return false;
  }

  /// 获取初始化顺序（拓扑排序）
  List<String> _getInitializationOrder() {
    final result = <String>[];
    final visited = <String>{};
    final visiting = <String>{};

    void visit(String moduleName) {
      if (visited.contains(moduleName)) return;
      if (visiting.contains(moduleName)) {
        throw StateError('Circular dependency detected');
      }

      visiting.add(moduleName);

      final dependencies = _dependencyGraph[moduleName] ?? [];
      for (final dependency in dependencies) {
        visit(dependency);
      }

      visiting.remove(moduleName);
      visited.add(moduleName);
      result.add(moduleName);
    }

    for (final moduleName in _modules.keys) {
      visit(moduleName);
    }

    return result;
  }

  /// 获取模块状态摘要
  Map<String, dynamic> getModuleStatusSummary() {
    final featureConfig = getIt<FeatureConfig>();
    final summary = <String, dynamic>{};

    for (final module in _modules.values) {
      summary[module.name] = {
        'version': module.version,
        'description': module.description,
        'dependencies': module.dependencies,
        'isRegistered': true,
        'isEnabled': featureConfig.isFeatureEnabled(module.name),
        'isInitialized': module.isInitialized,
      };
    }

    return summary;
  }

  /// 检查模块是否已注册
  bool isModuleRegistered(String name) {
    return _modules.containsKey(name);
  }

  /// 检查模块是否已启用
  bool isModuleEnabled(String name) {
    final featureConfig = getIt<FeatureConfig>();
    return featureConfig.isFeatureEnabled(name);
  }

  /// 检查模块是否已初始化
  bool isModuleInitialized(String name) {
    final module = _modules[name];
    return module?.isInitialized ?? false;
  }

  /// 清理注册表
  Future<void> clear() async {
    await disposeModules();
    _modules.clear();
    _dependencyGraph.clear();
    _isInitialized = false;
  }
}
