import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';
import '../config/environment_config.dart';
import 'interceptors/auth_interceptor.dart';
import 'interceptors/retry_interceptor.dart';
import 'interceptors/error_interceptor.dart';
import 'interceptors/cache_interceptor.dart';

/// HTTP客户端工厂
/// 
/// **功能依赖**: 需要启用 network 模块
/// **配置项**: FEATURE_NETWORK
@module
abstract class DioClientModule {
  /// 提供标准Dio客户端
  @singleton
  Dio provideDio(
    EnvironmentConfig config,
    AuthInterceptor authInterceptor,
    RetryInterceptor retryInterceptor,
    ErrorInterceptor errorInterceptor,
    CacheInterceptor cacheInterceptor,
  ) {
    final dio = Dio(BaseOptions(
      baseUrl: config.apiBaseUrl,
      connectTimeout: Duration(seconds: config.apiTimeout),
      receiveTimeout: Duration(seconds: config.apiTimeout),
      sendTimeout: Duration(seconds: config.apiTimeout),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'FlutterApp/${config.getString('app.version', '1.0.0')}',
      },
      validateStatus: (status) {
        // 允许所有状态码，让拦截器处理错误
        return status != null && status < 500;
      },
    ));

    // 添加拦截器（顺序很重要）
    dio.interceptors.addAll([
      // 缓存拦截器（最先执行）
      cacheInterceptor,
      
      // 认证拦截器
      authInterceptor,
      
      // 重试拦截器
      retryInterceptor,
      
      // 错误处理拦截器
      errorInterceptor,
      
      // 日志拦截器（最后执行）
      if (config.enableApiLogging)
        PrettyDioLogger(
          requestHeader: true,
          requestBody: true,
          responseBody: true,
          responseHeader: false,
          error: true,
          compact: true,
          maxWidth: 90,
        ),
    ]);

    return dio;
  }

  /// 提供上传专用Dio客户端
  @singleton
  @Named('upload')
  Dio provideUploadDio(
    EnvironmentConfig config,
    AuthInterceptor authInterceptor,
  ) {
    final dio = Dio(BaseOptions(
      baseUrl: config.apiBaseUrl,
      connectTimeout: const Duration(minutes: 5),
      receiveTimeout: const Duration(minutes: 5),
      sendTimeout: const Duration(minutes: 5),
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'FlutterApp/${config.getString('app.version', '1.0.0')}',
      },
    ));

    dio.interceptors.addAll([
      authInterceptor,
      if (config.enableApiLogging)
        PrettyDioLogger(
          requestHeader: true,
          requestBody: false, // 不记录上传文件内容
          responseBody: true,
          responseHeader: false,
          error: true,
          compact: true,
        ),
    ]);

    return dio;
  }

  /// 提供下载专用Dio客户端
  @singleton
  @Named('download')
  Dio provideDownloadDio(
    EnvironmentConfig config,
    AuthInterceptor authInterceptor,
  ) {
    final dio = Dio(BaseOptions(
      baseUrl: config.apiBaseUrl,
      connectTimeout: const Duration(minutes: 2),
      receiveTimeout: const Duration(minutes: 10),
      sendTimeout: const Duration(minutes: 2),
      headers: {
        'Accept': '*/*',
        'User-Agent': 'FlutterApp/${config.getString('app.version', '1.0.0')}',
      },
    ));

    dio.interceptors.addAll([
      authInterceptor,
      if (config.enableApiLogging)
        PrettyDioLogger(
          requestHeader: true,
          requestBody: false,
          responseBody: false, // 不记录下载文件内容
          responseHeader: false,
          error: true,
          compact: true,
        ),
    ]);

    return dio;
  }
}

/// Dio客户端包装器
/// 
/// 提供统一的HTTP客户端接口
@injectable
class DioClient {
  DioClient(
    this._dio,
    @Named('upload') this._uploadDio,
    @Named('download') this._downloadDio,
  );

  final Dio _dio;
  final Dio _uploadDio;
  final Dio _downloadDio;

  /// 标准HTTP客户端
  Dio get dio => _dio;

  /// 上传专用HTTP客户端
  Dio get uploadDio => _uploadDio;

  /// 下载专用HTTP客户端
  Dio get downloadDio => _downloadDio;

  /// 获取基础URL
  String get baseUrl => _dio.options.baseUrl;

  /// 设置基础URL
  void setBaseUrl(String baseUrl) {
    _dio.options.baseUrl = baseUrl;
    _uploadDio.options.baseUrl = baseUrl;
    _downloadDio.options.baseUrl = baseUrl;
  }

  /// 添加通用请求头
  void addHeader(String key, String value) {
    _dio.options.headers[key] = value;
    _uploadDio.options.headers[key] = value;
    _downloadDio.options.headers[key] = value;
  }

  /// 移除请求头
  void removeHeader(String key) {
    _dio.options.headers.remove(key);
    _uploadDio.options.headers.remove(key);
    _downloadDio.options.headers.remove(key);
  }

  /// 清除所有自定义请求头
  void clearHeaders() {
    _dio.options.headers.clear();
    _uploadDio.options.headers.clear();
    _downloadDio.options.headers.clear();
    
    // 重新设置默认请求头
    final defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
    
    _dio.options.headers.addAll(defaultHeaders);
    _uploadDio.options.headers['Accept'] = 'application/json';
    _downloadDio.options.headers['Accept'] = '*/*';
  }

  /// 设置超时时间
  void setTimeout({
    Duration? connectTimeout,
    Duration? receiveTimeout,
    Duration? sendTimeout,
  }) {
    if (connectTimeout != null) {
      _dio.options.connectTimeout = connectTimeout;
      _uploadDio.options.connectTimeout = connectTimeout;
      _downloadDio.options.connectTimeout = connectTimeout;
    }
    
    if (receiveTimeout != null) {
      _dio.options.receiveTimeout = receiveTimeout;
      _uploadDio.options.receiveTimeout = receiveTimeout;
      _downloadDio.options.receiveTimeout = receiveTimeout;
    }
    
    if (sendTimeout != null) {
      _dio.options.sendTimeout = sendTimeout;
      _uploadDio.options.sendTimeout = sendTimeout;
      _downloadDio.options.sendTimeout = sendTimeout;
    }
  }

  /// 关闭所有客户端
  void close({bool force = false}) {
    _dio.close(force: force);
    _uploadDio.close(force: force);
    _downloadDio.close(force: force);
  }
}
