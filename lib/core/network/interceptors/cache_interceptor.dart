import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:hive/hive.dart';
import 'dart:convert';
import 'dart:typed_data';

/// 缓存拦截器
/// 
/// 提供HTTP请求缓存功能，支持多种缓存策略
/// **功能依赖**: 需要启用 network 模块
/// **配置项**: FEATURE_NETWORK
@injectable
class CacheInterceptor extends Interceptor {
  CacheInterceptor(this._cacheBox);

  final Box _cacheBox;
  
  static const String _cacheKeyPrefix = 'http_cache_';
  static const Duration _defaultCacheDuration = Duration(minutes: 5);

  @override
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    // 检查是否启用缓存
    final cacheConfig = _getCacheConfig(options);
    if (cacheConfig == null || !cacheConfig.enabled) {
      handler.next(options);
      return;
    }

    // 只缓存GET请求
    if (options.method.toUpperCase() != 'GET') {
      handler.next(options);
      return;
    }

    try {
      final cacheKey = _generateCacheKey(options);
      final cachedResponse = await _getCachedResponse(cacheKey, cacheConfig);
      
      if (cachedResponse != null) {
        // 返回缓存的响应
        handler.resolve(cachedResponse);
        return;
      }
    } catch (e) {
      // 缓存读取失败，继续正常请求
      print('Cache read error: $e');
    }

    handler.next(options);
  }

  @override
  void onResponse(
    Response response,
    ResponseInterceptorHandler handler,
  ) async {
    // 检查是否需要缓存响应
    final cacheConfig = _getCacheConfig(response.requestOptions);
    if (cacheConfig != null && 
        cacheConfig.enabled && 
        response.requestOptions.method.toUpperCase() == 'GET' &&
        response.statusCode == 200) {
      
      try {
        final cacheKey = _generateCacheKey(response.requestOptions);
        await _cacheResponse(cacheKey, response, cacheConfig);
      } catch (e) {
        // 缓存写入失败，不影响正常响应
        print('Cache write error: $e');
      }
    }

    handler.next(response);
  }

  /// 获取缓存配置
  CacheConfig? _getCacheConfig(RequestOptions options) {
    final cacheOptions = options.extra['cache'] as Map<String, dynamic>?;
    if (cacheOptions == null) return null;

    return CacheConfig(
      enabled: cacheOptions['enabled'] ?? false,
      duration: Duration(
        milliseconds: cacheOptions['duration'] ?? _defaultCacheDuration.inMilliseconds,
      ),
      strategy: CacheStrategy.values.firstWhere(
        (s) => s.name == cacheOptions['strategy'],
        orElse: () => CacheStrategy.cacheFirst,
      ),
      forceRefresh: cacheOptions['force_refresh'] ?? false,
    );
  }

  /// 生成缓存键
  String _generateCacheKey(RequestOptions options) {
    final uri = options.uri.toString();
    final headers = options.headers.entries
        .where((e) => e.key.toLowerCase() != 'authorization')
        .map((e) => '${e.key}:${e.value}')
        .join(',');
    
    final keyData = '$uri|$headers';
    return '$_cacheKeyPrefix${keyData.hashCode}';
  }

  /// 获取缓存的响应
  Future<Response?> _getCachedResponse(String cacheKey, CacheConfig config) async {
    if (config.forceRefresh) return null;

    final cachedData = _cacheBox.get(cacheKey) as Map<dynamic, dynamic>?;
    if (cachedData == null) return null;

    final cachedAt = DateTime.parse(cachedData['cached_at'] as String);
    final now = DateTime.now();

    // 检查缓存是否过期
    if (now.difference(cachedAt) > config.duration) {
      await _cacheBox.delete(cacheKey);
      return null;
    }

    // 重建Response对象
    final responseData = cachedData['response'] as Map<dynamic, dynamic>;
    return Response(
      data: responseData['data'],
      statusCode: responseData['status_code'] as int,
      statusMessage: responseData['status_message'] as String?,
      headers: Headers.fromMap(
        Map<String, List<String>>.from(responseData['headers'] as Map),
      ),
      requestOptions: RequestOptions(path: ''), // 占位符
      extra: {'from_cache': true},
    );
  }

  /// 缓存响应
  Future<void> _cacheResponse(
    String cacheKey,
    Response response,
    CacheConfig config,
  ) async {
    // 检查响应大小，避免缓存过大的响应
    final responseSize = _calculateResponseSize(response);
    if (responseSize > 1024 * 1024) { // 1MB限制
      return;
    }

    final cacheData = {
      'cached_at': DateTime.now().toIso8601String(),
      'response': {
        'data': response.data,
        'status_code': response.statusCode,
        'status_message': response.statusMessage,
        'headers': response.headers.map,
      },
    };

    await _cacheBox.put(cacheKey, cacheData);
  }

  /// 计算响应大小
  int _calculateResponseSize(Response response) {
    try {
      final jsonString = json.encode(response.data);
      return Uint8List.fromList(utf8.encode(jsonString)).length;
    } catch (e) {
      return 0;
    }
  }

  /// 清除所有缓存
  Future<void> clearAllCache() async {
    final keys = _cacheBox.keys
        .where((key) => key.toString().startsWith(_cacheKeyPrefix))
        .toList();
    
    for (final key in keys) {
      await _cacheBox.delete(key);
    }
  }

  /// 清除过期缓存
  Future<void> clearExpiredCache() async {
    final now = DateTime.now();
    final keysToDelete = <dynamic>[];

    for (final key in _cacheBox.keys) {
      if (!key.toString().startsWith(_cacheKeyPrefix)) continue;

      final cachedData = _cacheBox.get(key) as Map<dynamic, dynamic>?;
      if (cachedData == null) continue;

      final cachedAt = DateTime.parse(cachedData['cached_at'] as String);
      if (now.difference(cachedAt) > _defaultCacheDuration) {
        keysToDelete.add(key);
      }
    }

    for (final key in keysToDelete) {
      await _cacheBox.delete(key);
    }
  }

  /// 获取缓存统计信息
  CacheStats getCacheStats() {
    final cacheKeys = _cacheBox.keys
        .where((key) => key.toString().startsWith(_cacheKeyPrefix))
        .toList();

    int totalSize = 0;
    int expiredCount = 0;
    final now = DateTime.now();

    for (final key in cacheKeys) {
      final cachedData = _cacheBox.get(key) as Map<dynamic, dynamic>?;
      if (cachedData == null) continue;

      // 计算大小
      try {
        final jsonString = json.encode(cachedData);
        totalSize += Uint8List.fromList(utf8.encode(jsonString)).length;
      } catch (e) {
        // 忽略计算错误
      }

      // 检查是否过期
      final cachedAt = DateTime.parse(cachedData['cached_at'] as String);
      if (now.difference(cachedAt) > _defaultCacheDuration) {
        expiredCount++;
      }
    }

    return CacheStats(
      totalEntries: cacheKeys.length,
      totalSize: totalSize,
      expiredEntries: expiredCount,
    );
  }

  /// 配置请求缓存
  static void configureCache(
    RequestOptions options, {
    bool enabled = true,
    Duration? duration,
    CacheStrategy strategy = CacheStrategy.cacheFirst,
    bool forceRefresh = false,
  }) {
    options.extra['cache'] = {
      'enabled': enabled,
      'duration': duration?.inMilliseconds,
      'strategy': strategy.name,
      'force_refresh': forceRefresh,
    };
  }

  /// 禁用请求缓存
  static void disableCache(RequestOptions options) {
    options.extra['cache'] = {'enabled': false};
  }
}

/// 缓存配置
class CacheConfig {
  const CacheConfig({
    required this.enabled,
    required this.duration,
    required this.strategy,
    required this.forceRefresh,
  });

  final bool enabled;
  final Duration duration;
  final CacheStrategy strategy;
  final bool forceRefresh;
}

/// 缓存策略
enum CacheStrategy {
  /// 缓存优先：先检查缓存，缓存不存在或过期时请求网络
  cacheFirst,
  
  /// 网络优先：先请求网络，失败时使用缓存
  networkFirst,
  
  /// 仅缓存：只使用缓存，不请求网络
  cacheOnly,
  
  /// 仅网络：只请求网络，不使用缓存
  networkOnly,
}

/// 缓存统计信息
class CacheStats {
  const CacheStats({
    required this.totalEntries,
    required this.totalSize,
    required this.expiredEntries,
  });

  final int totalEntries;
  final int totalSize;
  final int expiredEntries;

  @override
  String toString() {
    return 'CacheStats(entries: $totalEntries, size: ${(totalSize / 1024).toStringAsFixed(2)}KB, expired: $expiredEntries)';
  }
}
