import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

/// 认证拦截器
/// 
/// 自动添加认证头和处理token刷新
/// **功能依赖**: 需要启用 network 模块
/// **配置项**: FEATURE_NETWORK
@injectable
class AuthInterceptor extends Interceptor {
  AuthInterceptor(this._secureStorage);

  final FlutterSecureStorage _secureStorage;
  
  static const String _tokenKey = 'auth_access_token';
  static const String _refreshTokenKey = 'auth_refresh_token';

  @override
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    try {
      // 跳过不需要认证的请求
      if (_shouldSkipAuth(options)) {
        handler.next(options);
        return;
      }

      final token = await _secureStorage.read(key: _tokenKey);
      if (token != null) {
        options.headers['Authorization'] = 'Bearer $token';
      }

      handler.next(options);
    } catch (e) {
      handler.reject(
        DioException(
          requestOptions: options,
          error: 'Failed to add auth token: $e',
          type: DioExceptionType.unknown,
        ),
      );
    }
  }

  @override
  void onError(
    DioException err,
    ErrorInterceptorHandler handler,
  ) async {
    // 处理401未授权错误
    if (err.response?.statusCode == 401) {
      try {
        final refreshed = await _refreshToken();
        if (refreshed) {
          // 重试原始请求
          final retryResponse = await _retryRequest(err.requestOptions);
          handler.resolve(retryResponse);
          return;
        }
      } catch (e) {
        // 刷新token失败，清除所有认证信息
        await _clearAuthData();
      }
    }

    handler.next(err);
  }

  /// 检查是否应该跳过认证
  bool _shouldSkipAuth(RequestOptions options) {
    // 检查请求头中是否明确指定跳过认证
    if (options.headers.containsKey('Skip-Auth') && 
        options.headers['Skip-Auth'] == true) {
      return true;
    }

    // 跳过认证的路径
    final skipAuthPaths = [
      '/auth/login',
      '/auth/register',
      '/auth/refresh',
      '/auth/forgot-password',
      '/auth/reset-password',
      '/auth/verify-email',
      '/public/',
      '/health',
      '/version',
    ];

    return skipAuthPaths.any((path) => options.path.contains(path));
  }

  /// 刷新访问令牌
  Future<bool> _refreshToken() async {
    try {
      final refreshToken = await _secureStorage.read(key: _refreshTokenKey);
      if (refreshToken == null) return false;

      final dio = Dio();
      final response = await dio.post(
        '/auth/refresh',
        data: {'refresh_token': refreshToken},
      );

      if (response.statusCode == 200) {
        final data = response.data;
        final newAccessToken = data['access_token'] as String?;
        final newRefreshToken = data['refresh_token'] as String?;

        if (newAccessToken != null) {
          await _secureStorage.write(key: _tokenKey, value: newAccessToken);
        }
        
        if (newRefreshToken != null) {
          await _secureStorage.write(key: _refreshTokenKey, value: newRefreshToken);
        }

        return true;
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  /// 重试原始请求
  Future<Response> _retryRequest(RequestOptions options) async {
    final token = await _secureStorage.read(key: _tokenKey);
    if (token != null) {
      options.headers['Authorization'] = 'Bearer $token';
    }

    final dio = Dio();
    return dio.fetch(options);
  }

  /// 清除认证数据
  Future<void> _clearAuthData() async {
    await Future.wait([
      _secureStorage.delete(key: _tokenKey),
      _secureStorage.delete(key: _refreshTokenKey),
    ]);
  }

  /// 手动设置认证令牌
  Future<void> setAuthToken(String accessToken, String refreshToken) async {
    await Future.wait([
      _secureStorage.write(key: _tokenKey, value: accessToken),
      _secureStorage.write(key: _refreshTokenKey, value: refreshToken),
    ]);
  }

  /// 手动清除认证令牌
  Future<void> clearAuthToken() async {
    await _clearAuthData();
  }

  /// 获取当前访问令牌
  Future<String?> getAccessToken() async {
    return await _secureStorage.read(key: _tokenKey);
  }

  /// 获取当前刷新令牌
  Future<String?> getRefreshToken() async {
    return await _secureStorage.read(key: _refreshTokenKey);
  }

  /// 检查是否有有效的认证令牌
  Future<bool> hasValidToken() async {
    final token = await _secureStorage.read(key: _tokenKey);
    return token != null && token.isNotEmpty;
  }
}
