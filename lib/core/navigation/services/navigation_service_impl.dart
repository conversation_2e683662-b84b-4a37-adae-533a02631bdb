import 'dart:async';
import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import '../entities/app_route.dart';
import 'navigation_service.dart';

/// 导航服务实现
/// 
/// 提供完整的导航管理功能，包括路由注册、权限检查、重定向处理等
@Injectable(as: INavigationService)
class NavigationServiceImpl implements INavigationService {
  final GlobalKey<NavigatorState> _navigatorKey = GlobalKey<NavigatorState>();
  final RouteObserver<PageRoute<dynamic>> _routeObserver = RouteObserver<PageRoute<dynamic>>();
  
  // 路由配置
  final List<AppRoute> _routes = [];
  final Map<String, AppRoute> _routeMap = {};
  
  // 路由历史
  final List<String> _routeHistory = [];
  
  // 监听器
  final List<VoidCallback> _routeListeners = [];
  final StreamController<String> _routeController = StreamController.broadcast();
  
  @override
  GlobalKey<NavigatorState> get navigatorKey => _navigatorKey;

  @override
  BuildContext? get currentContext => _navigatorKey.currentContext;

  @override
  RouteObserver<PageRoute<dynamic>> get routeObserver => _routeObserver;

  @override
  Stream<String> get routeStream => _routeController.stream;

  @override
  void registerRoutes(List<AppRoute> routes) {
    _routes.clear();
    _routeMap.clear();
    
    for (final route in routes) {
      _registerRoute(route);
    }
  }

  void _registerRoute(AppRoute route) {
    _routes.add(route);
    _routeMap[route.name] = route;
    
    // 递归注册子路由
    if (route.hasChildren) {
      for (final child in route.children!) {
        _registerRoute(child.copyWith(parent: route.fullPath));
      }
    }
  }

  @override
  List<AppRoute> getAllRoutes() => List.unmodifiable(_routes);

  @override
  AppRoute? findRoute(String name) => _routeMap[name];

  @override
  AppRoute? findRouteByPath(String path) {
    for (final route in _routes) {
      if (route.matchesPath(path)) {
        return route;
      }
    }
    return null;
  }

  @override
  bool hasRoute(String name) => _routeMap.containsKey(name);

  @override
  Future<T?> navigateTo<T extends Object?>(
    String routeName, {
    Map<String, dynamic>? params,
    Object? arguments,
    bool replace = false,
    bool clearStack = false,
  }) async {
    final route = findRoute(routeName);
    if (route == null) {
      throw Exception('Route not found: $routeName');
    }

    if (!route.enabled) {
      throw Exception('Route is disabled: $routeName');
    }

    final effectiveParams = params ?? <String, dynamic>{};

    // 验证参数
    if (!validateRouteParams(route, effectiveParams)) {
      throw Exception('Invalid route parameters for: $routeName');
    }

    // 检查权限
    if (!await checkRoutePermission(route, effectiveParams)) {
      throw Exception('Access denied to route: $routeName');
    }

    // 处理重定向
    final redirectTo = await handleRouteRedirect(route, effectiveParams);
    if (redirectTo != null) {
      return navigateTo<T>(redirectTo, params: effectiveParams, arguments: arguments);
    }

    // 执行导航
    final navigator = _navigatorKey.currentState;
    if (navigator == null) {
      throw Exception('Navigator not available');
    }

    _addToHistory(routeName);
    _notifyRouteChange(routeName);

    if (clearStack) {
      return navigator.pushNamedAndRemoveUntil(
        routeName,
        (route) => false,
        arguments: arguments,
      );
    } else if (replace) {
      return navigator.pushReplacementNamed(
        routeName,
        arguments: arguments,
      );
    } else {
      return navigator.pushNamed(
        routeName,
        arguments: arguments,
      );
    }
  }

  @override
  Future<T?> navigateToPath<T extends Object?>(
    String path, {
    Object? arguments,
    bool replace = false,
    bool clearStack = false,
  }) async {
    final route = findRouteByPath(path);
    if (route == null) {
      throw Exception('Route not found for path: $path');
    }

    final params = route.extractParams(path);
    return navigateTo<T>(
      route.name,
      params: params,
      arguments: arguments,
      replace: replace,
      clearStack: clearStack,
    );
  }

  @override
  void goBack<T extends Object?>([T? result]) {
    final navigator = _navigatorKey.currentState;
    if (navigator != null && navigator.canPop()) {
      navigator.pop(result);
      _removeFromHistory();
    }
  }

  @override
  void goBackTo(String routeName) {
    final navigator = _navigatorKey.currentState;
    if (navigator != null) {
      navigator.popUntil((route) => route.settings.name == routeName);
      _updateHistoryAfterPopUntil(routeName);
    }
  }

  @override
  void goBackToRoot() {
    final navigator = _navigatorKey.currentState;
    if (navigator != null) {
      navigator.popUntil((route) => route.isFirst);
      _routeHistory.clear();
    }
  }

  @override
  bool canGoBack() {
    final navigator = _navigatorKey.currentState;
    return navigator?.canPop() ?? false;
  }

  @override
  Future<T?> replace<T extends Object?>(
    String routeName, {
    Map<String, dynamic>? params,
    Object? arguments,
  }) {
    return navigateTo<T>(
      routeName,
      params: params,
      arguments: arguments,
      replace: true,
    );
  }

  @override
  Future<T?> clearAndNavigateTo<T extends Object?>(
    String routeName, {
    Map<String, dynamic>? params,
    Object? arguments,
  }) {
    return navigateTo<T>(
      routeName,
      params: params,
      arguments: arguments,
      clearStack: true,
    );
  }

  @override
  Future<T?> pushRoute<T extends Object?>(Route<T> route) async {
    final navigator = _navigatorKey.currentState;
    if (navigator == null) {
      throw Exception('Navigator not available');
    }

    return navigator.push(route);
  }

  @override
  void popUntil(String routeName) {
    final navigator = _navigatorKey.currentState;
    if (navigator != null) {
      navigator.popUntil((route) => route.settings.name == routeName);
      _updateHistoryAfterPopUntil(routeName);
    }
  }

  @override
  void popCount(int count) {
    final navigator = _navigatorKey.currentState;
    if (navigator != null) {
      for (int i = 0; i < count && navigator.canPop(); i++) {
        navigator.pop();
        _removeFromHistory();
      }
    }
  }

  @override
  String? getCurrentRouteName() {
    final navigator = _navigatorKey.currentState;
    if (navigator == null) return null;

    String? routeName;
    navigator.popUntil((route) {
      routeName = route.settings.name;
      return true;
    });
    return routeName;
  }

  @override
  String? getCurrentRoutePath() {
    final routeName = getCurrentRouteName();
    if (routeName == null) return null;

    final route = findRoute(routeName);
    return route?.fullPath;
  }

  @override
  List<String> getRouteHistory() => List.unmodifiable(_routeHistory);

  @override
  void clearRouteHistory() {
    _routeHistory.clear();
  }

  @override
  Future<bool> checkRoutePermission(AppRoute route, Map<String, dynamic> params) async {
    switch (route.permission) {
      case RoutePermission.public:
        return true;
      
      case RoutePermission.authenticated:
        // TODO: 检查用户是否已认证
        return true; // 暂时返回true
      
      case RoutePermission.role:
        // TODO: 检查用户角色
        return true; // 暂时返回true
      
      case RoutePermission.custom:
        if (route.permissionChecker != null && currentContext != null) {
          return await route.permissionChecker!(currentContext!, params);
        }
        return false;
    }
  }

  @override
  Future<String?> handleRouteRedirect(AppRoute route, Map<String, dynamic> params) async {
    if (route.redirectChecker != null && currentContext != null) {
      return route.redirectChecker!(currentContext!, params);
    }
    return null;
  }

  @override
  bool validateRouteParams(AppRoute route, Map<String, dynamic> params) {
    if (route.paramValidator != null) {
      return route.paramValidator!(params);
    }
    return true;
  }

  @override
  Route<dynamic>? generateRoute(RouteSettings settings) {
    final routeName = settings.name;
    if (routeName == null) return null;

    final route = findRoute(routeName);
    if (route == null) return null;

    if (!route.enabled) return null;

    // 构建页面
    Widget Function(BuildContext) builder;
    if (route.builder != null) {
      final params = settings.arguments as Map<String, dynamic>? ?? {};
      builder = (context) => route.builder!(context, params);
    } else {
      // TODO: 根据 pageType 创建页面
      builder = (context) => const Scaffold(
        body: Center(child: Text('Page not implemented')),
      );
    }

    // 创建路由
    if (route.customTransitionBuilder != null) {
      return route.customTransitionBuilder!(settings, builder);
    } else {
      return RouteBuilderFactory.createRoute(
        settings: settings,
        builder: builder,
        transition: route.transition,
        transitionDuration: route.transitionDuration,
        fullscreenDialog: route.fullscreenDialog,
        maintainState: route.maintainState,
      );
    }
  }

  @override
  Route<dynamic>? handleUnknownRoute(RouteSettings settings) {
    return MaterialPageRoute(
      settings: settings,
      builder: (context) => Scaffold(
        appBar: AppBar(title: const Text('Page Not Found')),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 64),
              const SizedBox(height: 16),
              Text(
                'Page not found: ${settings.name}',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => goBackToRoot(),
                child: const Text('Go Home'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void addRouteListener(VoidCallback listener) {
    _routeListeners.add(listener);
  }

  @override
  void removeRouteListener(VoidCallback listener) {
    _routeListeners.remove(listener);
  }

  void _addToHistory(String routeName) {
    _routeHistory.add(routeName);
    // 限制历史记录长度
    if (_routeHistory.length > 50) {
      _routeHistory.removeAt(0);
    }
  }

  void _removeFromHistory() {
    if (_routeHistory.isNotEmpty) {
      _routeHistory.removeLast();
    }
  }

  void _updateHistoryAfterPopUntil(String routeName) {
    final index = _routeHistory.lastIndexOf(routeName);
    if (index >= 0) {
      _routeHistory.removeRange(index + 1, _routeHistory.length);
    }
  }

  void _notifyRouteChange(String routeName) {
    for (final listener in _routeListeners) {
      listener();
    }
    _routeController.add(routeName);
  }

  /// 清理资源
  void dispose() {
    _routeController.close();
  }
}
