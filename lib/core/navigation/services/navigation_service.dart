import 'dart:async';
import 'package:flutter/material.dart';
import '../entities/app_route.dart';

/// 导航服务接口
///
/// 提供统一的导航管理功能，支持声明式路由配置和条件路由
abstract class INavigationService {
  /// 获取全局导航器键
  GlobalKey<NavigatorState> get navigatorKey;
  
  /// 获取当前上下文
  BuildContext? get currentContext;
  
  /// 注册路由配置
  void registerRoutes(List<AppRoute> routes);
  
  /// 获取所有路由
  List<AppRoute> getAllRoutes();
  
  /// 根据名称查找路由
  AppRoute? findRoute(String name);
  
  /// 根据路径查找路由
  AppRoute? findRouteByPath(String path);
  
  /// 检查路由是否存在
  bool hasRoute(String name);
  
  /// 导航到指定路由
  Future<T?> navigateTo<T extends Object?>(
    String routeName, {
    Map<String, dynamic>? params,
    Object? arguments,
    bool replace = false,
    bool clearStack = false,
  });
  
  /// 根据路径导航
  Future<T?> navigateToPath<T extends Object?>(
    String path, {
    Object? arguments,
    bool replace = false,
    bool clearStack = false,
  });
  
  /// 返回上一页
  void goBack<T extends Object?>([T? result]);
  
  /// 返回到指定路由
  void goBackTo(String routeName);
  
  /// 返回到根路由
  void goBackToRoot();
  
  /// 检查是否可以返回
  bool canGoBack();
  
  /// 替换当前路由
  Future<T?> replace<T extends Object?>(
    String routeName, {
    Map<String, dynamic>? params,
    Object? arguments,
  });
  
  /// 清空导航栈并导航到指定路由
  Future<T?> clearAndNavigateTo<T extends Object?>(
    String routeName, {
    Map<String, dynamic>? params,
    Object? arguments,
  });
  
  /// 推送自定义路由
  Future<T?> pushRoute<T extends Object?>(Route<T> route);
  
  /// 弹出到指定路由
  void popUntil(String routeName);
  
  /// 弹出指定数量的路由
  void popCount(int count);
  
  /// 获取当前路由名称
  String? getCurrentRouteName();
  
  /// 获取当前路由路径
  String? getCurrentRoutePath();
  
  /// 获取路由历史
  List<String> getRouteHistory();
  
  /// 清除路由历史
  void clearRouteHistory();
  
  /// 检查路由权限
  Future<bool> checkRoutePermission(AppRoute route, Map<String, dynamic> params);
  
  /// 处理路由重定向
  Future<String?> handleRouteRedirect(AppRoute route, Map<String, dynamic> params);
  
  /// 验证路由参数
  bool validateRouteParams(AppRoute route, Map<String, dynamic> params);
  
  /// 生成路由
  Route<dynamic>? generateRoute(RouteSettings settings);
  
  /// 处理未知路由
  Route<dynamic>? handleUnknownRoute(RouteSettings settings);
  
  /// 路由观察器
  RouteObserver<PageRoute<dynamic>> get routeObserver;
  
  /// 添加路由监听器
  void addRouteListener(VoidCallback listener);
  
  /// 移除路由监听器
  void removeRouteListener(VoidCallback listener);
  
  /// 获取路由变更流
  Stream<String> get routeStream;
}
