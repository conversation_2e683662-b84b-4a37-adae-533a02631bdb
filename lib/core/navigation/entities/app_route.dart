import 'package:flutter/material.dart';
import 'package:equatable/equatable.dart';

/// 路由权限类型
enum RoutePermission {
  /// 公开访问
  public,
  
  /// 需要认证
  authenticated,
  
  /// 需要特定角色
  role,
  
  /// 自定义权限检查
  custom,
}

/// 路由转场动画类型
enum RouteTransition {
  /// 默认转场
  defaultTransition,
  
  /// 淡入淡出
  fade,
  
  /// 滑动
  slide,
  
  /// 缩放
  scale,
  
  /// 旋转
  rotation,
  
  /// 无动画
  none,
  
  /// 自定义动画
  custom,
}

/// 应用路由配置
/// 
/// 声明式路由配置，支持权限控制、条件路由和自定义转场动画
class AppRoute extends Equatable {
  /// 路由名称
  final String name;
  
  /// 路由路径
  final String path;
  
  /// 页面构建器
  final Widget Function(BuildContext context, Map<String, dynamic> params)? builder;
  
  /// 页面类型（用于延迟加载）
  final Type? pageType;
  
  /// 父路由路径
  final String? parent;
  
  /// 子路由列表
  final List<AppRoute>? children;
  
  /// 路由权限
  final RoutePermission permission;
  
  /// 所需角色（当permission为role时使用）
  final List<String>? requiredRoles;
  
  /// 自定义权限检查器（当permission为custom时使用）
  final Future<bool> Function(BuildContext context, Map<String, dynamic> params)? permissionChecker;
  
  /// 重定向条件检查器
  final String? Function(BuildContext context, Map<String, dynamic> params)? redirectChecker;
  
  /// 路由参数验证器
  final bool Function(Map<String, dynamic> params)? paramValidator;
  
  /// 转场动画类型
  final RouteTransition transition;
  
  /// 自定义转场动画构建器
  final Route<T> Function<T extends Object?>(
    RouteSettings settings,
    WidgetBuilder builder,
  )? customTransitionBuilder;
  
  /// 转场动画持续时间
  final Duration? transitionDuration;
  
  /// 是否全屏对话框
  final bool fullscreenDialog;
  
  /// 是否维护状态
  final bool maintainState;
  
  /// 路由元数据
  final Map<String, dynamic>? meta;
  
  /// 是否启用
  final bool enabled;

  const AppRoute({
    required this.name,
    required this.path,
    this.builder,
    this.pageType,
    this.parent,
    this.children,
    this.permission = RoutePermission.public,
    this.requiredRoles,
    this.permissionChecker,
    this.redirectChecker,
    this.paramValidator,
    this.transition = RouteTransition.defaultTransition,
    this.customTransitionBuilder,
    this.transitionDuration,
    this.fullscreenDialog = false,
    this.maintainState = true,
    this.meta,
    this.enabled = true,
  });

  /// 获取完整路径
  String get fullPath {
    if (parent == null) return path;
    return '$parent$path';
  }

  /// 是否为根路由
  bool get isRoot => parent == null;

  /// 是否有子路由
  bool get hasChildren => children != null && children!.isNotEmpty;

  /// 获取所有子路由（递归）
  List<AppRoute> getAllChildren() {
    if (!hasChildren) return [];
    
    final allChildren = <AppRoute>[];
    for (final child in children!) {
      allChildren.add(child);
      allChildren.addAll(child.getAllChildren());
    }
    return allChildren;
  }

  /// 查找子路由
  AppRoute? findChild(String name) {
    if (!hasChildren) return null;
    
    for (final child in children!) {
      if (child.name == name) return child;
      
      final found = child.findChild(name);
      if (found != null) return found;
    }
    
    return null;
  }

  /// 检查路径是否匹配
  bool matchesPath(String path) {
    return fullPath == path || _matchesPattern(fullPath, path);
  }

  /// 模式匹配（支持参数）
  bool _matchesPattern(String pattern, String path) {
    final patternSegments = pattern.split('/');
    final pathSegments = path.split('/');
    
    if (patternSegments.length != pathSegments.length) {
      return false;
    }
    
    for (int i = 0; i < patternSegments.length; i++) {
      final patternSegment = patternSegments[i];
      final pathSegment = pathSegments[i];
      
      // 参数段（以:开头）
      if (patternSegment.startsWith(':')) {
        continue;
      }
      
      // 通配符段
      if (patternSegment == '*') {
        continue;
      }
      
      // 精确匹配
      if (patternSegment != pathSegment) {
        return false;
      }
    }
    
    return true;
  }

  /// 从路径提取参数
  Map<String, String> extractParams(String path) {
    final params = <String, String>{};
    final patternSegments = fullPath.split('/');
    final pathSegments = path.split('/');
    
    if (patternSegments.length != pathSegments.length) {
      return params;
    }
    
    for (int i = 0; i < patternSegments.length; i++) {
      final patternSegment = patternSegments[i];
      final pathSegment = pathSegments[i];
      
      if (patternSegment.startsWith(':')) {
        final paramName = patternSegment.substring(1);
        params[paramName] = pathSegment;
      }
    }
    
    return params;
  }

  /// 复制并修改路由
  AppRoute copyWith({
    String? name,
    String? path,
    Widget Function(BuildContext context, Map<String, dynamic> params)? builder,
    Type? pageType,
    String? parent,
    List<AppRoute>? children,
    RoutePermission? permission,
    List<String>? requiredRoles,
    Future<bool> Function(BuildContext context, Map<String, dynamic> params)? permissionChecker,
    String? Function(BuildContext context, Map<String, dynamic> params)? redirectChecker,
    bool Function(Map<String, dynamic> params)? paramValidator,
    RouteTransition? transition,
    Route<T> Function<T extends Object?>(RouteSettings settings, WidgetBuilder builder)? customTransitionBuilder,
    Duration? transitionDuration,
    bool? fullscreenDialog,
    bool? maintainState,
    Map<String, dynamic>? meta,
    bool? enabled,
  }) {
    return AppRoute(
      name: name ?? this.name,
      path: path ?? this.path,
      builder: builder ?? this.builder,
      pageType: pageType ?? this.pageType,
      parent: parent ?? this.parent,
      children: children ?? this.children,
      permission: permission ?? this.permission,
      requiredRoles: requiredRoles ?? this.requiredRoles,
      permissionChecker: permissionChecker ?? this.permissionChecker,
      redirectChecker: redirectChecker ?? this.redirectChecker,
      paramValidator: paramValidator ?? this.paramValidator,
      transition: transition ?? this.transition,
      customTransitionBuilder: customTransitionBuilder ?? this.customTransitionBuilder,
      transitionDuration: transitionDuration ?? this.transitionDuration,
      fullscreenDialog: fullscreenDialog ?? this.fullscreenDialog,
      maintainState: maintainState ?? this.maintainState,
      meta: meta ?? this.meta,
      enabled: enabled ?? this.enabled,
    );
  }

  @override
  List<Object?> get props => [
        name,
        path,
        parent,
        permission,
        requiredRoles,
        transition,
        transitionDuration,
        fullscreenDialog,
        maintainState,
        meta,
        enabled,
      ];

  @override
  String toString() {
    return 'AppRoute(name: $name, path: $fullPath, permission: $permission)';
  }
}

/// 路由构建器工厂
class RouteBuilderFactory {
  RouteBuilderFactory._();

  /// 创建页面路由
  static Route<T> createRoute<T extends Object?>({
    required RouteSettings settings,
    required WidgetBuilder builder,
    RouteTransition transition = RouteTransition.defaultTransition,
    Duration? transitionDuration,
    bool fullscreenDialog = false,
    bool maintainState = true,
  }) {
    switch (transition) {
      case RouteTransition.defaultTransition:
        return MaterialPageRoute<T>(
          settings: settings,
          builder: builder,
          fullscreenDialog: fullscreenDialog,
          maintainState: maintainState,
        );
      
      case RouteTransition.fade:
        return PageRouteBuilder<T>(
          settings: settings,
          pageBuilder: (context, animation, _) => builder(context),
          transitionDuration: transitionDuration ?? const Duration(milliseconds: 300),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(opacity: animation, child: child);
          },
          fullscreenDialog: fullscreenDialog,
          maintainState: maintainState,
        );
      
      case RouteTransition.slide:
        return PageRouteBuilder<T>(
          settings: settings,
          pageBuilder: (context, animation, _) => builder(context),
          transitionDuration: transitionDuration ?? const Duration(milliseconds: 300),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = Offset(1.0, 0.0);
            const end = Offset.zero;
            const curve = Curves.ease;
            
            final tween = Tween(begin: begin, end: end);
            final curvedAnimation = CurvedAnimation(parent: animation, curve: curve);
            
            return SlideTransition(
              position: tween.animate(curvedAnimation),
              child: child,
            );
          },
          fullscreenDialog: fullscreenDialog,
          maintainState: maintainState,
        );
      
      case RouteTransition.scale:
        return PageRouteBuilder<T>(
          settings: settings,
          pageBuilder: (context, animation, _) => builder(context),
          transitionDuration: transitionDuration ?? const Duration(milliseconds: 300),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return ScaleTransition(scale: animation, child: child);
          },
          fullscreenDialog: fullscreenDialog,
          maintainState: maintainState,
        );
      
      case RouteTransition.rotation:
        return PageRouteBuilder<T>(
          settings: settings,
          pageBuilder: (context, animation, _) => builder(context),
          transitionDuration: transitionDuration ?? const Duration(milliseconds: 300),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return RotationTransition(turns: animation, child: child);
          },
          fullscreenDialog: fullscreenDialog,
          maintainState: maintainState,
        );
      
      case RouteTransition.none:
        return PageRouteBuilder<T>(
          settings: settings,
          pageBuilder: (context, animation, _) => builder(context),
          transitionDuration: Duration.zero,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return child;
          },
          fullscreenDialog: fullscreenDialog,
          maintainState: maintainState,
        );
      
      case RouteTransition.custom:
        // 自定义转场动画应该通过 customTransitionBuilder 处理
        return MaterialPageRoute<T>(
          settings: settings,
          builder: builder,
          fullscreenDialog: fullscreenDialog,
          maintainState: maintainState,
        );
    }
  }
}
