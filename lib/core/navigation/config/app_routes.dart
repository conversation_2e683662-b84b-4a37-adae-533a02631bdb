import 'package:flutter/material.dart';
import '../entities/app_route.dart';

/// 应用路由配置
/// 
/// 集中管理所有应用路由，支持模块化路由配置
class AppRoutes {
  AppRoutes._();

  // ============================================================================
  // 路由名称常量
  // ============================================================================
  
  /// 首页
  static const String home = '/';
  
  /// 登录页
  static const String login = '/login';
  
  /// 注册页
  static const String register = '/register';
  
  /// 忘记密码页
  static const String forgotPassword = '/forgot-password';
  
  /// 重置密码页
  static const String resetPassword = '/reset-password';
  
  /// 个人资料页
  static const String profile = '/profile';
  
  /// 设置页
  static const String settings = '/settings';
  
  /// 主题设置页
  static const String themeSettings = '/settings/theme';
  
  /// 语言设置页
  static const String languageSettings = '/settings/language';
  
  /// 关于页
  static const String about = '/about';
  
  /// 帮助页
  static const String help = '/help';
  
  /// 错误页
  static const String error = '/error';
  
  /// 未找到页面
  static const String notFound = '/404';

  // ============================================================================
  // 路由配置
  // ============================================================================
  
  /// 获取所有路由配置
  static List<AppRoute> getRoutes() {
    return [
      // 主要路由
      _homeRoute(),
      _authRoutes(),
      _profileRoute(),
      _settingsRoutes(),
      _infoRoutes(),
      _errorRoutes(),
    ].expand((route) => route is List ? route : [route]).cast<AppRoute>().toList();
  }

  /// 首页路由
  static AppRoute _homeRoute() {
    return AppRoute(
      name: 'home',
      path: home,
      builder: (context, params) => _buildHomePage(context),
      permission: RoutePermission.public,
      transition: RouteTransition.fade,
      meta: {
        'title': 'Home',
        'showInNavigation': true,
        'icon': Icons.home,
      },
    );
  }

  /// 认证相关路由
  static List<AppRoute> _authRoutes() {
    return [
      AppRoute(
        name: 'login',
        path: login,
        builder: (context, params) => _buildLoginPage(context),
        permission: RoutePermission.public,
        transition: RouteTransition.slide,
        redirectChecker: (context, params) {
          // TODO: 如果已登录，重定向到首页
          return null;
        },
        meta: {
          'title': 'Login',
          'hideNavigation': true,
        },
      ),
      AppRoute(
        name: 'register',
        path: register,
        builder: (context, params) => _buildRegisterPage(context),
        permission: RoutePermission.public,
        transition: RouteTransition.slide,
        meta: {
          'title': 'Register',
          'hideNavigation': true,
        },
      ),
      AppRoute(
        name: 'forgot_password',
        path: forgotPassword,
        builder: (context, params) => _buildForgotPasswordPage(context),
        permission: RoutePermission.public,
        transition: RouteTransition.fade,
        meta: {
          'title': 'Forgot Password',
          'hideNavigation': true,
        },
      ),
      AppRoute(
        name: 'reset_password',
        path: resetPassword,
        builder: (context, params) => _buildResetPasswordPage(context),
        permission: RoutePermission.public,
        transition: RouteTransition.fade,
        paramValidator: (params) {
          // 验证重置密码所需的token参数
          return params.containsKey('token') && params['token'] != null;
        },
        meta: {
          'title': 'Reset Password',
          'hideNavigation': true,
        },
      ),
    ];
  }

  /// 个人资料路由
  static AppRoute _profileRoute() {
    return AppRoute(
      name: 'profile',
      path: profile,
      builder: (context, params) => _buildProfilePage(context),
      permission: RoutePermission.authenticated,
      transition: RouteTransition.slide,
      meta: {
        'title': 'Profile',
        'showInNavigation': true,
        'icon': Icons.person,
      },
    );
  }

  /// 设置相关路由
  static AppRoute _settingsRoutes() {
    return AppRoute(
      name: 'settings',
      path: settings,
      builder: (context, params) => _buildSettingsPage(context),
      permission: RoutePermission.public,
      transition: RouteTransition.slide,
      meta: {
        'title': 'Settings',
        'showInNavigation': true,
        'icon': Icons.settings,
      },
      children: [
        AppRoute(
          name: 'theme_settings',
          path: '/theme',
          builder: (context, params) => _buildThemeSettingsPage(context),
          permission: RoutePermission.public,
          transition: RouteTransition.slide,
          meta: {
            'title': 'Theme Settings',
            'parent': 'settings',
          },
        ),
        AppRoute(
          name: 'language_settings',
          path: '/language',
          builder: (context, params) => _buildLanguageSettingsPage(context),
          permission: RoutePermission.public,
          transition: RouteTransition.slide,
          meta: {
            'title': 'Language Settings',
            'parent': 'settings',
          },
        ),
      ],
    );
  }

  /// 信息页面路由
  static List<AppRoute> _infoRoutes() {
    return [
      AppRoute(
        name: 'about',
        path: about,
        builder: (context, params) => _buildAboutPage(context),
        permission: RoutePermission.public,
        transition: RouteTransition.fade,
        meta: {
          'title': 'About',
          'showInNavigation': false,
        },
      ),
      AppRoute(
        name: 'help',
        path: help,
        builder: (context, params) => _buildHelpPage(context),
        permission: RoutePermission.public,
        transition: RouteTransition.fade,
        meta: {
          'title': 'Help',
          'showInNavigation': false,
        },
      ),
    ];
  }

  /// 错误页面路由
  static List<AppRoute> _errorRoutes() {
    return [
      AppRoute(
        name: 'error',
        path: error,
        builder: (context, params) => _buildErrorPage(context, params),
        permission: RoutePermission.public,
        transition: RouteTransition.fade,
        meta: {
          'title': 'Error',
          'hideNavigation': true,
        },
      ),
      AppRoute(
        name: 'not_found',
        path: notFound,
        builder: (context, params) => _buildNotFoundPage(context),
        permission: RoutePermission.public,
        transition: RouteTransition.fade,
        meta: {
          'title': 'Page Not Found',
          'hideNavigation': true,
        },
      ),
    ];
  }

  // ============================================================================
  // 页面构建器（临时实现，实际应该引用具体的页面组件）
  // ============================================================================
  
  static Widget _buildHomePage(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Home')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.home, size: 64),
            SizedBox(height: 16),
            Text('Welcome to Flutter Template'),
          ],
        ),
      ),
    );
  }

  static Widget _buildLoginPage(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Login')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.login, size: 64),
            SizedBox(height: 16),
            Text('Login Page'),
          ],
        ),
      ),
    );
  }

  static Widget _buildRegisterPage(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Register')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.person_add, size: 64),
            SizedBox(height: 16),
            Text('Register Page'),
          ],
        ),
      ),
    );
  }

  static Widget _buildForgotPasswordPage(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Forgot Password')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.lock_reset, size: 64),
            SizedBox(height: 16),
            Text('Forgot Password Page'),
          ],
        ),
      ),
    );
  }

  static Widget _buildResetPasswordPage(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Reset Password')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.lock_open, size: 64),
            SizedBox(height: 16),
            Text('Reset Password Page'),
          ],
        ),
      ),
    );
  }

  static Widget _buildProfilePage(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Profile')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.person, size: 64),
            SizedBox(height: 16),
            Text('Profile Page'),
          ],
        ),
      ),
    );
  }

  static Widget _buildSettingsPage(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Settings')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.settings, size: 64),
            SizedBox(height: 16),
            Text('Settings Page'),
          ],
        ),
      ),
    );
  }

  static Widget _buildThemeSettingsPage(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Theme Settings')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.palette, size: 64),
            SizedBox(height: 16),
            Text('Theme Settings Page'),
          ],
        ),
      ),
    );
  }

  static Widget _buildLanguageSettingsPage(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Language Settings')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.language, size: 64),
            SizedBox(height: 16),
            Text('Language Settings Page'),
          ],
        ),
      ),
    );
  }

  static Widget _buildAboutPage(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('About')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.info, size: 64),
            SizedBox(height: 16),
            Text('About Page'),
          ],
        ),
      ),
    );
  }

  static Widget _buildHelpPage(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Help')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.help, size: 64),
            SizedBox(height: 16),
            Text('Help Page'),
          ],
        ),
      ),
    );
  }

  static Widget _buildErrorPage(BuildContext context, Map<String, dynamic> params) {
    final error = params['error'] as String? ?? 'Unknown error';
    return Scaffold(
      appBar: AppBar(title: const Text('Error')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text('Error: $error'),
          ],
        ),
      ),
    );
  }

  static Widget _buildNotFoundPage(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Page Not Found')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64),
            SizedBox(height: 16),
            Text('404 - Page Not Found'),
          ],
        ),
      ),
    );
  }
}
