import 'package:injectable/injectable.dart';
import 'package:hive/hive.dart';
import 'dart:convert';
import '../database/database_manager.dart';

/// 缓存管理器
/// 
/// 提供统一的缓存管理功能，支持多种缓存策略和过期机制
/// **功能依赖**: 需要启用 cache 模块
/// **配置项**: FEATURE_CACHE
@singleton
class CacheManager {
  CacheManager(this._databaseManager);

  final DatabaseManager _databaseManager;
  
  static const Duration _defaultTtl = Duration(hours: 1);
  static const String _metadataKey = '_metadata';

  /// 获取缓存盒子
  Box get _cacheBox => _databaseManager.cacheBox;

  /// 存储缓存数据
  Future<void> put<T>(
    String key,
    T value, {
    Duration? ttl,
    List<String>? tags,
    CachePriority priority = CachePriority.normal,
  }) async {
    final expiresAt = DateTime.now().add(ttl ?? _defaultTtl);
    
    final cacheEntry = CacheEntry<T>(
      key: key,
      value: value,
      createdAt: DateTime.now(),
      expiresAt: expiresAt,
      tags: tags ?? [],
      priority: priority,
      accessCount: 0,
      lastAccessedAt: DateTime.now(),
    );

    await _cacheBox.put(key, cacheEntry.toJson());
    await _updateMetadata(key, cacheEntry);
  }

  /// 获取缓存数据
  Future<T?> get<T>(String key) async {
    final data = _cacheBox.get(key);
    if (data == null) return null;

    try {
      final cacheEntry = CacheEntry<T>.fromJson(data as Map<String, dynamic>);
      
      // 检查是否过期
      if (cacheEntry.isExpired) {
        await remove(key);
        return null;
      }

      // 更新访问信息
      await _updateAccessInfo(key, cacheEntry);
      
      return cacheEntry.value;
    } catch (e) {
      // 数据格式错误，删除缓存
      await remove(key);
      return null;
    }
  }

  /// 检查缓存是否存在且未过期
  Future<bool> contains(String key) async {
    final data = _cacheBox.get(key);
    if (data == null) return false;

    try {
      final cacheEntry = CacheEntry.fromJson(data as Map<String, dynamic>);
      if (cacheEntry.isExpired) {
        await remove(key);
        return false;
      }
      return true;
    } catch (e) {
      await remove(key);
      return false;
    }
  }

  /// 删除指定缓存
  Future<void> remove(String key) async {
    await _cacheBox.delete(key);
    await _removeFromMetadata(key);
  }

  /// 根据标签删除缓存
  Future<void> removeByTag(String tag) async {
    final keysToRemove = <String>[];
    
    for (final key in _cacheBox.keys) {
      if (key == _metadataKey) continue;
      
      final data = _cacheBox.get(key);
      if (data != null) {
        try {
          final cacheEntry = CacheEntry.fromJson(data as Map<String, dynamic>);
          if (cacheEntry.tags.contains(tag)) {
            keysToRemove.add(key as String);
          }
        } catch (e) {
          keysToRemove.add(key as String);
        }
      }
    }

    for (final key in keysToRemove) {
      await remove(key);
    }
  }

  /// 根据前缀删除缓存
  Future<void> removeByPrefix(String prefix) async {
    final keysToRemove = _cacheBox.keys
        .where((key) => key.toString().startsWith(prefix))
        .cast<String>()
        .toList();

    for (final key in keysToRemove) {
      await remove(key);
    }
  }

  /// 清空所有缓存
  Future<void> clear() async {
    await _cacheBox.clear();
  }

  /// 清理过期缓存
  Future<int> clearExpired() async {
    final now = DateTime.now();
    final keysToRemove = <String>[];

    for (final key in _cacheBox.keys) {
      if (key == _metadataKey) continue;
      
      final data = _cacheBox.get(key);
      if (data != null) {
        try {
          final cacheEntry = CacheEntry.fromJson(data as Map<String, dynamic>);
          if (now.isAfter(cacheEntry.expiresAt)) {
            keysToRemove.add(key as String);
          }
        } catch (e) {
          keysToRemove.add(key as String);
        }
      }
    }

    for (final key in keysToRemove) {
      await remove(key);
    }

    return keysToRemove.length;
  }

  /// 清理最少使用的缓存（LRU策略）
  Future<int> clearLeastUsed(int maxEntries) async {
    final entries = <CacheEntry>[];
    
    for (final key in _cacheBox.keys) {
      if (key == _metadataKey) continue;
      
      final data = _cacheBox.get(key);
      if (data != null) {
        try {
          final cacheEntry = CacheEntry.fromJson(data as Map<String, dynamic>);
          entries.add(cacheEntry);
        } catch (e) {
          // 忽略格式错误的条目
        }
      }
    }

    if (entries.length <= maxEntries) return 0;

    // 按最后访问时间排序
    entries.sort((a, b) => a.lastAccessedAt.compareTo(b.lastAccessedAt));
    
    final entriesToRemove = entries.take(entries.length - maxEntries);
    for (final entry in entriesToRemove) {
      await remove(entry.key);
    }

    return entriesToRemove.length;
  }

  /// 获取缓存统计信息
  CacheStats getStats() {
    int totalEntries = 0;
    int expiredEntries = 0;
    int totalSize = 0;
    final now = DateTime.now();
    final priorityCount = <CachePriority, int>{};

    for (final key in _cacheBox.keys) {
      if (key == _metadataKey) continue;
      
      final data = _cacheBox.get(key);
      if (data != null) {
        totalEntries++;
        
        try {
          final jsonString = json.encode(data);
          totalSize += jsonString.length;
          
          final cacheEntry = CacheEntry.fromJson(data as Map<String, dynamic>);
          
          if (now.isAfter(cacheEntry.expiresAt)) {
            expiredEntries++;
          }
          
          priorityCount[cacheEntry.priority] = 
              (priorityCount[cacheEntry.priority] ?? 0) + 1;
        } catch (e) {
          expiredEntries++; // 格式错误的条目视为过期
        }
      }
    }

    return CacheStats(
      totalEntries: totalEntries,
      expiredEntries: expiredEntries,
      totalSize: totalSize,
      priorityCount: priorityCount,
    );
  }

  /// 获取所有缓存键
  List<String> getAllKeys() {
    return _cacheBox.keys
        .where((key) => key != _metadataKey)
        .cast<String>()
        .toList();
  }

  /// 获取指定标签的所有键
  List<String> getKeysByTag(String tag) {
    final keys = <String>[];
    
    for (final key in _cacheBox.keys) {
      if (key == _metadataKey) continue;
      
      final data = _cacheBox.get(key);
      if (data != null) {
        try {
          final cacheEntry = CacheEntry.fromJson(data as Map<String, dynamic>);
          if (cacheEntry.tags.contains(tag)) {
            keys.add(key as String);
          }
        } catch (e) {
          // 忽略格式错误的条目
        }
      }
    }

    return keys;
  }

  /// 更新访问信息
  Future<void> _updateAccessInfo(String key, CacheEntry cacheEntry) async {
    final updatedEntry = cacheEntry.copyWith(
      accessCount: cacheEntry.accessCount + 1,
      lastAccessedAt: DateTime.now(),
    );
    
    await _cacheBox.put(key, updatedEntry.toJson());
  }

  /// 更新元数据
  Future<void> _updateMetadata(String key, CacheEntry cacheEntry) async {
    // 这里可以维护缓存的元数据信息，如索引、统计等
    // 暂时简化实现
  }

  /// 从元数据中移除
  Future<void> _removeFromMetadata(String key) async {
    // 这里可以从元数据中移除相关信息
    // 暂时简化实现
  }
}

/// 缓存条目
class CacheEntry<T> {
  const CacheEntry({
    required this.key,
    required this.value,
    required this.createdAt,
    required this.expiresAt,
    required this.tags,
    required this.priority,
    required this.accessCount,
    required this.lastAccessedAt,
  });

  final String key;
  final T value;
  final DateTime createdAt;
  final DateTime expiresAt;
  final List<String> tags;
  final CachePriority priority;
  final int accessCount;
  final DateTime lastAccessedAt;

  /// 检查是否过期
  bool get isExpired => DateTime.now().isAfter(expiresAt);

  /// 获取剩余生存时间
  Duration get remainingTtl {
    final now = DateTime.now();
    if (now.isAfter(expiresAt)) return Duration.zero;
    return expiresAt.difference(now);
  }

  /// 复制条目
  CacheEntry<T> copyWith({
    String? key,
    T? value,
    DateTime? createdAt,
    DateTime? expiresAt,
    List<String>? tags,
    CachePriority? priority,
    int? accessCount,
    DateTime? lastAccessedAt,
  }) {
    return CacheEntry<T>(
      key: key ?? this.key,
      value: value ?? this.value,
      createdAt: createdAt ?? this.createdAt,
      expiresAt: expiresAt ?? this.expiresAt,
      tags: tags ?? this.tags,
      priority: priority ?? this.priority,
      accessCount: accessCount ?? this.accessCount,
      lastAccessedAt: lastAccessedAt ?? this.lastAccessedAt,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'key': key,
      'value': value,
      'created_at': createdAt.toIso8601String(),
      'expires_at': expiresAt.toIso8601String(),
      'tags': tags,
      'priority': priority.index,
      'access_count': accessCount,
      'last_accessed_at': lastAccessedAt.toIso8601String(),
    };
  }

  /// 从JSON创建
  factory CacheEntry.fromJson(Map<String, dynamic> json) {
    return CacheEntry<T>(
      key: json['key'] as String,
      value: json['value'] as T,
      createdAt: DateTime.parse(json['created_at'] as String),
      expiresAt: DateTime.parse(json['expires_at'] as String),
      tags: List<String>.from(json['tags'] as List),
      priority: CachePriority.values[json['priority'] as int],
      accessCount: json['access_count'] as int,
      lastAccessedAt: DateTime.parse(json['last_accessed_at'] as String),
    );
  }
}

/// 缓存优先级
enum CachePriority {
  low,
  normal,
  high,
  critical,
}

/// 缓存统计信息
class CacheStats {
  const CacheStats({
    required this.totalEntries,
    required this.expiredEntries,
    required this.totalSize,
    required this.priorityCount,
  });

  final int totalEntries;
  final int expiredEntries;
  final int totalSize;
  final Map<CachePriority, int> priorityCount;

  /// 获取命中率（需要额外统计）
  double get hitRate => 0.0; // 简化实现

  @override
  String toString() {
    return 'CacheStats(entries: $totalEntries, expired: $expiredEntries, size: ${(totalSize / 1024).toStringAsFixed(2)}KB)';
  }
}
