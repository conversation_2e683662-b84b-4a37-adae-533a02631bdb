import 'package:flutter/material.dart';

/// 响应式断点系统
/// 
/// 定义不同屏幕尺寸的断点，支持移动端、平板和桌面端的响应式设计
class Breakpoints {
  // 私有构造函数，防止实例化
  Breakpoints._();

  // ============================================================================
  // 断点定义
  // ============================================================================
  
  /// 移动端断点 - 600dp
  static const double mobile = 600;
  
  /// 平板端断点 - 1024dp
  static const double tablet = 1024;
  
  /// 桌面端断点 - 1440dp
  static const double desktop = 1440;
  
  /// 大屏桌面端断点 - 1920dp
  static const double largeDesktop = 1920;

  // ============================================================================
  // 设备类型判断
  // ============================================================================
  
  /// 判断是否为移动端
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobile;
  }
  
  /// 判断是否为平板端
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobile && width < desktop;
  }
  
  /// 判断是否为桌面端
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= desktop;
  }
  
  /// 判断是否为大屏桌面端
  static bool isLargeDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= largeDesktop;
  }

  /// 判断是否为小屏设备（移动端）
  static bool isSmallScreen(BuildContext context) {
    return isMobile(context);
  }

  /// 判断是否为中等屏幕设备（平板端）
  static bool isMediumScreen(BuildContext context) {
    return isTablet(context);
  }

  /// 判断是否为大屏设备（桌面端及以上）
  static bool isLargeScreen(BuildContext context) {
    return isDesktop(context);
  }

  // ============================================================================
  // 屏幕尺寸获取
  // ============================================================================
  
  /// 获取屏幕宽度
  static double getScreenWidth(BuildContext context) {
    return MediaQuery.of(context).size.width;
  }
  
  /// 获取屏幕高度
  static double getScreenHeight(BuildContext context) {
    return MediaQuery.of(context).size.height;
  }
  
  /// 获取屏幕尺寸
  static Size getScreenSize(BuildContext context) {
    return MediaQuery.of(context).size;
  }

  // ============================================================================
  // 设备类型枚举
  // ============================================================================
  
  /// 获取当前设备类型
  static DeviceType getDeviceType(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    
    if (width >= largeDesktop) {
      return DeviceType.largeDesktop;
    } else if (width >= desktop) {
      return DeviceType.desktop;
    } else if (width >= mobile) {
      return DeviceType.tablet;
    } else {
      return DeviceType.mobile;
    }
  }

  // ============================================================================
  // 响应式值计算
  // ============================================================================
  
  /// 根据屏幕尺寸返回不同的值
  static T responsive<T>(
    BuildContext context, {
    required T mobile,
    T? tablet,
    T? desktop,
    T? largeDesktop,
  }) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.largeDesktop:
        return largeDesktop ?? desktop ?? tablet ?? mobile;
      case DeviceType.desktop:
        return desktop ?? tablet ?? mobile;
      case DeviceType.tablet:
        return tablet ?? mobile;
      case DeviceType.mobile:
        return mobile;
    }
  }

  /// 根据屏幕宽度计算响应式值
  static double responsiveValue(
    BuildContext context, {
    required double mobile,
    double? tablet,
    double? desktop,
    double? largeDesktop,
  }) {
    return responsive<double>(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
      largeDesktop: largeDesktop,
    );
  }

  /// 根据屏幕尺寸计算列数
  static int getColumns(BuildContext context, {
    int mobileColumns = 1,
    int tabletColumns = 2,
    int desktopColumns = 3,
    int largeDesktopColumns = 4,
  }) {
    return responsive<int>(
      context,
      mobile: mobileColumns,
      tablet: tabletColumns,
      desktop: desktopColumns,
      largeDesktop: largeDesktopColumns,
    );
  }

  /// 根据屏幕尺寸计算间距
  static double getSpacing(BuildContext context, {
    double mobileSpacing = 8.0,
    double tabletSpacing = 16.0,
    double desktopSpacing = 24.0,
    double largeDesktopSpacing = 32.0,
  }) {
    return responsive<double>(
      context,
      mobile: mobileSpacing,
      tablet: tabletSpacing,
      desktop: desktopSpacing,
      largeDesktop: largeDesktopSpacing,
    );
  }

  /// 根据屏幕尺寸计算字体大小
  static double getFontSize(BuildContext context, {
    required double baseFontSize,
    double mobileScale = 1.0,
    double tabletScale = 1.1,
    double desktopScale = 1.2,
    double largeDesktopScale = 1.3,
  }) {
    final scale = responsive<double>(
      context,
      mobile: mobileScale,
      tablet: tabletScale,
      desktop: desktopScale,
      largeDesktop: largeDesktopScale,
    );
    
    return baseFontSize * scale;
  }

  // ============================================================================
  // 内容宽度限制
  // ============================================================================
  
  /// 获取内容最大宽度
  static double getContentMaxWidth(BuildContext context) {
    return responsive<double>(
      context,
      mobile: double.infinity,
      tablet: 768,
      desktop: 1200,
      largeDesktop: 1400,
    );
  }

  /// 获取侧边栏宽度
  static double getSidebarWidth(BuildContext context) {
    return responsive<double>(
      context,
      mobile: 0, // 移动端不显示侧边栏
      tablet: 240,
      desktop: 280,
      largeDesktop: 320,
    );
  }

  /// 判断是否应该显示侧边栏
  static bool shouldShowSidebar(BuildContext context) {
    return !isMobile(context);
  }

  /// 判断是否应该使用抽屉导航
  static bool shouldUseDrawer(BuildContext context) {
    return isMobile(context);
  }
}

/// 设备类型枚举
enum DeviceType {
  /// 移动端
  mobile,
  
  /// 平板端
  tablet,
  
  /// 桌面端
  desktop,
  
  /// 大屏桌面端
  largeDesktop,
}

/// 设备类型扩展
extension DeviceTypeExtension on DeviceType {
  /// 是否为移动端
  bool get isMobile => this == DeviceType.mobile;
  
  /// 是否为平板端
  bool get isTablet => this == DeviceType.tablet;
  
  /// 是否为桌面端
  bool get isDesktop => this == DeviceType.desktop;
  
  /// 是否为大屏桌面端
  bool get isLargeDesktop => this == DeviceType.largeDesktop;
  
  /// 是否为小屏设备
  bool get isSmallScreen => isMobile;
  
  /// 是否为中等屏幕设备
  bool get isMediumScreen => isTablet;
  
  /// 是否为大屏设备
  bool get isLargeScreen => isDesktop || isLargeDesktop;
  
  /// 获取设备类型名称
  String get name {
    switch (this) {
      case DeviceType.mobile:
        return 'Mobile';
      case DeviceType.tablet:
        return 'Tablet';
      case DeviceType.desktop:
        return 'Desktop';
      case DeviceType.largeDesktop:
        return 'Large Desktop';
    }
  }
}
