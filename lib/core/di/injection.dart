import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:logger/logger.dart';
import 'package:yaml/yaml.dart';
import 'package:flutter/services.dart';
import '../config/feature_config.dart';
import 'injection.config.dart';

/// 全局依赖注入容器
final GetIt getIt = GetIt.instance;

/// 配置依赖注入
@InjectableInit(
  initializerName: 'init',
  preferRelativeImports: true,
  asExtension: true,
)
Future<void> configureDependencies({
  String environment = 'dev',
}) async {
  // 注册环境
  getIt.registerSingleton<String>(
    environment,
    instanceName: 'environment',
  );

  // 初始化功能配置
  await _initializeFeatureConfig(environment);

  // 注册第三方依赖
  await _registerThirdPartyDependencies();

  // 初始化生成的依赖注入配置
  getIt.init(environment: environment);

  // 注册条件依赖
  await _registerConditionalDependencies();

  // 初始化核心服务
  await _initializeCoreServices();

  // 验证依赖注入配置
  await _validateDependencies();
}

/// 初始化功能配置
Future<void> _initializeFeatureConfig(String environment) async {
  try {
    // 加载基础配置
    final baseConfigYaml = await rootBundle.loadString('assets/config/app_config.yaml');
    final baseConfig = loadYaml(baseConfigYaml) as Map;

    // 加载环境特定配置
    final envConfigYaml = await rootBundle.loadString('assets/config/app_config.$environment.yaml');
    final envConfig = loadYaml(envConfigYaml) as Map;

    // 合并配置
    final mergedConfig = Map.from(baseConfig);
    _mergeConfig(mergedConfig, envConfig);

    // 初始化功能配置
    await FeatureConfig.initialize(Map<String, dynamic>.from(mergedConfig));
    
    // 注册功能配置实例
    getIt.registerSingleton<FeatureConfig>(FeatureConfig.instance);
  } catch (e) {
    // 如果配置加载失败，使用默认配置
    await FeatureConfig.initialize({});
    getIt.registerSingleton<FeatureConfig>(FeatureConfig.instance);
  }
}

/// 合并配置
void _mergeConfig(Map target, Map source) {
  source.forEach((key, value) {
    if (value is Map && target[key] is Map) {
      _mergeConfig(target[key], value);
    } else {
      target[key] = value;
    }
  });
}

/// 注册第三方依赖
Future<void> _registerThirdPartyDependencies() async {
  // Logger
  getIt.registerSingleton<Logger>(
    Logger(
      printer: PrettyPrinter(
        methodCount: 2,
        errorMethodCount: 8,
        lineLength: 120,
        colors: true,
        printEmojis: true,
        printTime: true,
      ),
    ),
  );

  // FlutterSecureStorage
  const secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );
  getIt.registerSingleton<FlutterSecureStorage>(secureStorage);

  // 初始化Hive
  await Hive.initFlutter();
  getIt.registerSingleton<HiveInterface>(Hive);

  // Dio (网络客户端)
  getIt.registerSingleton<Dio>(_createDio());
}

/// 创建Dio实例
Dio _createDio() {
  final dio = Dio();
  
  // 基础配置
  dio.options = BaseOptions(
    connectTimeout: const Duration(seconds: 30),
    receiveTimeout: const Duration(seconds: 30),
    sendTimeout: const Duration(seconds: 30),
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
  );

  return dio;
}

/// 注册条件依赖
Future<void> _registerConditionalDependencies() async {
  final featureConfig = getIt<FeatureConfig>();

  // 这里会根据功能配置注册相应的服务
  // 具体的条件依赖注册会在各个功能模块中实现

  // 示例：认证服务的条件注册
  // if (featureConfig.isFeatureEnabled('authentication')) {
  //   getIt.registerLazySingleton<IAuthService>(() => AuthService());
  // } else {
  //   getIt.registerLazySingleton<IAuthService>(() => NoOpAuthService());
  // }
}

/// 初始化核心服务
Future<void> _initializeCoreServices() async {
  try {
    // 初始化数据库管理器
    if (isRegistered<DatabaseManager>()) {
      final databaseManager = get<DatabaseManager>();
      await databaseManager.initialize();
    }

    // 初始化全局状态管理器
    if (isRegistered<GlobalStateManager>()) {
      final globalStateManager = get<GlobalStateManager>();
      await globalStateManager.initialize();
    }

    // 初始化同步管理器
    if (isRegistered<SyncManager>()) {
      final syncManager = get<SyncManager>();
      await syncManager.initialize();
    }

    final logger = get<Logger>();
    logger.i('核心服务初始化完成');
  } catch (e, stackTrace) {
    final logger = get<Logger>();
    logger.e('核心服务初始化失败', error: e, stackTrace: stackTrace);
    rethrow;
  }
}

/// 验证依赖注入配置
Future<void> _validateDependencies() async {
  final logger = getIt<Logger>();
  
  try {
    // 验证核心依赖是否正确注册
    final featureConfig = getIt<FeatureConfig>();
    final secureStorage = getIt<FlutterSecureStorage>();
    final hive = getIt<HiveInterface>();
    final dio = getIt<Dio>();
    
    logger.i('依赖注入验证成功');
    logger.d('已注册的服务数量: ${getIt.allReadySync()}');
    
  } catch (e, stackTrace) {
    final logger = getIt<Logger>();
    logger.e('依赖注入验证失败', error: e, stackTrace: stackTrace);
    rethrow;
  }
}

/// 重置依赖注入容器（主要用于测试）
Future<void> resetDependencies() async {
  await getIt.reset();
}

/// 检查服务是否已注册
bool isRegistered<T extends Object>({String? instanceName}) {
  return getIt.isRegistered<T>(instanceName: instanceName);
}

/// 获取服务实例
T get<T extends Object>({String? instanceName}) {
  return getIt.get<T>(instanceName: instanceName);
}

/// 获取服务实例（异步）
Future<T> getAsync<T extends Object>({String? instanceName}) {
  return getIt.getAsync<T>(instanceName: instanceName);
}

/// 注册单例服务
void registerSingleton<T extends Object>(
  T instance, {
  String? instanceName,
  bool? signalsReady,
  DisposingFunc<T>? dispose,
}) {
  getIt.registerSingleton<T>(
    instance,
    instanceName: instanceName,
    signalsReady: signalsReady,
    dispose: dispose,
  );
}

/// 注册懒加载单例服务
void registerLazySingleton<T extends Object>(
  FactoryFunc<T> factoryFunc, {
  String? instanceName,
  DisposingFunc<T>? dispose,
}) {
  getIt.registerLazySingleton<T>(
    factoryFunc,
    instanceName: instanceName,
    dispose: dispose,
  );
}

/// 注册工厂服务
void registerFactory<T extends Object>(
  FactoryFunc<T> factoryFunc, {
  String? instanceName,
}) {
  getIt.registerFactory<T>(
    factoryFunc,
    instanceName: instanceName,
  );
}

/// 注销服务
Future<void> unregister<T extends Object>({
  String? instanceName,
}) async {
  await getIt.unregister<T>(
    instanceName: instanceName,
  );
}
