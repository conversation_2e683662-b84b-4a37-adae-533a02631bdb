// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:connectivity_plus/connectivity_plus.dart' as _i895;
import 'package:dio/dio.dart' as _i361;
import 'package:flutter_secure_storage/flutter_secure_storage.dart' as _i558;
import 'package:get_it/get_it.dart' as _i174;
import 'package:hive/hive.dart' as _i979;
import 'package:injectable/injectable.dart' as _i526;

import '../../features/auth/data/datasources/auth_local_datasource.dart'
    as _i992;
import '../../features/auth/data/repositories/noop_auth_repository.dart'
    as _i717;
import '../../features/auth/domain/repositories/auth_repository.dart' as _i787;
import '../../features/auth/domain/usecases/login_usecase.dart' as _i188;
import '../../features/auth/domain/usecases/register_usecase.dart' as _i941;
import '../../features/auth/presentation/bloc/auth_bloc.dart' as _i797;
import '../cache/cache_manager.dart' as _i326;
import '../config/environment_config.dart' as _i424;
import '../database/database_manager.dart' as _i779;
import '../errors/enhanced_error_handler.dart' as _i315;
import '../network/dio_client.dart' as _i667;
import '../network/interceptors/auth_interceptor.dart' as _i745;
import '../network/interceptors/cache_interceptor.dart' as _i684;
import '../network/interceptors/error_interceptor.dart' as _i511;
import '../network/interceptors/retry_interceptor.dart' as _i914;
import '../state/global_state_manager.dart' as _i505;
import '../state/module_state_manager.dart' as _i942;
import '../sync/sync_manager.dart' as _i417;

const String _noop = 'noop';

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  _i174.GetIt init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    final dioClientModule = _$DioClientModule();
    gh.factory<_i511.ErrorInterceptor>(() => _i511.ErrorInterceptor());
    gh.singleton<_i942.ModuleStateManager>(() => _i942.ModuleStateManager());
    gh.singleton<_i779.DatabaseManager>(() => _i779.DatabaseManager(
          gh<_i424.EnvironmentConfig>(),
          gh<_i558.FlutterSecureStorage>(),
        ));
    gh.factory<_i745.AuthInterceptor>(
        () => _i745.AuthInterceptor(gh<_i558.FlutterSecureStorage>()));
    gh.singleton<_i326.CacheManager>(
        () => _i326.CacheManager(gh<_i779.DatabaseManager>()));
    gh.singleton<_i505.GlobalStateManager>(
        () => _i505.GlobalStateManager(gh<_i779.DatabaseManager>()));
    gh.singleton<_i315.EnhancedErrorHandler>(
        () => _i315.EnhancedErrorHandler(gh<_i779.DatabaseManager>()));
    gh.factory<_i914.RetryInterceptor>(() => _i914.RetryInterceptor(
          maxRetries: gh<int>(),
          retryDelay: gh<Duration>(),
          retryDelayFactor: gh<double>(),
          maxRetryDelay: gh<Duration>(),
        ));
    gh.factory<_i992.AuthLocalDataSource>(() => _i992.AuthLocalDataSourceImpl(
          gh<_i558.FlutterSecureStorage>(),
          gh<_i979.Box<dynamic>>(),
        ));
    gh.factory<_i941.ForgotPasswordUseCase>(
      () => const _i717.NoOpForgotPasswordUseCase(),
      registerFor: {_noop},
    );
    gh.factory<_i188.CheckAuthStatusUseCase>(
      () => const _i717.NoOpCheckAuthStatusUseCase(),
      registerFor: {_noop},
    );
    gh.factory<_i787.IAuthRepository>(
      () => const _i717.NoOpAuthRepository(),
      registerFor: {_noop},
    );
    gh.factory<_i188.LoginUseCase>(
      () => const _i717.NoOpLoginUseCase(),
      registerFor: {_noop},
    );
    gh.factory<_i941.RegisterUseCase>(
      () => const _i717.NoOpRegisterUseCase(),
      registerFor: {_noop},
    );
    gh.factory<_i941.ResetPasswordUseCase>(
      () => const _i717.NoOpResetPasswordUseCase(),
      registerFor: {_noop},
    );
    gh.factory<_i684.CacheInterceptor>(
        () => _i684.CacheInterceptor(gh<_i979.Box<dynamic>>()));
    gh.factory<_i188.RefreshTokenUseCase>(
      () => const _i717.NoOpRefreshTokenUseCase(),
      registerFor: {_noop},
    );
    gh.factory<_i188.LogoutUseCase>(
      () => const _i717.NoOpLogoutUseCase(),
      registerFor: {_noop},
    );
    gh.factory<_i188.GetCurrentUserUseCase>(
      () => const _i717.NoOpGetCurrentUserUseCase(),
      registerFor: {_noop},
    );
    gh.singleton<_i361.Dio>(() => dioClientModule.provideDio(
          gh<_i424.EnvironmentConfig>(),
          gh<_i745.AuthInterceptor>(),
          gh<_i914.RetryInterceptor>(),
          gh<_i511.ErrorInterceptor>(),
          gh<_i684.CacheInterceptor>(),
        ));
    gh.factory<_i505.GlobalStateBloc>(
        () => _i505.GlobalStateBloc(gh<_i505.GlobalStateManager>()));
    gh.factory<_i941.RegisterUseCase>(
        () => _i941.RegisterUseCase(gh<_i787.IAuthRepository>()));
    gh.factory<_i941.ForgotPasswordUseCase>(
        () => _i941.ForgotPasswordUseCase(gh<_i787.IAuthRepository>()));
    gh.factory<_i941.ResetPasswordUseCase>(
        () => _i941.ResetPasswordUseCase(gh<_i787.IAuthRepository>()));
    gh.factory<_i188.LoginUseCase>(
        () => _i188.LoginUseCase(gh<_i787.IAuthRepository>()));
    gh.factory<_i188.LogoutUseCase>(
        () => _i188.LogoutUseCase(gh<_i787.IAuthRepository>()));
    gh.factory<_i188.GetCurrentUserUseCase>(
        () => _i188.GetCurrentUserUseCase(gh<_i787.IAuthRepository>()));
    gh.factory<_i188.CheckAuthStatusUseCase>(
        () => _i188.CheckAuthStatusUseCase(gh<_i787.IAuthRepository>()));
    gh.factory<_i188.RefreshTokenUseCase>(
        () => _i188.RefreshTokenUseCase(gh<_i787.IAuthRepository>()));
    gh.singleton<_i361.Dio>(
      () => dioClientModule.provideUploadDio(
        gh<_i424.EnvironmentConfig>(),
        gh<_i745.AuthInterceptor>(),
      ),
      instanceName: 'upload',
    );
    gh.singleton<_i361.Dio>(
      () => dioClientModule.provideDownloadDio(
        gh<_i424.EnvironmentConfig>(),
        gh<_i745.AuthInterceptor>(),
      ),
      instanceName: 'download',
    );
    gh.factory<_i667.DioClient>(() => _i667.DioClient(
          gh<_i361.Dio>(),
          gh<_i361.Dio>(instanceName: 'upload'),
          gh<_i361.Dio>(instanceName: 'download'),
        ));
    gh.factory<_i797.AuthBloc>(() => _i797.AuthBloc(
          gh<_i188.LoginUseCase>(),
          gh<_i188.LogoutUseCase>(),
          gh<_i941.RegisterUseCase>(),
          gh<_i188.GetCurrentUserUseCase>(),
          gh<_i188.CheckAuthStatusUseCase>(),
          gh<_i188.RefreshTokenUseCase>(),
          gh<_i941.ForgotPasswordUseCase>(),
          gh<_i941.ResetPasswordUseCase>(),
        ));
    gh.singleton<_i417.SyncManager>(() => _i417.SyncManager(
          gh<_i779.DatabaseManager>(),
          gh<_i667.DioClient>(),
          gh<_i895.Connectivity>(),
        ));
    return this;
  }
}

class _$DioClientModule extends _i667.DioClientModule {}
