/// 简化的依赖注入配置
/// 
/// 用于确保应用能够正常启动的最小依赖配置
library;

import 'package:get_it/get_it.dart';
import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:logger/logger.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../config/feature_config.dart';
import '../config/environment_config.dart';

/// 全局依赖注入容器
final GetIt getIt = GetIt.instance;

/// 简化的依赖注入配置
Future<void> configureSimpleDependencies({
  String environment = 'dev',
}) async {
  try {
    // 注册环境
    getIt.registerSingleton<String>(
      environment,
      instanceName: 'environment',
    );

    // 初始化环境配置
    await EnvironmentConfig.initialize(environment);
    getIt.registerSingleton<EnvironmentConfig>(EnvironmentConfig.instance);

    // 初始化功能配置
    await FeatureConfig.initialize();
    getIt.registerSingleton<FeatureConfig>(FeatureConfig.instance);

    // 注册基础服务
    await _registerBasicServices();

    print('✅ 简化依赖注入配置完成');
  } catch (e) {
    print('❌ 简化依赖注入配置失败: $e');
    rethrow;
  }
}

/// 注册基础服务
Future<void> _registerBasicServices() async {
  // Logger
  getIt.registerSingleton<Logger>(
    Logger(
      printer: PrettyPrinter(
        methodCount: 2,
        errorMethodCount: 8,
        lineLength: 120,
        colors: true,
        printEmojis: true,
        printTime: true,
      ),
    ),
  );

  // FlutterSecureStorage
  const secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );
  getIt.registerSingleton<FlutterSecureStorage>(secureStorage);

  // 初始化Hive
  await Hive.initFlutter();
  getIt.registerSingleton<HiveInterface>(Hive);

  // Connectivity
  getIt.registerSingleton<Connectivity>(Connectivity());

  // Dio (简化配置)
  final dio = Dio();
  dio.options = BaseOptions(
    connectTimeout: const Duration(seconds: 30),
    receiveTimeout: const Duration(seconds: 30),
    sendTimeout: const Duration(seconds: 30),
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
  );
  getIt.registerSingleton<Dio>(dio);
}

/// 获取服务实例
T get<T extends Object>({String? instanceName}) {
  return getIt.get<T>(instanceName: instanceName);
}

/// 检查服务是否已注册
bool isRegistered<T extends Object>({String? instanceName}) {
  return getIt.isRegistered<T>(instanceName: instanceName);
}

/// 注册单例服务
void registerSingleton<T extends Object>(
  T instance, {
  String? instanceName,
  bool? signalsReady,
  DisposingFunc<T>? dispose,
}) {
  getIt.registerSingleton<T>(
    instance,
    instanceName: instanceName,
    signalsReady: signalsReady,
    dispose: dispose,
  );
}

/// 注册懒加载单例服务
void registerLazySingleton<T extends Object>(
  FactoryFunc<T> factoryFunc, {
  String? instanceName,
  DisposingFunc<T>? dispose,
}) {
  getIt.registerLazySingleton<T>(
    factoryFunc,
    instanceName: instanceName,
    dispose: dispose,
  );
}

/// 重置依赖注入容器
Future<void> resetDependencies() async {
  await getIt.reset();
}

/// 清理资源
Future<void> dispose() async {
  await getIt.reset();
}
