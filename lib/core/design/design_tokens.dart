import 'package:flutter/material.dart';

/// 设计令牌系统
/// 
/// 提供统一的设计规范，包括颜色、字体、间距、圆角、阴影等
/// 确保整个应用的视觉一致性
class DesignTokens {
  // 私有构造函数，防止实例化
  DesignTokens._();

  // ============================================================================
  // 颜色系统
  // ============================================================================
  
  /// 浅色主题颜色方案
  static const ColorScheme lightColorScheme = ColorScheme(
    brightness: Brightness.light,
    primary: Color(0xFF1976D2),
    onPrimary: Color(0xFFFFFFFF),
    primaryContainer: Color(0xFFBBDEFB),
    onPrimaryContainer: Color(0xFF0D47A1),
    secondary: Color(0xFF03DAC6),
    onSecondary: Color(0xFF000000),
    secondaryContainer: Color(0xFFB2DFDB),
    onSecondaryContainer: Color(0xFF004D40),
    tertiary: Color(0xFF9C27B0),
    onTertiary: Color(0xFFFFFFFF),
    tertiaryContainer: Color(0xFFE1BEE7),
    onTertiaryContainer: Color(0xFF4A148C),
    error: Color(0xFFB00020),
    onError: Color(0xFFFFFFFF),
    errorContainer: Color(0xFFFFDAD6),
    onErrorContainer: Color(0xFF410002),
    background: Color(0xFFFAFAFA),
    onBackground: Color(0xFF1C1B1F),
    surface: Color(0xFFFFFFFF),
    onSurface: Color(0xFF1C1B1F),
    surfaceVariant: Color(0xFFE7E0EC),
    onSurfaceVariant: Color(0xFF49454F),
    outline: Color(0xFF79747E),
    outlineVariant: Color(0xFFCAC4D0),
    shadow: Color(0xFF000000),
    scrim: Color(0xFF000000),
    inverseSurface: Color(0xFF313033),
    onInverseSurface: Color(0xFFF4EFF4),
    inversePrimary: Color(0xFF90CAF9),
  );

  /// 深色主题颜色方案
  static const ColorScheme darkColorScheme = ColorScheme(
    brightness: Brightness.dark,
    primary: Color(0xFF90CAF9),
    onPrimary: Color(0xFF0D47A1),
    primaryContainer: Color(0xFF1565C0),
    onPrimaryContainer: Color(0xFFBBDEFB),
    secondary: Color(0xFF4DB6AC),
    onSecondary: Color(0xFF004D40),
    secondaryContainer: Color(0xFF00695C),
    onSecondaryContainer: Color(0xFFB2DFDB),
    tertiary: Color(0xFFCE93D8),
    onTertiary: Color(0xFF4A148C),
    tertiaryContainer: Color(0xFF7B1FA2),
    onTertiaryContainer: Color(0xFFE1BEE7),
    error: Color(0xFFCF6679),
    onError: Color(0xFF410002),
    errorContainer: Color(0xFF93000A),
    onErrorContainer: Color(0xFFFFDAD6),
    background: Color(0xFF121212),
    onBackground: Color(0xFFE6E1E5),
    surface: Color(0xFF1E1E1E),
    onSurface: Color(0xFFE6E1E5),
    surfaceVariant: Color(0xFF49454F),
    onSurfaceVariant: Color(0xFFCAC4D0),
    outline: Color(0xFF938F99),
    outlineVariant: Color(0xFF49454F),
    shadow: Color(0xFF000000),
    scrim: Color(0xFF000000),
    inverseSurface: Color(0xFFE6E1E5),
    onInverseSurface: Color(0xFF313033),
    inversePrimary: Color(0xFF1976D2),
  );

  // ============================================================================
  // 字体系统
  // ============================================================================
  
  /// 文本主题
  static const TextTheme textTheme = TextTheme(
    // 显示文本
    displayLarge: TextStyle(
      fontSize: 57,
      fontWeight: FontWeight.w400,
      letterSpacing: -0.25,
      height: 1.12,
    ),
    displayMedium: TextStyle(
      fontSize: 45,
      fontWeight: FontWeight.w400,
      letterSpacing: 0,
      height: 1.16,
    ),
    displaySmall: TextStyle(
      fontSize: 36,
      fontWeight: FontWeight.w400,
      letterSpacing: 0,
      height: 1.22,
    ),
    
    // 标题文本
    headlineLarge: TextStyle(
      fontSize: 32,
      fontWeight: FontWeight.w400,
      letterSpacing: 0,
      height: 1.25,
    ),
    headlineMedium: TextStyle(
      fontSize: 28,
      fontWeight: FontWeight.w400,
      letterSpacing: 0,
      height: 1.29,
    ),
    headlineSmall: TextStyle(
      fontSize: 24,
      fontWeight: FontWeight.w400,
      letterSpacing: 0,
      height: 1.33,
    ),
    
    // 标题文本
    titleLarge: TextStyle(
      fontSize: 22,
      fontWeight: FontWeight.w400,
      letterSpacing: 0,
      height: 1.27,
    ),
    titleMedium: TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.15,
      height: 1.50,
    ),
    titleSmall: TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.1,
      height: 1.43,
    ),
    
    // 标签文本
    labelLarge: TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.1,
      height: 1.43,
    ),
    labelMedium: TextStyle(
      fontSize: 12,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.5,
      height: 1.33,
    ),
    labelSmall: TextStyle(
      fontSize: 11,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.5,
      height: 1.45,
    ),
    
    // 正文文本
    bodyLarge: TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.15,
      height: 1.50,
    ),
    bodyMedium: TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.25,
      height: 1.43,
    ),
    bodySmall: TextStyle(
      fontSize: 12,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.4,
      height: 1.33,
    ),
  );

  // ============================================================================
  // 间距系统
  // ============================================================================
  
  /// 超小间距 - 4dp
  static const EdgeInsets spacingXS = EdgeInsets.all(4);
  
  /// 小间距 - 8dp
  static const EdgeInsets spacingS = EdgeInsets.all(8);
  
  /// 中等间距 - 16dp
  static const EdgeInsets spacingM = EdgeInsets.all(16);
  
  /// 大间距 - 24dp
  static const EdgeInsets spacingL = EdgeInsets.all(24);
  
  /// 超大间距 - 32dp
  static const EdgeInsets spacingXL = EdgeInsets.all(32);
  
  /// 巨大间距 - 48dp
  static const EdgeInsets spacingXXL = EdgeInsets.all(48);

  // 水平间距
  static const EdgeInsets spacingHorizontalXS = EdgeInsets.symmetric(horizontal: 4);
  static const EdgeInsets spacingHorizontalS = EdgeInsets.symmetric(horizontal: 8);
  static const EdgeInsets spacingHorizontalM = EdgeInsets.symmetric(horizontal: 16);
  static const EdgeInsets spacingHorizontalL = EdgeInsets.symmetric(horizontal: 24);
  static const EdgeInsets spacingHorizontalXL = EdgeInsets.symmetric(horizontal: 32);

  // 垂直间距
  static const EdgeInsets spacingVerticalXS = EdgeInsets.symmetric(vertical: 4);
  static const EdgeInsets spacingVerticalS = EdgeInsets.symmetric(vertical: 8);
  static const EdgeInsets spacingVerticalM = EdgeInsets.symmetric(vertical: 16);
  static const EdgeInsets spacingVerticalL = EdgeInsets.symmetric(vertical: 24);
  static const EdgeInsets spacingVerticalXL = EdgeInsets.symmetric(vertical: 32);

  // ============================================================================
  // 圆角系统
  // ============================================================================
  
  /// 无圆角
  static const BorderRadius radiusNone = BorderRadius.zero;
  
  /// 小圆角 - 4dp
  static const BorderRadius radiusS = BorderRadius.all(Radius.circular(4));
  
  /// 中等圆角 - 8dp
  static const BorderRadius radiusM = BorderRadius.all(Radius.circular(8));
  
  /// 大圆角 - 16dp
  static const BorderRadius radiusL = BorderRadius.all(Radius.circular(16));
  
  /// 超大圆角 - 24dp
  static const BorderRadius radiusXL = BorderRadius.all(Radius.circular(24));
  
  /// 完全圆角 - 999dp
  static const BorderRadius radiusFull = BorderRadius.all(Radius.circular(999));

  // ============================================================================
  // 阴影系统
  // ============================================================================
  
  /// 无阴影
  static const List<BoxShadow> shadowNone = [];
  
  /// 小阴影
  static const List<BoxShadow> shadowS = [
    BoxShadow(
      color: Color(0x1A000000),
      blurRadius: 2,
      offset: Offset(0, 1),
    ),
  ];
  
  /// 中等阴影
  static const List<BoxShadow> shadowM = [
    BoxShadow(
      color: Color(0x1F000000),
      blurRadius: 4,
      offset: Offset(0, 2),
    ),
  ];
  
  /// 大阴影
  static const List<BoxShadow> shadowL = [
    BoxShadow(
      color: Color(0x24000000),
      blurRadius: 8,
      offset: Offset(0, 4),
    ),
  ];
  
  /// 超大阴影
  static const List<BoxShadow> shadowXL = [
    BoxShadow(
      color: Color(0x29000000),
      blurRadius: 16,
      offset: Offset(0, 8),
    ),
  ];

  // ============================================================================
  // 尺寸系统
  // ============================================================================
  
  /// 图标尺寸
  static const double iconSizeS = 16;
  static const double iconSizeM = 24;
  static const double iconSizeL = 32;
  static const double iconSizeXL = 48;

  /// 按钮高度
  static const double buttonHeightS = 32;
  static const double buttonHeightM = 40;
  static const double buttonHeightL = 48;
  static const double buttonHeightXL = 56;

  /// 输入框高度
  static const double inputHeightS = 32;
  static const double inputHeightM = 40;
  static const double inputHeightL = 48;
  static const double inputHeightXL = 56;

  // ============================================================================
  // 动画系统
  // ============================================================================
  
  /// 动画持续时间
  static const Duration durationFast = Duration(milliseconds: 150);
  static const Duration durationNormal = Duration(milliseconds: 300);
  static const Duration durationSlow = Duration(milliseconds: 500);

  /// 动画曲线
  static const Curve curveStandard = Curves.easeInOut;
  static const Curve curveDecelerate = Curves.easeOut;
  static const Curve curveAccelerate = Curves.easeIn;
}
