import 'package:flutter/material.dart';
import 'design_tokens.dart';

/// 设计系统
/// 
/// 基于设计令牌构建的完整设计系统，提供统一的主题配置
class DesignSystem {
  // 私有构造函数，防止实例化
  DesignSystem._();

  /// 获取浅色主题
  static ThemeData get lightTheme => _buildTheme(
    colorScheme: DesignTokens.lightColorScheme,
    brightness: Brightness.light,
  );

  /// 获取深色主题
  static ThemeData get darkTheme => _buildTheme(
    colorScheme: DesignTokens.darkColorScheme,
    brightness: Brightness.dark,
  );

  /// 构建主题
  static ThemeData _buildTheme({
    required ColorScheme colorScheme,
    required Brightness brightness,
  }) {
    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      textTheme: DesignTokens.textTheme,
      
      // AppBar 主题
      appBarTheme: AppBarTheme(
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        elevation: 0,
        scrolledUnderElevation: 1,
        titleTextStyle: DesignTokens.textTheme.titleLarge?.copyWith(
          color: colorScheme.onSurface,
        ),
        iconTheme: IconThemeData(
          color: colorScheme.onSurface,
          size: DesignTokens.iconSizeM,
        ),
      ),

      // 按钮主题
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: colorScheme.primary,
          foregroundColor: colorScheme.onPrimary,
          elevation: 1,
          shadowColor: colorScheme.shadow,
          shape: RoundedRectangleBorder(
            borderRadius: DesignTokens.radiusM,
          ),
          minimumSize: const Size(64, DesignTokens.buttonHeightM),
          padding: DesignTokens.spacingHorizontalM,
          textStyle: DesignTokens.textTheme.labelLarge,
        ),
      ),

      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: colorScheme.primary,
          side: BorderSide(color: colorScheme.outline),
          shape: RoundedRectangleBorder(
            borderRadius: DesignTokens.radiusM,
          ),
          minimumSize: const Size(64, DesignTokens.buttonHeightM),
          padding: DesignTokens.spacingHorizontalM,
          textStyle: DesignTokens.textTheme.labelLarge,
        ),
      ),

      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: colorScheme.primary,
          shape: RoundedRectangleBorder(
            borderRadius: DesignTokens.radiusM,
          ),
          minimumSize: const Size(64, DesignTokens.buttonHeightM),
          padding: DesignTokens.spacingHorizontalM,
          textStyle: DesignTokens.textTheme.labelLarge,
        ),
      ),

      // 输入框主题
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: colorScheme.surfaceVariant.withOpacity(0.3),
        border: OutlineInputBorder(
          borderRadius: DesignTokens.radiusM,
          borderSide: BorderSide(color: colorScheme.outline),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: DesignTokens.radiusM,
          borderSide: BorderSide(color: colorScheme.outline),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: DesignTokens.radiusM,
          borderSide: BorderSide(color: colorScheme.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: DesignTokens.radiusM,
          borderSide: BorderSide(color: colorScheme.error),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: DesignTokens.radiusM,
          borderSide: BorderSide(color: colorScheme.error, width: 2),
        ),
        contentPadding: DesignTokens.spacingM,
        labelStyle: DesignTokens.textTheme.bodyMedium?.copyWith(
          color: colorScheme.onSurfaceVariant,
        ),
        hintStyle: DesignTokens.textTheme.bodyMedium?.copyWith(
          color: colorScheme.onSurfaceVariant.withOpacity(0.6),
        ),
        errorStyle: DesignTokens.textTheme.bodySmall?.copyWith(
          color: colorScheme.error,
        ),
      ),

      // 卡片主题
      cardTheme: CardTheme(
        color: colorScheme.surface,
        shadowColor: colorScheme.shadow,
        elevation: 1,
        shape: RoundedRectangleBorder(
          borderRadius: DesignTokens.radiusM,
        ),
        margin: DesignTokens.spacingS,
      ),

      // 对话框主题
      dialogTheme: DialogTheme(
        backgroundColor: colorScheme.surface,
        surfaceTintColor: colorScheme.surfaceTint,
        elevation: 6,
        shape: RoundedRectangleBorder(
          borderRadius: DesignTokens.radiusL,
        ),
        titleTextStyle: DesignTokens.textTheme.headlineSmall?.copyWith(
          color: colorScheme.onSurface,
        ),
        contentTextStyle: DesignTokens.textTheme.bodyMedium?.copyWith(
          color: colorScheme.onSurfaceVariant,
        ),
      ),

      // 底部导航栏主题
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: colorScheme.surface,
        selectedItemColor: colorScheme.primary,
        unselectedItemColor: colorScheme.onSurfaceVariant,
        type: BottomNavigationBarType.fixed,
        elevation: 3,
        selectedLabelStyle: DesignTokens.textTheme.labelSmall,
        unselectedLabelStyle: DesignTokens.textTheme.labelSmall,
      ),

      // 导航栏主题
      navigationBarTheme: NavigationBarThemeData(
        backgroundColor: colorScheme.surface,
        indicatorColor: colorScheme.secondaryContainer,
        elevation: 3,
        labelTextStyle: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return DesignTokens.textTheme.labelSmall?.copyWith(
              color: colorScheme.onSecondaryContainer,
            );
          }
          return DesignTokens.textTheme.labelSmall?.copyWith(
            color: colorScheme.onSurfaceVariant,
          );
        }),
        iconTheme: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return IconThemeData(
              color: colorScheme.onSecondaryContainer,
              size: DesignTokens.iconSizeM,
            );
          }
          return IconThemeData(
            color: colorScheme.onSurfaceVariant,
            size: DesignTokens.iconSizeM,
          );
        }),
      ),

      // 列表瓦片主题
      listTileTheme: ListTileThemeData(
        contentPadding: DesignTokens.spacingHorizontalM,
        titleTextStyle: DesignTokens.textTheme.bodyLarge?.copyWith(
          color: colorScheme.onSurface,
        ),
        subtitleTextStyle: DesignTokens.textTheme.bodyMedium?.copyWith(
          color: colorScheme.onSurfaceVariant,
        ),
        iconColor: colorScheme.onSurfaceVariant,
        shape: RoundedRectangleBorder(
          borderRadius: DesignTokens.radiusM,
        ),
      ),

      // 分割线主题
      dividerTheme: DividerThemeData(
        color: colorScheme.outlineVariant,
        thickness: 1,
        space: 1,
      ),

      // 图标主题
      iconTheme: IconThemeData(
        color: colorScheme.onSurface,
        size: DesignTokens.iconSizeM,
      ),

      // 浮动操作按钮主题
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: colorScheme.primaryContainer,
        foregroundColor: colorScheme.onPrimaryContainer,
        elevation: 6,
        shape: RoundedRectangleBorder(
          borderRadius: DesignTokens.radiusL,
        ),
      ),

      // Chip 主题
      chipTheme: ChipThemeData(
        backgroundColor: colorScheme.surfaceVariant,
        deleteIconColor: colorScheme.onSurfaceVariant,
        disabledColor: colorScheme.onSurface.withOpacity(0.12),
        selectedColor: colorScheme.secondaryContainer,
        secondarySelectedColor: colorScheme.secondaryContainer,
        padding: DesignTokens.spacingS,
        labelStyle: DesignTokens.textTheme.labelLarge?.copyWith(
          color: colorScheme.onSurfaceVariant,
        ),
        secondaryLabelStyle: DesignTokens.textTheme.labelLarge?.copyWith(
          color: colorScheme.onSecondaryContainer,
        ),
        brightness: brightness,
        shape: RoundedRectangleBorder(
          borderRadius: DesignTokens.radiusS,
        ),
      ),

      // 进度指示器主题
      progressIndicatorTheme: ProgressIndicatorThemeData(
        color: colorScheme.primary,
        linearTrackColor: colorScheme.surfaceVariant,
        circularTrackColor: colorScheme.surfaceVariant,
      ),

      // 滑块主题
      sliderTheme: SliderThemeData(
        activeTrackColor: colorScheme.primary,
        inactiveTrackColor: colorScheme.surfaceVariant,
        thumbColor: colorScheme.primary,
        overlayColor: colorScheme.primary.withOpacity(0.12),
        valueIndicatorColor: colorScheme.inverseSurface,
        valueIndicatorTextStyle: DesignTokens.textTheme.labelSmall?.copyWith(
          color: colorScheme.onInverseSurface,
        ),
      ),

      // 开关主题
      switchTheme: SwitchThemeData(
        thumbColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return colorScheme.onPrimary;
          }
          return colorScheme.outline;
        }),
        trackColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return colorScheme.primary;
          }
          return colorScheme.surfaceVariant;
        }),
      ),

      // 复选框主题
      checkboxTheme: CheckboxThemeData(
        fillColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return colorScheme.primary;
          }
          return Colors.transparent;
        }),
        checkColor: MaterialStateProperty.all(colorScheme.onPrimary),
        side: BorderSide(color: colorScheme.outline, width: 2),
        shape: RoundedRectangleBorder(
          borderRadius: DesignTokens.radiusS,
        ),
      ),

      // 单选按钮主题
      radioTheme: RadioThemeData(
        fillColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return colorScheme.primary;
          }
          return colorScheme.outline;
        }),
      ),
    );
  }
}
