/// 生产环境配置
/// 
/// 提供生产环境的专用配置管理
library;

import 'package:enterprise_flutter/core/config/feature_config.dart';

/// 生产环境配置类
class ProductionConfig {
  /// API基础URL
  static const String apiBaseUrl = 'https://api.production.company.com';
  
  /// API超时时间（毫秒）
  static const int apiTimeout = 30000;
  
  /// API重试次数
  static const int apiRetryAttempts = 3;
  
  /// 是否启用日志
  static const bool enableLogging = false;
  
  /// 是否启用分析
  static const bool enableAnalytics = true;
  
  /// 是否启用崩溃报告
  static const bool enableCrashReporting = true;
  
  /// 是否启用远程日志
  static const bool enableRemoteLogging = true;
  
  // 安全配置
  /// 是否启用证书固定
  static const bool enableCertificatePinning = true;
  
  /// 是否启用Root检测
  static const bool enableRootDetection = true;
  
  /// 是否启用调试保护
  static const bool enableDebugProtection = true;
  
  /// 是否启用数据加密
  static const bool enableDataEncryption = true;
  
  // 性能配置
  /// 是否启用懒加载
  static const bool enableLazyLoading = true;
  
  /// 是否启用图片缓存
  static const bool enableImageCaching = true;
  
  /// 是否启用网络缓存
  static const bool enableNetworkCaching = true;
  
  /// 是否启用内存优化
  static const bool enableMemoryOptimization = true;
  
  /// 最大缓存大小（字节）
  static const int maxCacheSize = 100 * 1024 * 1024; // 100MB
  
  /// 日志级别
  static const String logLevel = 'WARNING';
  
  /// 数据库名称
  static const String databaseName = 'app_prod.db';
  
  /// 功能开关配置
  static const Map<String, bool> featureFlags = {
    // 核心功能 - 生产环境全部启用
    Features.authentication: true,
    Features.network: true,
    Features.database: true,
    Features.stateManagement: true,
    Features.errorHandling: true,
    
    // UI/UX 功能 - 生产环境全部启用
    Features.designSystem: true,
    Features.theming: true,
    Features.internationalization: true,
    Features.routing: true,
    Features.performanceMonitoring: true,
    
    // 高级功能 - 根据需求启用
    Features.analytics: true,
    Features.pushNotifications: true,
    Features.offlineSupport: true,
    Features.securityEnhanced: true,
    
    // 开发工具 - 生产环境禁用
    Features.devTools: false,
    Features.debugFeatures: false,
  };
  
  /// 安全配置
  static const Map<String, bool> securityConfig = {
    'encryption_enabled': true,
    'certificate_pinning': true,
    'root_detection': true,
    'debug_protection': true,
  };
  
  /// 性能配置
  static const Map<String, dynamic> performanceConfig = {
    'lazy_loading': true,
    'image_caching': true,
    'network_caching': true,
    'memory_optimization': true,
    'max_cache_size': maxCacheSize,
  };
  
  /// 日志配置
  static const Map<String, dynamic> loggingConfig = {
    'level': logLevel,
    'remote_logging': enableRemoteLogging,
    'crash_reporting': enableCrashReporting,
    'enable_console': false,
    'enable_file': true,
  };
  
  /// 获取API配置
  static Map<String, dynamic> get apiConfig => {
    'base_url': apiBaseUrl,
    'timeout': apiTimeout,
    'retry_attempts': apiRetryAttempts,
    'enable_logging': enableLogging,
  };
  
  /// 获取数据库配置
  static Map<String, dynamic> get databaseConfig => {
    'name': databaseName,
    'enable_logging': enableLogging,
    'encryption_enabled': enableDataEncryption,
  };
  
  /// 验证生产环境配置
  static bool validateConfig() {
    // 验证必要的配置项
    if (apiBaseUrl.isEmpty) return false;
    if (apiTimeout <= 0) return false;
    if (databaseName.isEmpty) return false;
    
    // 验证安全配置
    if (!enableCertificatePinning) return false;
    if (!enableDataEncryption) return false;
    
    // 验证功能配置
    if (featureFlags[Features.devTools] == true) return false;
    if (featureFlags[Features.debugFeatures] == true) return false;
    
    return true;
  }
  
  /// 获取环境信息
  static Map<String, dynamic> get environmentInfo => {
    'name': 'production',
    'debug': false,
    'api_base_url': apiBaseUrl,
    'features_enabled': featureFlags.entries
        .where((entry) => entry.value)
        .map((entry) => entry.key)
        .toList(),
    'security_enabled': securityConfig.entries
        .where((entry) => entry.value)
        .map((entry) => entry.key)
        .toList(),
  };
}
