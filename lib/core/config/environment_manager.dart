/// 环境配置管理器
/// 
/// 统一管理不同环境的配置，支持开发、测试、生产环境
library;

import 'package:enterprise_flutter/core/config/production_config.dart';
import 'package:enterprise_flutter/core/config/environment_config.dart';

/// 环境配置管理器
class EnvironmentManager {
  static EnvironmentManager? _instance;
  static EnvironmentManager get instance => _instance ??= EnvironmentManager._();
  
  EnvironmentManager._();
  
  String _currentEnvironment = 'development';
  
  /// 当前环境
  String get currentEnvironment => _currentEnvironment;
  
  /// 初始化环境配置
  static Future<void> initialize([String? environment]) async {
    final env = environment ?? const String.fromEnvironment(
      'ENVIRONMENT',
      defaultValue: 'development',
    );
    
    instance._currentEnvironment = env;
    
    // 初始化环境配置
    await EnvironmentConfig.initialize(env);
  }
  
  /// 是否为生产环境
  bool get isProduction => _currentEnvironment == 'production';
  
  /// 是否为开发环境
  bool get isDevelopment => _currentEnvironment == 'development';
  
  /// 是否为测试环境
  bool get isStaging => _currentEnvironment == 'staging';
  
  /// 获取API基础URL
  String get apiBaseUrl {
    switch (_currentEnvironment) {
      case 'production':
        return ProductionConfig.apiBaseUrl;
      case 'staging':
        return 'https://api.staging.company.com';
      default:
        return 'https://api.dev.company.com';
    }
  }
  
  /// 获取API超时时间
  int get apiTimeout {
    switch (_currentEnvironment) {
      case 'production':
        return ProductionConfig.apiTimeout;
      case 'staging':
        return 20000;
      default:
        return 10000;
    }
  }
  
  /// 获取功能开关配置
  Map<String, bool> get featureFlags {
    switch (_currentEnvironment) {
      case 'production':
        return ProductionConfig.featureFlags;
      case 'staging':
        return _getStagingFeatureFlags();
      default:
        return _getDevelopmentFeatureFlags();
    }
  }
  
  /// 获取安全配置
  Map<String, bool> get securityConfig {
    switch (_currentEnvironment) {
      case 'production':
        return ProductionConfig.securityConfig;
      case 'staging':
        return {
          'encryption_enabled': true,
          'certificate_pinning': false,
          'root_detection': false,
          'debug_protection': false,
        };
      default:
        return {
          'encryption_enabled': false,
          'certificate_pinning': false,
          'root_detection': false,
          'debug_protection': false,
        };
    }
  }
  
  /// 获取性能配置
  Map<String, dynamic> get performanceConfig {
    switch (_currentEnvironment) {
      case 'production':
        return ProductionConfig.performanceConfig;
      case 'staging':
        return {
          'lazy_loading': true,
          'image_caching': true,
          'network_caching': false,
          'memory_optimization': false,
          'max_cache_size': 50 * 1024 * 1024, // 50MB
        };
      default:
        return {
          'lazy_loading': false,
          'image_caching': false,
          'network_caching': false,
          'memory_optimization': false,
          'max_cache_size': 20 * 1024 * 1024, // 20MB
        };
    }
  }
  
  /// 获取日志配置
  Map<String, dynamic> get loggingConfig {
    switch (_currentEnvironment) {
      case 'production':
        return ProductionConfig.loggingConfig;
      case 'staging':
        return {
          'level': 'INFO',
          'remote_logging': true,
          'crash_reporting': true,
          'enable_console': true,
          'enable_file': true,
        };
      default:
        return {
          'level': 'DEBUG',
          'remote_logging': false,
          'crash_reporting': false,
          'enable_console': true,
          'enable_file': false,
        };
    }
  }
  
  /// 获取数据库配置
  Map<String, dynamic> get databaseConfig {
    switch (_currentEnvironment) {
      case 'production':
        return ProductionConfig.databaseConfig;
      case 'staging':
        return {
          'name': 'app_staging.db',
          'enable_logging': true,
          'encryption_enabled': true,
        };
      default:
        return {
          'name': 'app_dev.db',
          'enable_logging': true,
          'encryption_enabled': false,
        };
    }
  }
  
  /// 是否启用调试功能
  bool get isDebugEnabled {
    return !isProduction;
  }
  
  /// 是否启用分析
  bool get isAnalyticsEnabled {
    return isProduction || isStaging;
  }
  
  /// 是否启用崩溃报告
  bool get isCrashReportingEnabled {
    return isProduction || isStaging;
  }
  
  /// 验证当前环境配置
  bool validateCurrentConfig() {
    switch (_currentEnvironment) {
      case 'production':
        return ProductionConfig.validateConfig();
      default:
        return true; // 开发和测试环境不需要严格验证
    }
  }
  
  /// 获取环境信息
  Map<String, dynamic> get environmentInfo {
    return {
      'environment': _currentEnvironment,
      'is_production': isProduction,
      'is_development': isDevelopment,
      'is_staging': isStaging,
      'debug_enabled': isDebugEnabled,
      'analytics_enabled': isAnalyticsEnabled,
      'crash_reporting_enabled': isCrashReportingEnabled,
      'api_base_url': apiBaseUrl,
      'api_timeout': apiTimeout,
    };
  }
  
  /// 获取测试环境功能开关
  Map<String, bool> _getStagingFeatureFlags() {
    return {
      'authentication': true,
      'network': true,
      'database': true,
      'state_management': true,
      'error_handling': true,
      'design_system': true,
      'theming': true,
      'internationalization': true,
      'routing': true,
      'performance_monitoring': true,
      'analytics': true,
      'push_notifications': false,
      'offline_support': false,
      'security_enhanced': false,
      'dev_tools': true,
      'debug_features': true,
    };
  }
  
  /// 获取开发环境功能开关
  Map<String, bool> _getDevelopmentFeatureFlags() {
    return {
      'authentication': true,
      'network': true,
      'database': true,
      'state_management': true,
      'error_handling': true,
      'design_system': true,
      'theming': true,
      'internationalization': true,
      'routing': true,
      'performance_monitoring': false,
      'analytics': false,
      'push_notifications': false,
      'offline_support': false,
      'security_enhanced': false,
      'dev_tools': true,
      'debug_features': true,
    };
  }
}
