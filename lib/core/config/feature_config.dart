import 'dart:async';
import 'package:flutter/foundation.dart';
import '../constants/feature_constants.dart';

/// 功能配置管理器
/// 
/// 负责管理应用中所有功能模块的启用/禁用状态
/// 支持运行时配置和依赖关系验证
class FeatureConfig {
  static FeatureConfig? _instance;
  static FeatureConfig get instance {
    if (_instance == null) {
      throw StateError('FeatureConfig has not been initialized. Call FeatureConfig.initialize() first.');
    }
    return _instance!;
  }

  FeatureConfig._(this._config, this._dependencies);

  final Map<String, dynamic> _config;
  final Map<String, List<String>> _dependencies;
  final Map<String, bool> _featureCache = {};
  final StreamController<FeatureConfigChangeEvent> _changeController = 
      StreamController<FeatureConfigChangeEvent>.broadcast();

  /// 功能配置变更事件流
  Stream<FeatureConfigChangeEvent> get onConfigChanged => _changeController.stream;

  /// 初始化功能配置
  static Future<void> initialize(Map<String, dynamic> config) async {
    final features = config['features'] as Map<String, dynamic>? ?? {};
    final dependencies = config['dependencies'] as Map<String, dynamic>? ?? {};
    
    // 转换依赖关系格式
    final dependencyMap = <String, List<String>>{};
    dependencies.forEach((key, value) {
      if (value is Map && value.containsKey('requires')) {
        final requires = value['requires'];
        if (requires is List) {
          dependencyMap[key] = requires.cast<String>();
        }
      }
    });

    _instance = FeatureConfig._(features, dependencyMap);
    
    // 验证配置
    await _instance!._validateConfiguration();
    
    if (kDebugMode) {
      _instance!._logConfiguration();
    }
  }

  /// 检查功能是否启用
  bool isFeatureEnabled(String featureName) {
    // 检查缓存
    if (_featureCache.containsKey(featureName)) {
      return _featureCache[featureName]!;
    }

    // 获取功能配置
    final isEnabled = _config[featureName] as bool? ?? false;
    
    // 检查依赖关系
    if (isEnabled && _dependencies.containsKey(featureName)) {
      final requiredFeatures = _dependencies[featureName]!;
      for (final requiredFeature in requiredFeatures) {
        if (!isFeatureEnabled(requiredFeature)) {
          // 依赖的功能未启用，当前功能也不能启用
          _featureCache[featureName] = false;
          return false;
        }
      }
    }

    // 缓存结果
    _featureCache[featureName] = isEnabled;
    return isEnabled;
  }

  /// 动态启用功能
  Future<bool> enableFeature(String featureName) async {
    if (isFeatureEnabled(featureName)) {
      return true; // 已经启用
    }

    // 检查依赖关系
    if (_dependencies.containsKey(featureName)) {
      final requiredFeatures = _dependencies[featureName]!;
      for (final requiredFeature in requiredFeatures) {
        if (!isFeatureEnabled(requiredFeature)) {
          if (kDebugMode) {
            print('Cannot enable $featureName: required feature $requiredFeature is not enabled');
          }
          return false;
        }
      }
    }

    // 启用功能
    _config[featureName] = true;
    _featureCache[featureName] = true;

    // 发送变更事件
    _changeController.add(FeatureConfigChangeEvent(
      featureName: featureName,
      isEnabled: true,
      changeType: FeatureChangeType.enabled,
    ));

    return true;
  }

  /// 动态禁用功能
  Future<bool> disableFeature(String featureName) async {
    if (!isFeatureEnabled(featureName)) {
      return true; // 已经禁用
    }

    // 检查是否有其他功能依赖此功能
    final dependentFeatures = _getDependentFeatures(featureName);
    if (dependentFeatures.isNotEmpty) {
      if (kDebugMode) {
        print('Cannot disable $featureName: features $dependentFeatures depend on it');
      }
      return false;
    }

    // 禁用功能
    _config[featureName] = false;
    _featureCache[featureName] = false;

    // 发送变更事件
    _changeController.add(FeatureConfigChangeEvent(
      featureName: featureName,
      isEnabled: false,
      changeType: FeatureChangeType.disabled,
    ));

    return true;
  }

  /// 获取所有启用的功能
  List<String> getEnabledFeatures() {
    return _config.entries
        .where((entry) => entry.value == true && isFeatureEnabled(entry.key))
        .map((entry) => entry.key)
        .toList();
  }

  /// 获取所有禁用的功能
  List<String> getDisabledFeatures() {
    return Features.allFeatures
        .where((feature) => !isFeatureEnabled(feature))
        .toList();
  }

  /// 获取功能依赖关系
  List<String> getFeatureDependencies(String featureName) {
    return _dependencies[featureName] ?? [];
  }

  /// 获取依赖某个功能的其他功能
  List<String> _getDependentFeatures(String featureName) {
    final dependentFeatures = <String>[];
    _dependencies.forEach((feature, dependencies) {
      if (dependencies.contains(featureName) && isFeatureEnabled(feature)) {
        dependentFeatures.add(feature);
      }
    });
    return dependentFeatures;
  }



  /// 验证配置
  Future<void> _validateConfiguration() async {
    final errors = <String>[];

    // 检查循环依赖
    for (final feature in _dependencies.keys) {
      if (_hasCircularDependency(feature, [])) {
        errors.add('Circular dependency detected for feature: $feature');
      }
    }

    // 检查依赖的功能是否存在
    _dependencies.forEach((feature, dependencies) {
      for (final dependency in dependencies) {
        if (!Features.allFeatures.contains(dependency)) {
          errors.add('Feature $feature depends on unknown feature: $dependency');
        }
      }
    });

    if (errors.isNotEmpty) {
      throw FeatureConfigurationException(
        'Feature configuration validation failed:\n${errors.join('\n')}',
      );
    }
  }

  /// 检查循环依赖
  bool _hasCircularDependency(String feature, List<String> visited) {
    if (visited.contains(feature)) {
      return true;
    }

    final dependencies = _dependencies[feature] ?? [];
    final newVisited = [...visited, feature];

    for (final dependency in dependencies) {
      if (_hasCircularDependency(dependency, newVisited)) {
        return true;
      }
    }

    return false;
  }

  /// 记录配置信息
  void _logConfiguration() {
    print('=== Feature Configuration ===');
    print('Enabled features: ${getEnabledFeatures()}');
    print('Disabled features: ${getDisabledFeatures()}');
    print('Dependencies: $_dependencies');
    print('=============================');
  }

  /// 清理资源
  void dispose() {
    _changeController.close();
    _featureCache.clear();
  }
}

/// 功能配置变更事件
class FeatureConfigChangeEvent {
  const FeatureConfigChangeEvent({
    required this.featureName,
    required this.isEnabled,
    required this.changeType,
  });

  final String featureName;
  final bool isEnabled;
  final FeatureChangeType changeType;
}

/// 功能变更类型
enum FeatureChangeType {
  enabled,
  disabled,
}

/// 功能配置异常
class FeatureConfigurationException implements Exception {
  const FeatureConfigurationException(this.message);
  
  final String message;
  
  @override
  String toString() => 'FeatureConfigurationException: $message';
}
