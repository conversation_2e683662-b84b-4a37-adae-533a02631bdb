/// 应用配置管理
/// 
/// 提供应用级别的配置管理功能，包括基础配置和环境配置
library;

import 'package:flutter/services.dart';
import 'package:yaml/yaml.dart';

/// 应用配置管理类
class AppConfig {
  static AppConfig? _instance;
  static AppConfig get instance => _instance ??= AppConfig._();
  
  AppConfig._();
  
  late Map<String, dynamic> _config;
  bool _initialized = false;
  
  /// 初始化配置
  Future<void> initialize({String? environment}) async {
    if (_initialized) return;
    
    // 加载基础配置
    final baseConfigString = await rootBundle.loadString('assets/config/app_config.yaml');
    _config = Map<String, dynamic>.from(loadYaml(baseConfigString));
    
    // 如果指定了环境，加载环境特定配置
    if (environment != null) {
      try {
        final envConfigString = await rootBundle.loadString('assets/config/app_config.$environment.yaml');
        final envConfig = loadYaml(envConfigString) as Map<String, dynamic>;
        _mergeConfig(envConfig);
      } catch (e) {
        // 环境配置文件不存在时忽略错误
      }
    }
    
    _initialized = true;
  }
  
  /// 合并配置
  void _mergeConfig(Map<String, dynamic> envConfig) {
    for (final entry in envConfig.entries) {
      if (entry.value is Map && _config[entry.key] is Map) {
        final baseMap = _config[entry.key] as Map<String, dynamic>;
        final envMap = entry.value as Map<String, dynamic>;
        baseMap.addAll(envMap);
      } else {
        _config[entry.key] = entry.value;
      }
    }
  }
  
  /// 获取应用名称
  String get appName => _config['app']?['name'] ?? 'Flutter Enterprise App';
  
  /// 获取应用版本
  String get appVersion => _config['app']?['version'] ?? '1.0.0';
  
  /// 获取构建号
  int get buildNumber => _config['app']?['build_number'] ?? 1;
  
  /// 获取配置值
  T? getValue<T>(String key) {
    final keys = key.split('.');
    dynamic value = _config;
    
    for (final k in keys) {
      if (value is Map<String, dynamic>) {
        value = value[k];
      } else {
        return null;
      }
    }
    
    return value as T?;
  }
  
  /// 获取默认配置
  String get defaultTheme => getValue<String>('defaults.theme') ?? 'system';
  String get defaultLanguage => getValue<String>('defaults.language') ?? 'zh_CN';
  String get defaultLogLevel => getValue<String>('defaults.log_level') ?? 'info';
  
  /// 检查是否已初始化
  bool get isInitialized => _initialized;
}
