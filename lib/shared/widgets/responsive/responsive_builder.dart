import 'package:flutter/material.dart';
import '../../../core/responsive/breakpoints.dart';

/// 响应式构建器
/// 
/// 根据不同的屏幕尺寸构建不同的UI布局
class ResponsiveBuilder extends StatelessWidget {
  /// 移动端构建器（必需）
  final Widget Function(BuildContext context, BoxConstraints constraints) mobile;
  
  /// 平板端构建器（可选，默认使用移动端）
  final Widget Function(BuildContext context, BoxConstraints constraints)? tablet;
  
  /// 桌面端构建器（可选，默认使用平板端或移动端）
  final Widget Function(BuildContext context, BoxConstraints constraints)? desktop;
  
  /// 大屏桌面端构建器（可选，默认使用桌面端）
  final Widget Function(BuildContext context, BoxConstraints constraints)? largeDesktop;

  const ResponsiveBuilder({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
    this.largeDesktop,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final deviceType = _getDeviceType(constraints.maxWidth);
        
        switch (deviceType) {
          case DeviceType.largeDesktop:
            return (largeDesktop ?? desktop ?? tablet ?? mobile)(context, constraints);
          case DeviceType.desktop:
            return (desktop ?? tablet ?? mobile)(context, constraints);
          case DeviceType.tablet:
            return (tablet ?? mobile)(context, constraints);
          case DeviceType.mobile:
            return mobile(context, constraints);
        }
      },
    );
  }

  /// 根据宽度获取设备类型
  DeviceType _getDeviceType(double width) {
    if (width >= Breakpoints.largeDesktop) {
      return DeviceType.largeDesktop;
    } else if (width >= Breakpoints.desktop) {
      return DeviceType.desktop;
    } else if (width >= Breakpoints.mobile) {
      return DeviceType.tablet;
    } else {
      return DeviceType.mobile;
    }
  }
}

/// 响应式值构建器
/// 
/// 根据屏幕尺寸返回不同的值
class ResponsiveValue<T> extends StatelessWidget {
  /// 移动端值
  final T mobile;
  
  /// 平板端值
  final T? tablet;
  
  /// 桌面端值
  final T? desktop;
  
  /// 大屏桌面端值
  final T? largeDesktop;
  
  /// 构建器函数
  final Widget Function(BuildContext context, T value) builder;

  const ResponsiveValue({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
    this.largeDesktop,
    required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    final value = Breakpoints.responsive<T>(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
      largeDesktop: largeDesktop,
    );
    
    return builder(context, value);
  }
}

/// 响应式网格
/// 
/// 根据屏幕尺寸自动调整列数的网格布局
class ResponsiveGrid extends StatelessWidget {
  /// 子组件列表
  final List<Widget> children;
  
  /// 移动端列数
  final int mobileColumns;
  
  /// 平板端列数
  final int tabletColumns;
  
  /// 桌面端列数
  final int desktopColumns;
  
  /// 大屏桌面端列数
  final int largeDesktopColumns;
  
  /// 主轴间距
  final double mainAxisSpacing;
  
  /// 交叉轴间距
  final double crossAxisSpacing;
  
  /// 子组件宽高比
  final double childAspectRatio;
  
  /// 内边距
  final EdgeInsetsGeometry? padding;

  const ResponsiveGrid({
    super.key,
    required this.children,
    this.mobileColumns = 1,
    this.tabletColumns = 2,
    this.desktopColumns = 3,
    this.largeDesktopColumns = 4,
    this.mainAxisSpacing = 8.0,
    this.crossAxisSpacing = 8.0,
    this.childAspectRatio = 1.0,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveValue<int>(
      mobile: mobileColumns,
      tablet: tabletColumns,
      desktop: desktopColumns,
      largeDesktop: largeDesktopColumns,
      builder: (context, columns) {
        return Padding(
          padding: padding ?? EdgeInsets.zero,
          child: GridView.builder(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: columns,
              mainAxisSpacing: mainAxisSpacing,
              crossAxisSpacing: crossAxisSpacing,
              childAspectRatio: childAspectRatio,
            ),
            itemCount: children.length,
            itemBuilder: (context, index) => children[index],
          ),
        );
      },
    );
  }
}

/// 响应式包装器
/// 
/// 限制内容的最大宽度，并在大屏幕上居中显示
class ResponsiveWrapper extends StatelessWidget {
  /// 子组件
  final Widget child;
  
  /// 最大宽度
  final double? maxWidth;
  
  /// 内边距
  final EdgeInsetsGeometry? padding;
  
  /// 是否居中对齐
  final bool center;

  const ResponsiveWrapper({
    super.key,
    required this.child,
    this.maxWidth,
    this.padding,
    this.center = true,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveMaxWidth = maxWidth ?? Breakpoints.getContentMaxWidth(context);
    
    Widget content = Container(
      width: double.infinity,
      constraints: BoxConstraints(maxWidth: effectiveMaxWidth),
      padding: padding,
      child: child,
    );
    
    if (center && !Breakpoints.isMobile(context)) {
      content = Center(child: content);
    }
    
    return content;
  }
}

/// 响应式侧边栏布局
/// 
/// 在大屏幕上显示侧边栏，在小屏幕上使用抽屉
class ResponsiveSidebarLayout extends StatelessWidget {
  /// 主要内容
  final Widget body;
  
  /// 侧边栏内容
  final Widget sidebar;
  
  /// AppBar
  final PreferredSizeWidget? appBar;
  
  /// 浮动操作按钮
  final Widget? floatingActionButton;
  
  /// 底部导航栏
  final Widget? bottomNavigationBar;
  
  /// 抽屉标题
  final String? drawerTitle;

  const ResponsiveSidebarLayout({
    super.key,
    required this.body,
    required this.sidebar,
    this.appBar,
    this.floatingActionButton,
    this.bottomNavigationBar,
    this.drawerTitle,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      mobile: (context, constraints) => Scaffold(
        appBar: appBar,
        drawer: Drawer(
          child: Column(
            children: [
              if (drawerTitle != null)
                DrawerHeader(
                  child: Text(
                    drawerTitle!,
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                ),
              Expanded(child: sidebar),
            ],
          ),
        ),
        body: body,
        floatingActionButton: floatingActionButton,
        bottomNavigationBar: bottomNavigationBar,
      ),
      tablet: (context, constraints) => Scaffold(
        appBar: appBar,
        body: Row(
          children: [
            SizedBox(
              width: Breakpoints.getSidebarWidth(context),
              child: sidebar,
            ),
            const VerticalDivider(width: 1),
            Expanded(child: body),
          ],
        ),
        floatingActionButton: floatingActionButton,
        bottomNavigationBar: bottomNavigationBar,
      ),
    );
  }
}

/// 响应式列布局
/// 
/// 在小屏幕上垂直排列，在大屏幕上水平排列
class ResponsiveRow extends StatelessWidget {
  /// 子组件列表
  final List<Widget> children;
  
  /// 主轴对齐方式
  final MainAxisAlignment mainAxisAlignment;
  
  /// 交叉轴对齐方式
  final CrossAxisAlignment crossAxisAlignment;
  
  /// 主轴尺寸
  final MainAxisSize mainAxisSize;
  
  /// 间距
  final double spacing;
  
  /// 断点（小于此宽度时垂直排列）
  final double breakpoint;

  const ResponsiveRow({
    super.key,
    required this.children,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.mainAxisSize = MainAxisSize.max,
    this.spacing = 8.0,
    this.breakpoint = 600,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth < breakpoint) {
          // 小屏幕：垂直排列
          return Column(
            mainAxisAlignment: mainAxisAlignment,
            crossAxisAlignment: crossAxisAlignment,
            mainAxisSize: mainAxisSize,
            children: _addSpacing(children, isVertical: true),
          );
        } else {
          // 大屏幕：水平排列
          return Row(
            mainAxisAlignment: mainAxisAlignment,
            crossAxisAlignment: crossAxisAlignment,
            mainAxisSize: mainAxisSize,
            children: _addSpacing(children, isVertical: false),
          );
        }
      },
    );
  }

  /// 添加间距
  List<Widget> _addSpacing(List<Widget> children, {required bool isVertical}) {
    if (children.isEmpty) return children;
    
    final spacedChildren = <Widget>[];
    for (int i = 0; i < children.length; i++) {
      spacedChildren.add(children[i]);
      if (i < children.length - 1) {
        spacedChildren.add(
          isVertical 
            ? SizedBox(height: spacing)
            : SizedBox(width: spacing),
        );
      }
    }
    return spacedChildren;
  }
}
