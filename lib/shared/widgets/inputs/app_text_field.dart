import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/design/design_tokens.dart';

/// 应用文本输入框组件
/// 
/// 统一的文本输入框组件，支持多种类型和验证
class AppTextField extends StatefulWidget {
  /// 标签文本
  final String? label;
  
  /// 提示文本
  final String? hint;
  
  /// 帮助文本
  final String? helperText;
  
  /// 错误文本
  final String? errorText;
  
  /// 控制器
  final TextEditingController? controller;
  
  /// 初始值
  final String? initialValue;
  
  /// 是否隐藏文本（密码输入）
  final bool obscureText;
  
  /// 键盘类型
  final TextInputType keyboardType;
  
  /// 文本输入动作
  final TextInputAction? textInputAction;
  
  /// 验证器
  final String? Function(String?)? validator;
  
  /// 输入变化回调
  final void Function(String)? onChanged;
  
  /// 提交回调
  final void Function(String)? onSubmitted;
  
  /// 焦点变化回调
  final void Function(bool)? onFocusChanged;
  
  /// 是否只读
  final bool readOnly;
  
  /// 是否启用
  final bool enabled;
  
  /// 最大行数
  final int? maxLines;
  
  /// 最小行数
  final int? minLines;
  
  /// 最大长度
  final int? maxLength;
  
  /// 输入格式化器
  final List<TextInputFormatter>? inputFormatters;
  
  /// 前缀图标
  final Widget? prefixIcon;
  
  /// 后缀图标
  final Widget? suffixIcon;
  
  /// 是否显示计数器
  final bool showCounter;
  
  /// 是否必填
  final bool required;
  
  /// 自动焦点
  final bool autofocus;
  
  /// 自动填充提示
  final Iterable<String>? autofillHints;

  const AppTextField({
    super.key,
    this.label,
    this.hint,
    this.helperText,
    this.errorText,
    this.controller,
    this.initialValue,
    this.obscureText = false,
    this.keyboardType = TextInputType.text,
    this.textInputAction,
    this.validator,
    this.onChanged,
    this.onSubmitted,
    this.onFocusChanged,
    this.readOnly = false,
    this.enabled = true,
    this.maxLines = 1,
    this.minLines,
    this.maxLength,
    this.inputFormatters,
    this.prefixIcon,
    this.suffixIcon,
    this.showCounter = false,
    this.required = false,
    this.autofocus = false,
    this.autofillHints,
  });

  /// 创建密码输入框
  const AppTextField.password({
    super.key,
    this.label,
    this.hint,
    this.helperText,
    this.errorText,
    this.controller,
    this.initialValue,
    this.validator,
    this.onChanged,
    this.onSubmitted,
    this.onFocusChanged,
    this.readOnly = false,
    this.enabled = true,
    this.maxLength,
    this.inputFormatters,
    this.prefixIcon,
    this.required = false,
    this.autofocus = false,
  }) : obscureText = true,
       keyboardType = TextInputType.visiblePassword,
       textInputAction = TextInputAction.done,
       maxLines = 1,
       minLines = null,
       suffixIcon = null,
       showCounter = false,
       autofillHints = const [AutofillHints.password];

  /// 创建邮箱输入框
  const AppTextField.email({
    super.key,
    this.label,
    this.hint,
    this.helperText,
    this.errorText,
    this.controller,
    this.initialValue,
    this.validator,
    this.onChanged,
    this.onSubmitted,
    this.onFocusChanged,
    this.readOnly = false,
    this.enabled = true,
    this.maxLength,
    this.inputFormatters,
    this.prefixIcon,
    this.suffixIcon,
    this.required = false,
    this.autofocus = false,
  }) : obscureText = false,
       keyboardType = TextInputType.emailAddress,
       textInputAction = TextInputAction.next,
       maxLines = 1,
       minLines = null,
       showCounter = false,
       autofillHints = const [AutofillHints.email];

  /// 创建电话输入框
  const AppTextField.phone({
    super.key,
    this.label,
    this.hint,
    this.helperText,
    this.errorText,
    this.controller,
    this.initialValue,
    this.validator,
    this.onChanged,
    this.onSubmitted,
    this.onFocusChanged,
    this.readOnly = false,
    this.enabled = true,
    this.maxLength,
    this.inputFormatters,
    this.prefixIcon,
    this.suffixIcon,
    this.required = false,
    this.autofocus = false,
  }) : obscureText = false,
       keyboardType = TextInputType.phone,
       textInputAction = TextInputAction.done,
       maxLines = 1,
       minLines = null,
       showCounter = false,
       autofillHints = const [AutofillHints.telephoneNumber];

  /// 创建多行文本输入框
  const AppTextField.multiline({
    super.key,
    this.label,
    this.hint,
    this.helperText,
    this.errorText,
    this.controller,
    this.initialValue,
    this.validator,
    this.onChanged,
    this.onSubmitted,
    this.onFocusChanged,
    this.readOnly = false,
    this.enabled = true,
    this.maxLines = 5,
    this.minLines = 3,
    this.maxLength,
    this.inputFormatters,
    this.prefixIcon,
    this.suffixIcon,
    this.showCounter = true,
    this.required = false,
    this.autofocus = false,
  }) : obscureText = false,
       keyboardType = TextInputType.multiline,
       textInputAction = TextInputAction.newline,
       autofillHints = null;

  @override
  State<AppTextField> createState() => _AppTextFieldState();
}

class _AppTextFieldState extends State<AppTextField> {
  late FocusNode _focusNode;
  bool _obscureText = false;
  bool _showPasswordToggle = false;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
    _focusNode.addListener(_onFocusChanged);
    _obscureText = widget.obscureText;
    _showPasswordToggle = widget.obscureText;
  }

  @override
  void dispose() {
    _focusNode.removeListener(_onFocusChanged);
    _focusNode.dispose();
    super.dispose();
  }

  void _onFocusChanged() {
    widget.onFocusChanged?.call(_focusNode.hasFocus);
  }

  void _togglePasswordVisibility() {
    setState(() {
      _obscureText = !_obscureText;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.label != null) ...[
          _buildLabel(context),
          const SizedBox(height: 8),
        ],
        _buildTextField(context),
        if (widget.helperText != null && widget.errorText == null) ...[
          const SizedBox(height: 4),
          _buildHelperText(context),
        ],
      ],
    );
  }

  /// 构建标签
  Widget _buildLabel(BuildContext context) {
    final theme = Theme.of(context);
    
    return RichText(
      text: TextSpan(
        text: widget.label!,
        style: theme.textTheme.labelMedium?.copyWith(
          color: theme.colorScheme.onSurface,
        ),
        children: [
          if (widget.required)
            TextSpan(
              text: ' *',
              style: TextStyle(
                color: theme.colorScheme.error,
              ),
            ),
        ],
      ),
    );
  }

  /// 构建文本输入框
  Widget _buildTextField(BuildContext context) {
    return TextFormField(
      controller: widget.controller,
      initialValue: widget.initialValue,
      focusNode: _focusNode,
      obscureText: _obscureText,
      keyboardType: widget.keyboardType,
      textInputAction: widget.textInputAction,
      validator: widget.validator,
      onChanged: widget.onChanged,
      onFieldSubmitted: widget.onSubmitted,
      readOnly: widget.readOnly,
      enabled: widget.enabled,
      maxLines: widget.maxLines,
      minLines: widget.minLines,
      maxLength: widget.maxLength,
      inputFormatters: widget.inputFormatters,
      autofocus: widget.autofocus,
      autofillHints: widget.autofillHints,
      decoration: InputDecoration(
        hintText: widget.hint,
        errorText: widget.errorText,
        prefixIcon: widget.prefixIcon,
        suffixIcon: _buildSuffixIcon(),
        counterText: widget.showCounter ? null : '',
      ),
    );
  }

  /// 构建后缀图标
  Widget? _buildSuffixIcon() {
    if (_showPasswordToggle) {
      return IconButton(
        icon: Icon(
          _obscureText ? Icons.visibility : Icons.visibility_off,
          size: DesignTokens.iconSizeM,
        ),
        onPressed: _togglePasswordVisibility,
        tooltip: _obscureText ? '显示密码' : '隐藏密码',
      );
    }
    
    return widget.suffixIcon;
  }

  /// 构建帮助文本
  Widget _buildHelperText(BuildContext context) {
    final theme = Theme.of(context);
    
    return Text(
      widget.helperText!,
      style: theme.textTheme.bodySmall?.copyWith(
        color: theme.colorScheme.onSurfaceVariant,
      ),
    );
  }
}

/// 文本输入框验证器
class AppTextFieldValidators {
  AppTextFieldValidators._();

  /// 必填验证
  static String? required(String? value, {String? message}) {
    if (value == null || value.trim().isEmpty) {
      return message ?? '此字段为必填项';
    }
    return null;
  }

  /// 邮箱验证
  static String? email(String? value, {String? message}) {
    if (value == null || value.isEmpty) return null;
    
    final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    if (!emailRegex.hasMatch(value)) {
      return message ?? '请输入有效的邮箱地址';
    }
    return null;
  }

  /// 密码验证
  static String? password(String? value, {int minLength = 6, String? message}) {
    if (value == null || value.isEmpty) return null;
    
    if (value.length < minLength) {
      return message ?? '密码长度至少为 $minLength 位';
    }
    return null;
  }

  /// 电话号码验证
  static String? phone(String? value, {String? message}) {
    if (value == null || value.isEmpty) return null;
    
    final phoneRegex = RegExp(r'^1[3-9]\d{9}$');
    if (!phoneRegex.hasMatch(value)) {
      return message ?? '请输入有效的手机号码';
    }
    return null;
  }

  /// 最小长度验证
  static String? minLength(String? value, int minLength, {String? message}) {
    if (value == null || value.isEmpty) return null;
    
    if (value.length < minLength) {
      return message ?? '长度至少为 $minLength 位';
    }
    return null;
  }

  /// 最大长度验证
  static String? maxLength(String? value, int maxLength, {String? message}) {
    if (value == null || value.isEmpty) return null;
    
    if (value.length > maxLength) {
      return message ?? '长度不能超过 $maxLength 位';
    }
    return null;
  }

  /// 数字验证
  static String? numeric(String? value, {String? message}) {
    if (value == null || value.isEmpty) return null;
    
    if (double.tryParse(value) == null) {
      return message ?? '请输入有效的数字';
    }
    return null;
  }

  /// 组合验证器
  static String? Function(String?) combine(List<String? Function(String?)> validators) {
    return (String? value) {
      for (final validator in validators) {
        final result = validator(value);
        if (result != null) return result;
      }
      return null;
    };
  }
}
