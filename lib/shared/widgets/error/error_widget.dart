import 'package:flutter/material.dart';
import '../../../core/design/design_tokens.dart';
import '../buttons/app_button.dart';

/// 错误类型
enum ErrorType {
  /// 网络错误
  network,
  
  /// 服务器错误
  server,
  
  /// 认证错误
  authentication,
  
  /// 权限错误
  permission,
  
  /// 数据错误
  data,
  
  /// 未知错误
  unknown,
  
  /// 自定义错误
  custom,
}

/// 错误严重程度
enum ErrorSeverity {
  /// 信息
  info,
  
  /// 警告
  warning,
  
  /// 错误
  error,
  
  /// 致命错误
  fatal,
}

/// 应用错误组件
/// 
/// 提供统一的错误状态显示，支持多种错误类型和自定义操作
class AppErrorWidget extends StatelessWidget {
  /// 错误类型
  final ErrorType type;
  
  /// 错误严重程度
  final ErrorSeverity severity;
  
  /// 错误标题
  final String? title;
  
  /// 错误消息
  final String message;
  
  /// 错误详情
  final String? details;
  
  /// 重试回调
  final VoidCallback? onRetry;
  
  /// 自定义操作
  final List<Widget>? actions;
  
  /// 自定义图标
  final Widget? icon;
  
  /// 是否显示详情
  final bool showDetails;
  
  /// 是否可以关闭
  final bool dismissible;
  
  /// 关闭回调
  final VoidCallback? onDismiss;

  const AppErrorWidget({
    super.key,
    this.type = ErrorType.unknown,
    this.severity = ErrorSeverity.error,
    this.title,
    required this.message,
    this.details,
    this.onRetry,
    this.actions,
    this.icon,
    this.showDetails = false,
    this.dismissible = false,
    this.onDismiss,
  });

  /// 创建网络错误组件
  const AppErrorWidget.network({
    super.key,
    this.title = '网络连接失败',
    this.message = '请检查您的网络连接并重试',
    this.details,
    this.onRetry,
    this.actions,
    this.showDetails = false,
    this.dismissible = false,
    this.onDismiss,
  }) : type = ErrorType.network,
       severity = ErrorSeverity.warning,
       icon = null;

  /// 创建服务器错误组件
  const AppErrorWidget.server({
    super.key,
    this.title = '服务器错误',
    this.message = '服务器暂时无法响应，请稍后重试',
    this.details,
    this.onRetry,
    this.actions,
    this.showDetails = false,
    this.dismissible = false,
    this.onDismiss,
  }) : type = ErrorType.server,
       severity = ErrorSeverity.error,
       icon = null;

  /// 创建认证错误组件
  const AppErrorWidget.authentication({
    super.key,
    this.title = '认证失败',
    this.message = '您的登录状态已过期，请重新登录',
    this.details,
    this.onRetry,
    this.actions,
    this.showDetails = false,
    this.dismissible = false,
    this.onDismiss,
  }) : type = ErrorType.authentication,
       severity = ErrorSeverity.warning,
       icon = null;

  /// 创建权限错误组件
  const AppErrorWidget.permission({
    super.key,
    this.title = '权限不足',
    this.message = '您没有权限执行此操作',
    this.details,
    this.onRetry,
    this.actions,
    this.showDetails = false,
    this.dismissible = true,
    this.onDismiss,
  }) : type = ErrorType.permission,
       severity = ErrorSeverity.warning,
       icon = null;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    return Container(
      padding: DesignTokens.spacingL,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 关闭按钮
          if (dismissible && onDismiss != null)
            Align(
              alignment: Alignment.topRight,
              child: IconButton(
                onPressed: onDismiss,
                icon: const Icon(Icons.close),
                iconSize: DesignTokens.iconSizeM,
              ),
            ),
          
          // 错误图标
          _buildErrorIcon(colorScheme),
          
          const SizedBox(height: 16),
          
          // 错误标题
          if (title != null)
            Text(
              title!,
              style: theme.textTheme.headlineSmall?.copyWith(
                color: _getErrorColor(colorScheme),
              ),
              textAlign: TextAlign.center,
            ),
          
          if (title != null) const SizedBox(height: 8),
          
          // 错误消息
          Text(
            message,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          
          // 错误详情
          if (showDetails && details != null) ...[
            const SizedBox(height: 16),
            _buildErrorDetails(context),
          ],
          
          const SizedBox(height: 24),
          
          // 操作按钮
          _buildActions(context),
        ],
      ),
    );
  }

  Widget _buildErrorIcon(ColorScheme colorScheme) {
    if (icon != null) return icon!;
    
    IconData iconData;
    switch (type) {
      case ErrorType.network:
        iconData = Icons.wifi_off;
        break;
      case ErrorType.server:
        iconData = Icons.cloud_off;
        break;
      case ErrorType.authentication:
        iconData = Icons.lock_outline;
        break;
      case ErrorType.permission:
        iconData = Icons.block;
        break;
      case ErrorType.data:
        iconData = Icons.error_outline;
        break;
      default:
        iconData = Icons.error_outline;
    }
    
    return Icon(
      iconData,
      size: 64,
      color: _getErrorColor(colorScheme),
    );
  }

  Widget _buildErrorDetails(BuildContext context) {
    return ExpansionTile(
      title: const Text('查看详情'),
      children: [
        Container(
          width: double.infinity,
          padding: DesignTokens.spacingM,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
            borderRadius: DesignTokens.radiusS,
          ),
          child: Text(
            details!,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontFamily: 'monospace',
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActions(BuildContext context) {
    final defaultActions = <Widget>[];
    
    // 重试按钮
    if (onRetry != null) {
      defaultActions.add(
        AppButton.primary(
          text: '重试',
          onPressed: onRetry,
          icon: const Icon(Icons.refresh),
        ),
      );
    }
    
    // 自定义操作
    if (actions != null) {
      defaultActions.addAll(actions!);
    }
    
    if (defaultActions.isEmpty) return const SizedBox.shrink();
    
    if (defaultActions.length == 1) {
      return defaultActions.first;
    }
    
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      alignment: WrapAlignment.center,
      children: defaultActions,
    );
  }

  Color _getErrorColor(ColorScheme colorScheme) {
    switch (severity) {
      case ErrorSeverity.info:
        return colorScheme.primary;
      case ErrorSeverity.warning:
        return Colors.orange;
      case ErrorSeverity.error:
        return colorScheme.error;
      case ErrorSeverity.fatal:
        return Colors.red.shade700;
    }
  }
}

/// 错误边界组件
class ErrorBoundary extends StatefulWidget {
  /// 子组件
  final Widget child;
  
  /// 错误回调
  final void Function(Object error, StackTrace stackTrace)? onError;
  
  /// 错误组件构建器
  final Widget Function(Object error, StackTrace stackTrace)? errorBuilder;

  const ErrorBoundary({
    super.key,
    required this.child,
    this.onError,
    this.errorBuilder,
  });

  @override
  State<ErrorBoundary> createState() => _ErrorBoundaryState();
}

class _ErrorBoundaryState extends State<ErrorBoundary> {
  Object? _error;
  StackTrace? _stackTrace;

  @override
  Widget build(BuildContext context) {
    if (_error != null) {
      return widget.errorBuilder?.call(_error!, _stackTrace!) ??
          AppErrorWidget(
            type: ErrorType.unknown,
            severity: ErrorSeverity.fatal,
            title: '应用错误',
            message: '应用遇到了一个意外错误',
            details: _error.toString(),
            showDetails: true,
            onRetry: () {
              setState(() {
                _error = null;
                _stackTrace = null;
              });
            },
          );
    }
    
    return widget.child;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    ErrorWidget.builder = (FlutterErrorDetails details) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            _error = details.exception;
            _stackTrace = details.stack;
          });
          widget.onError?.call(details.exception, details.stack ?? StackTrace.empty);
        }
      });
      return const SizedBox.shrink();
    };
  }
}

/// 空状态组件
class EmptyStateWidget extends StatelessWidget {
  /// 图标
  final Widget? icon;
  
  /// 标题
  final String title;
  
  /// 描述
  final String? description;
  
  /// 操作按钮
  final Widget? action;

  const EmptyStateWidget({
    super.key,
    this.icon,
    required this.title,
    this.description,
    this.action,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    return Center(
      child: Padding(
        padding: DesignTokens.spacingL,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            icon ??
                Icon(
                  Icons.inbox_outlined,
                  size: 64,
                  color: colorScheme.onSurfaceVariant,
                ),
            const SizedBox(height: 16),
            Text(
              title,
              style: theme.textTheme.headlineSmall?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            if (description != null) ...[
              const SizedBox(height: 8),
              Text(
                description!,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
            ],
            if (action != null) ...[
              const SizedBox(height: 24),
              action!,
            ],
          ],
        ),
      ),
    );
  }
}

/// 状态构建器
class StateBuilder<T> extends StatelessWidget {
  /// 状态值
  final T? state;
  
  /// 是否加载中
  final bool isLoading;
  
  /// 错误信息
  final Object? error;
  
  /// 加载组件构建器
  final Widget Function()? loadingBuilder;
  
  /// 错误组件构建器
  final Widget Function(Object error)? errorBuilder;
  
  /// 空状态组件构建器
  final Widget Function()? emptyBuilder;
  
  /// 数据组件构建器
  final Widget Function(T data) dataBuilder;
  
  /// 空状态检查器
  final bool Function(T data)? isEmptyChecker;

  const StateBuilder({
    super.key,
    this.state,
    this.isLoading = false,
    this.error,
    this.loadingBuilder,
    this.errorBuilder,
    this.emptyBuilder,
    required this.dataBuilder,
    this.isEmptyChecker,
  });

  @override
  Widget build(BuildContext context) {
    // 错误状态
    if (error != null) {
      return errorBuilder?.call(error!) ??
          AppErrorWidget(
            message: error.toString(),
            onRetry: () {
              // TODO: 实现重试逻辑
            },
          );
    }
    
    // 加载状态
    if (isLoading) {
      return loadingBuilder?.call() ??
          const Center(child: CircularProgressIndicator());
    }
    
    // 空状态
    if (state == null || (isEmptyChecker?.call(state!) ?? false)) {
      return emptyBuilder?.call() ??
          const EmptyStateWidget(
            title: '暂无数据',
            description: '当前没有可显示的内容',
          );
    }
    
    // 数据状态
    return dataBuilder(state!);
  }
}
