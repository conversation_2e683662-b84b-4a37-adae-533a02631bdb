import 'package:flutter/material.dart';
import '../../../core/design/design_tokens.dart';

/// 应用按钮类型
enum AppButtonType {
  /// 主要按钮
  primary,
  
  /// 次要按钮
  secondary,
  
  /// 轮廓按钮
  outlined,
  
  /// 文本按钮
  text,
  
  /// 危险按钮
  danger,
}

/// 应用按钮尺寸
enum AppButtonSize {
  /// 小尺寸
  small,
  
  /// 中等尺寸
  medium,
  
  /// 大尺寸
  large,
}

/// 应用按钮组件
/// 
/// 统一的按钮组件，支持多种类型、尺寸和状态
class AppButton extends StatelessWidget {
  /// 按钮文本
  final String text;
  
  /// 点击回调
  final VoidCallback? onPressed;
  
  /// 按钮类型
  final AppButtonType type;
  
  /// 按钮尺寸
  final AppButtonSize size;
  
  /// 是否加载中
  final bool isLoading;
  
  /// 是否禁用
  final bool isDisabled;
  
  /// 图标
  final Widget? icon;
  
  /// 图标位置（在文本前还是后）
  final bool iconAfterText;
  
  /// 自定义宽度
  final double? width;
  
  /// 是否全宽
  final bool fullWidth;

  const AppButton({
    super.key,
    required this.text,
    this.onPressed,
    this.type = AppButtonType.primary,
    this.size = AppButtonSize.medium,
    this.isLoading = false,
    this.isDisabled = false,
    this.icon,
    this.iconAfterText = false,
    this.width,
    this.fullWidth = false,
  });

  /// 创建主要按钮
  const AppButton.primary({
    super.key,
    required this.text,
    this.onPressed,
    this.size = AppButtonSize.medium,
    this.isLoading = false,
    this.isDisabled = false,
    this.icon,
    this.iconAfterText = false,
    this.width,
    this.fullWidth = false,
  }) : type = AppButtonType.primary;

  /// 创建次要按钮
  const AppButton.secondary({
    super.key,
    required this.text,
    this.onPressed,
    this.size = AppButtonSize.medium,
    this.isLoading = false,
    this.isDisabled = false,
    this.icon,
    this.iconAfterText = false,
    this.width,
    this.fullWidth = false,
  }) : type = AppButtonType.secondary;

  /// 创建轮廓按钮
  const AppButton.outlined({
    super.key,
    required this.text,
    this.onPressed,
    this.size = AppButtonSize.medium,
    this.isLoading = false,
    this.isDisabled = false,
    this.icon,
    this.iconAfterText = false,
    this.width,
    this.fullWidth = false,
  }) : type = AppButtonType.outlined;

  /// 创建文本按钮
  const AppButton.text({
    super.key,
    required this.text,
    this.onPressed,
    this.size = AppButtonSize.medium,
    this.isLoading = false,
    this.isDisabled = false,
    this.icon,
    this.iconAfterText = false,
    this.width,
    this.fullWidth = false,
  }) : type = AppButtonType.text;

  /// 创建危险按钮
  const AppButton.danger({
    super.key,
    required this.text,
    this.onPressed,
    this.size = AppButtonSize.medium,
    this.isLoading = false,
    this.isDisabled = false,
    this.icon,
    this.iconAfterText = false,
    this.width,
    this.fullWidth = false,
  }) : type = AppButtonType.danger;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    // 确定是否禁用
    final effectivelyDisabled = isDisabled || isLoading || onPressed == null;
    
    // 获取按钮样式
    final buttonStyle = _getButtonStyle(context, colorScheme);
    
    // 构建按钮内容
    Widget content = _buildContent(context);
    
    // 构建按钮
    Widget button = _buildButton(context, buttonStyle, content, effectivelyDisabled);
    
    // 应用宽度约束
    if (fullWidth) {
      button = SizedBox(width: double.infinity, child: button);
    } else if (width != null) {
      button = SizedBox(width: width, child: button);
    }
    
    return button;
  }

  /// 构建按钮
  Widget _buildButton(
    BuildContext context,
    ButtonStyle style,
    Widget content,
    bool disabled,
  ) {
    final callback = disabled ? null : onPressed;
    
    switch (type) {
      case AppButtonType.primary:
      case AppButtonType.danger:
        return ElevatedButton(
          onPressed: callback,
          style: style,
          child: content,
        );
      case AppButtonType.secondary:
        return FilledButton.tonal(
          onPressed: callback,
          style: style,
          child: content,
        );
      case AppButtonType.outlined:
        return OutlinedButton(
          onPressed: callback,
          style: style,
          child: content,
        );
      case AppButtonType.text:
        return TextButton(
          onPressed: callback,
          style: style,
          child: content,
        );
    }
  }

  /// 构建按钮内容
  Widget _buildContent(BuildContext context) {
    if (isLoading) {
      return _buildLoadingContent(context);
    }
    
    if (icon == null) {
      return Text(text);
    }
    
    // 有图标的情况
    final iconSize = _getIconSize();
    final spacing = _getIconSpacing();
    
    final iconWidget = SizedBox(
      width: iconSize,
      height: iconSize,
      child: icon,
    );
    
    final textWidget = Text(text);
    
    if (iconAfterText) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          textWidget,
          SizedBox(width: spacing),
          iconWidget,
        ],
      );
    } else {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          iconWidget,
          SizedBox(width: spacing),
          textWidget,
        ],
      );
    }
  }

  /// 构建加载中内容
  Widget _buildLoadingContent(BuildContext context) {
    final indicatorSize = _getLoadingIndicatorSize();
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: indicatorSize,
          height: indicatorSize,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              _getLoadingIndicatorColor(context),
            ),
          ),
        ),
        const SizedBox(width: 8),
        Text(text),
      ],
    );
  }

  /// 获取按钮样式
  ButtonStyle _getButtonStyle(BuildContext context, ColorScheme colorScheme) {
    final baseStyle = _getBaseButtonStyle(context, colorScheme);
    final sizeStyle = _getSizeButtonStyle();
    
    return baseStyle.merge(sizeStyle);
  }

  /// 获取基础按钮样式
  ButtonStyle _getBaseButtonStyle(BuildContext context, ColorScheme colorScheme) {
    switch (type) {
      case AppButtonType.primary:
        return ElevatedButton.styleFrom(
          backgroundColor: colorScheme.primary,
          foregroundColor: colorScheme.onPrimary,
          disabledBackgroundColor: colorScheme.onSurface.withOpacity(0.12),
          disabledForegroundColor: colorScheme.onSurface.withOpacity(0.38),
        );
      case AppButtonType.secondary:
        return FilledButton.styleFrom(
          backgroundColor: colorScheme.secondaryContainer,
          foregroundColor: colorScheme.onSecondaryContainer,
          disabledBackgroundColor: colorScheme.onSurface.withOpacity(0.12),
          disabledForegroundColor: colorScheme.onSurface.withOpacity(0.38),
        );
      case AppButtonType.outlined:
        return OutlinedButton.styleFrom(
          foregroundColor: colorScheme.primary,
          side: BorderSide(color: colorScheme.outline),
          disabledForegroundColor: colorScheme.onSurface.withOpacity(0.38),
        );
      case AppButtonType.text:
        return TextButton.styleFrom(
          foregroundColor: colorScheme.primary,
          disabledForegroundColor: colorScheme.onSurface.withOpacity(0.38),
        );
      case AppButtonType.danger:
        return ElevatedButton.styleFrom(
          backgroundColor: colorScheme.error,
          foregroundColor: colorScheme.onError,
          disabledBackgroundColor: colorScheme.onSurface.withOpacity(0.12),
          disabledForegroundColor: colorScheme.onSurface.withOpacity(0.38),
        );
    }
  }

  /// 获取尺寸按钮样式
  ButtonStyle _getSizeButtonStyle() {
    final height = _getButtonHeight();
    final padding = _getButtonPadding();
    final textStyle = _getTextStyle();
    
    return ButtonStyle(
      minimumSize: MaterialStateProperty.all(Size(64, height)),
      padding: MaterialStateProperty.all(padding),
      textStyle: MaterialStateProperty.all(textStyle),
      shape: MaterialStateProperty.all(
        RoundedRectangleBorder(
          borderRadius: DesignTokens.radiusM,
        ),
      ),
    );
  }

  /// 获取按钮高度
  double _getButtonHeight() {
    switch (size) {
      case AppButtonSize.small:
        return DesignTokens.buttonHeightS;
      case AppButtonSize.medium:
        return DesignTokens.buttonHeightM;
      case AppButtonSize.large:
        return DesignTokens.buttonHeightL;
    }
  }

  /// 获取按钮内边距
  EdgeInsets _getButtonPadding() {
    switch (size) {
      case AppButtonSize.small:
        return const EdgeInsets.symmetric(horizontal: 12);
      case AppButtonSize.medium:
        return const EdgeInsets.symmetric(horizontal: 16);
      case AppButtonSize.large:
        return const EdgeInsets.symmetric(horizontal: 24);
    }
  }

  /// 获取文本样式
  TextStyle _getTextStyle() {
    switch (size) {
      case AppButtonSize.small:
        return DesignTokens.textTheme.labelSmall!;
      case AppButtonSize.medium:
        return DesignTokens.textTheme.labelMedium!;
      case AppButtonSize.large:
        return DesignTokens.textTheme.labelLarge!;
    }
  }

  /// 获取图标尺寸
  double _getIconSize() {
    switch (size) {
      case AppButtonSize.small:
        return DesignTokens.iconSizeS;
      case AppButtonSize.medium:
        return DesignTokens.iconSizeM;
      case AppButtonSize.large:
        return DesignTokens.iconSizeL;
    }
  }

  /// 获取图标间距
  double _getIconSpacing() {
    switch (size) {
      case AppButtonSize.small:
        return 4;
      case AppButtonSize.medium:
        return 8;
      case AppButtonSize.large:
        return 12;
    }
  }

  /// 获取加载指示器尺寸
  double _getLoadingIndicatorSize() {
    switch (size) {
      case AppButtonSize.small:
        return 12;
      case AppButtonSize.medium:
        return 16;
      case AppButtonSize.large:
        return 20;
    }
  }

  /// 获取加载指示器颜色
  Color _getLoadingIndicatorColor(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    
    switch (type) {
      case AppButtonType.primary:
      case AppButtonType.danger:
        return colorScheme.onPrimary;
      case AppButtonType.secondary:
        return colorScheme.onSecondaryContainer;
      case AppButtonType.outlined:
      case AppButtonType.text:
        return colorScheme.primary;
    }
  }
}
