import 'package:flutter/material.dart';
import '../../../core/design/design_tokens.dart';

/// 应用卡片类型
enum AppCardType {
  /// 基础卡片
  basic,
  
  /// 信息卡片
  info,
  
  /// 操作卡片
  action,
  
  /// 统计卡片
  stats,
}

/// 应用卡片组件
/// 
/// 统一的卡片组件，支持多种类型和样式
class AppCard extends StatelessWidget {
  /// 子组件
  final Widget child;
  
  /// 卡片类型
  final AppCardType type;
  
  /// 内边距
  final EdgeInsetsGeometry? padding;
  
  /// 外边距
  final EdgeInsetsGeometry? margin;
  
  /// 点击回调
  final VoidCallback? onTap;
  
  /// 长按回调
  final VoidCallback? onLongPress;
  
  /// 是否有阴影
  final bool elevated;
  
  /// 自定义阴影
  final double? elevation;
  
  /// 背景颜色
  final Color? backgroundColor;
  
  /// 边框
  final Border? border;
  
  /// 圆角
  final BorderRadius? borderRadius;
  
  /// 宽度
  final double? width;
  
  /// 高度
  final double? height;

  const AppCard({
    super.key,
    required this.child,
    this.type = AppCardType.basic,
    this.padding,
    this.margin,
    this.onTap,
    this.onLongPress,
    this.elevated = true,
    this.elevation,
    this.backgroundColor,
    this.border,
    this.borderRadius,
    this.width,
    this.height,
  });

  /// 创建基础卡片
  const AppCard.basic({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.onTap,
    this.onLongPress,
    this.elevated = true,
    this.elevation,
    this.backgroundColor,
    this.border,
    this.borderRadius,
    this.width,
    this.height,
  }) : type = AppCardType.basic;

  /// 创建信息卡片
  const AppCard.info({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.onTap,
    this.onLongPress,
    this.elevated = false,
    this.elevation,
    this.backgroundColor,
    this.border,
    this.borderRadius,
    this.width,
    this.height,
  }) : type = AppCardType.info;

  /// 创建操作卡片
  const AppCard.action({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.onTap,
    this.onLongPress,
    this.elevated = true,
    this.elevation,
    this.backgroundColor,
    this.border,
    this.borderRadius,
    this.width,
    this.height,
  }) : type = AppCardType.action;

  /// 创建统计卡片
  const AppCard.stats({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.onTap,
    this.onLongPress,
    this.elevated = true,
    this.elevation,
    this.backgroundColor,
    this.border,
    this.borderRadius,
    this.width,
    this.height,
  }) : type = AppCardType.stats;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    // 确定卡片样式
    final cardStyle = _getCardStyle(context, colorScheme);
    
    Widget card = Card(
      color: cardStyle.backgroundColor,
      elevation: cardStyle.elevation,
      shadowColor: cardStyle.shadowColor,
      shape: RoundedRectangleBorder(
        borderRadius: cardStyle.borderRadius,
        side: cardStyle.border,
      ),
      margin: margin ?? cardStyle.margin,
      child: Container(
        width: width,
        height: height,
        padding: padding ?? cardStyle.padding,
        child: child,
      ),
    );

    // 如果有点击事件，包装在 InkWell 中
    if (onTap != null || onLongPress != null) {
      card = Card(
        color: cardStyle.backgroundColor,
        elevation: cardStyle.elevation,
        shadowColor: cardStyle.shadowColor,
        shape: RoundedRectangleBorder(
          borderRadius: cardStyle.borderRadius,
          side: cardStyle.border,
        ),
        margin: margin ?? cardStyle.margin,
        child: InkWell(
          onTap: onTap,
          onLongPress: onLongPress,
          borderRadius: cardStyle.borderRadius,
          child: Container(
            width: width,
            height: height,
            padding: padding ?? cardStyle.padding,
            child: child,
          ),
        ),
      );
    }

    return card;
  }

  /// 获取卡片样式
  _CardStyle _getCardStyle(BuildContext context, ColorScheme colorScheme) {
    switch (type) {
      case AppCardType.basic:
        return _CardStyle(
          backgroundColor: backgroundColor ?? colorScheme.surface,
          elevation: elevation ?? (elevated ? 1.0 : 0.0),
          shadowColor: colorScheme.shadow,
          borderRadius: borderRadius ?? DesignTokens.radiusM,
          border: border ?? BorderSide.none,
          padding: DesignTokens.spacingM,
          margin: DesignTokens.spacingS,
        );
      
      case AppCardType.info:
        return _CardStyle(
          backgroundColor: backgroundColor ?? colorScheme.surfaceVariant.withOpacity(0.3),
          elevation: elevation ?? (elevated ? 0.0 : 0.0),
          shadowColor: colorScheme.shadow,
          borderRadius: borderRadius ?? DesignTokens.radiusM,
          border: border ?? BorderSide(
            color: colorScheme.outline.withOpacity(0.2),
            width: 1,
          ),
          padding: DesignTokens.spacingM,
          margin: DesignTokens.spacingS,
        );
      
      case AppCardType.action:
        return _CardStyle(
          backgroundColor: backgroundColor ?? colorScheme.surface,
          elevation: elevation ?? (elevated ? 2.0 : 0.0),
          shadowColor: colorScheme.shadow,
          borderRadius: borderRadius ?? DesignTokens.radiusM,
          border: border ?? BorderSide.none,
          padding: DesignTokens.spacingM,
          margin: DesignTokens.spacingS,
        );
      
      case AppCardType.stats:
        return _CardStyle(
          backgroundColor: backgroundColor ?? colorScheme.primaryContainer.withOpacity(0.1),
          elevation: elevation ?? (elevated ? 1.0 : 0.0),
          shadowColor: colorScheme.shadow,
          borderRadius: borderRadius ?? DesignTokens.radiusL,
          border: border ?? BorderSide(
            color: colorScheme.primary.withOpacity(0.2),
            width: 1,
          ),
          padding: DesignTokens.spacingL,
          margin: DesignTokens.spacingS,
        );
    }
  }
}

/// 卡片样式数据类
class _CardStyle {
  final Color backgroundColor;
  final double elevation;
  final Color shadowColor;
  final BorderRadius borderRadius;
  final BorderSide border;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;

  const _CardStyle({
    required this.backgroundColor,
    required this.elevation,
    required this.shadowColor,
    required this.borderRadius,
    required this.border,
    required this.padding,
    required this.margin,
  });
}

/// 信息卡片组件
/// 
/// 专门用于显示信息的卡片，包含标题、内容和可选的图标
class InfoCard extends StatelessWidget {
  /// 标题
  final String title;
  
  /// 内容
  final String? content;
  
  /// 图标
  final Widget? icon;
  
  /// 操作按钮
  final List<Widget>? actions;
  
  /// 点击回调
  final VoidCallback? onTap;
  
  /// 卡片类型
  final AppCardType type;
  
  /// 内边距
  final EdgeInsetsGeometry? padding;
  
  /// 外边距
  final EdgeInsetsGeometry? margin;

  const InfoCard({
    super.key,
    required this.title,
    this.content,
    this.icon,
    this.actions,
    this.onTap,
    this.type = AppCardType.info,
    this.padding,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AppCard(
      type: type,
      onTap: onTap,
      padding: padding,
      margin: margin,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // 标题行
          Row(
            children: [
              if (icon != null) ...[
                icon!,
                const SizedBox(width: 12),
              ],
              Expanded(
                child: Text(
                  title,
                  style: theme.textTheme.titleMedium,
                ),
              ),
            ],
          ),
          
          // 内容
          if (content != null) ...[
            const SizedBox(height: 8),
            Text(
              content!,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
          
          // 操作按钮
          if (actions != null && actions!.isNotEmpty) ...[
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: actions!,
            ),
          ],
        ],
      ),
    );
  }
}

/// 统计卡片组件
/// 
/// 专门用于显示统计数据的卡片
class StatsCard extends StatelessWidget {
  /// 标题
  final String title;
  
  /// 数值
  final String value;
  
  /// 单位
  final String? unit;
  
  /// 变化值
  final String? change;
  
  /// 变化是否为正向
  final bool? isPositiveChange;
  
  /// 图标
  final Widget? icon;
  
  /// 点击回调
  final VoidCallback? onTap;
  
  /// 内边距
  final EdgeInsetsGeometry? padding;
  
  /// 外边距
  final EdgeInsetsGeometry? margin;

  const StatsCard({
    super.key,
    required this.title,
    required this.value,
    this.unit,
    this.change,
    this.isPositiveChange,
    this.icon,
    this.onTap,
    this.padding,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    return AppCard.stats(
      onTap: onTap,
      padding: padding,
      margin: margin,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // 标题行
          Row(
            children: [
              Expanded(
                child: Text(
                  title,
                  style: theme.textTheme.titleSmall?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
              ),
              if (icon != null) icon!,
            ],
          ),
          
          const SizedBox(height: 8),
          
          // 数值行
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                value,
                style: theme.textTheme.headlineMedium?.copyWith(
                  color: colorScheme.onSurface,
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (unit != null) ...[
                const SizedBox(width: 4),
                Text(
                  unit!,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ],
          ),
          
          // 变化值
          if (change != null) ...[
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  isPositiveChange == true 
                    ? Icons.trending_up 
                    : isPositiveChange == false 
                      ? Icons.trending_down 
                      : Icons.trending_flat,
                  size: 16,
                  color: isPositiveChange == true 
                    ? Colors.green 
                    : isPositiveChange == false 
                      ? Colors.red 
                      : colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 4),
                Text(
                  change!,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: isPositiveChange == true 
                      ? Colors.green 
                      : isPositiveChange == false 
                        ? Colors.red 
                        : colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }
}
