import 'package:flutter/material.dart';
import '../../../core/design/design_tokens.dart';

/// 加载状态类型
enum LoadingType {
  /// 圆形进度指示器
  circular,
  
  /// 线性进度指示器
  linear,
  
  /// 自定义加载动画
  custom,
  
  /// 骨架屏
  skeleton,
}

/// 加载覆盖层组件
/// 
/// 提供统一的加载状态显示，支持多种加载样式和自定义配置
class LoadingOverlay extends StatelessWidget {
  /// 是否显示加载状态
  final bool isLoading;
  
  /// 子组件
  final Widget child;
  
  /// 加载类型
  final LoadingType type;
  
  /// 加载文本
  final String? loadingText;
  
  /// 自定义加载组件
  final Widget? customLoader;
  
  /// 背景颜色
  final Color? backgroundColor;
  
  /// 是否模糊背景
  final bool blurBackground;
  
  /// 是否可以取消
  final bool cancelable;
  
  /// 取消回调
  final VoidCallback? onCancel;
  
  /// 进度值（0.0 - 1.0）
  final double? progress;

  const LoadingOverlay({
    super.key,
    required this.isLoading,
    required this.child,
    this.type = LoadingType.circular,
    this.loadingText,
    this.customLoader,
    this.backgroundColor,
    this.blurBackground = false,
    this.cancelable = false,
    this.onCancel,
    this.progress,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isLoading) _buildLoadingOverlay(context),
      ],
    );
  }

  Widget _buildLoadingOverlay(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    return Positioned.fill(
      child: Container(
        color: backgroundColor ?? colorScheme.surface.withOpacity(0.8),
        child: Center(
          child: Container(
            padding: DesignTokens.spacingL,
            decoration: BoxDecoration(
              color: colorScheme.surface,
              borderRadius: DesignTokens.radiusM,
              boxShadow: DesignTokens.shadowM,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildLoader(context),
                if (loadingText != null) ...[
                  const SizedBox(height: 16),
                  Text(
                    loadingText!,
                    style: theme.textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                ],
                if (cancelable && onCancel != null) ...[
                  const SizedBox(height: 16),
                  TextButton(
                    onPressed: onCancel,
                    child: const Text('取消'),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoader(BuildContext context) {
    switch (type) {
      case LoadingType.circular:
        return SizedBox(
          width: 48,
          height: 48,
          child: CircularProgressIndicator(
            value: progress,
            strokeWidth: 3,
          ),
        );
      
      case LoadingType.linear:
        return SizedBox(
          width: 200,
          child: LinearProgressIndicator(
            value: progress,
          ),
        );
      
      case LoadingType.custom:
        return customLoader ?? _buildDefaultLoader();
      
      case LoadingType.skeleton:
        return _buildSkeletonLoader();
    }
  }

  Widget _buildDefaultLoader() {
    return const SizedBox(
      width: 48,
      height: 48,
      child: CircularProgressIndicator(strokeWidth: 3),
    );
  }

  Widget _buildSkeletonLoader() {
    return Column(
      children: [
        _SkeletonItem(width: 200, height: 20),
        const SizedBox(height: 8),
        _SkeletonItem(width: 150, height: 20),
        const SizedBox(height: 8),
        _SkeletonItem(width: 180, height: 20),
      ],
    );
  }
}

/// 骨架屏项目
class _SkeletonItem extends StatefulWidget {
  final double width;
  final double height;
  final BorderRadius? borderRadius;

  const _SkeletonItem({
    required this.width,
    required this.height,
    this.borderRadius,
  });

  @override
  State<_SkeletonItem> createState() => _SkeletonItemState();
}

class _SkeletonItemState extends State<_SkeletonItem>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.3, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            color: colorScheme.surfaceVariant.withOpacity(_animation.value),
            borderRadius: widget.borderRadius ?? DesignTokens.radiusS,
          ),
        );
      },
    );
  }
}

/// 加载状态构建器
class LoadingBuilder extends StatelessWidget {
  /// 是否加载中
  final bool isLoading;
  
  /// 加载时显示的组件
  final Widget? loadingWidget;
  
  /// 加载完成时显示的组件
  final Widget child;
  
  /// 加载类型
  final LoadingType type;
  
  /// 加载文本
  final String? loadingText;

  const LoadingBuilder({
    super.key,
    required this.isLoading,
    required this.child,
    this.loadingWidget,
    this.type = LoadingType.circular,
    this.loadingText,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return loadingWidget ?? _buildDefaultLoading(context);
    }
    return child;
  }

  Widget _buildDefaultLoading(BuildContext context) {
    switch (type) {
      case LoadingType.circular:
        return Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(),
              if (loadingText != null) ...[
                const SizedBox(height: 16),
                Text(
                  loadingText!,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ],
          ),
        );
      
      case LoadingType.skeleton:
        return _buildSkeletonScreen();
      
      default:
        return const Center(child: CircularProgressIndicator());
    }
  }

  Widget _buildSkeletonScreen() {
    return Padding(
      padding: DesignTokens.spacingM,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _SkeletonItem(width: double.infinity, height: 200),
          const SizedBox(height: 16),
          _SkeletonItem(width: double.infinity, height: 20),
          const SizedBox(height: 8),
          _SkeletonItem(width: 200, height: 20),
          const SizedBox(height: 16),
          Row(
            children: [
              _SkeletonItem(width: 60, height: 60, borderRadius: DesignTokens.radiusFull),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _SkeletonItem(width: double.infinity, height: 16),
                    const SizedBox(height: 4),
                    _SkeletonItem(width: 120, height: 14),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/// 加载按钮
class LoadingButton extends StatelessWidget {
  /// 按钮文本
  final String text;
  
  /// 是否加载中
  final bool isLoading;
  
  /// 点击回调
  final VoidCallback? onPressed;
  
  /// 按钮样式
  final ButtonStyle? style;
  
  /// 加载文本
  final String? loadingText;

  const LoadingButton({
    super.key,
    required this.text,
    this.isLoading = false,
    this.onPressed,
    this.style,
    this.loadingText,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: style,
      child: isLoading
          ? Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                const SizedBox(width: 8),
                Text(loadingText ?? '加载中...'),
              ],
            )
          : Text(text),
    );
  }
}

/// 刷新指示器
class RefreshIndicatorWrapper extends StatelessWidget {
  /// 子组件
  final Widget child;
  
  /// 刷新回调
  final Future<void> Function() onRefresh;
  
  /// 是否启用刷新
  final bool enabled;

  const RefreshIndicatorWrapper({
    super.key,
    required this.child,
    required this.onRefresh,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    if (!enabled) return child;
    
    return RefreshIndicator(
      onRefresh: onRefresh,
      child: child,
    );
  }
}
