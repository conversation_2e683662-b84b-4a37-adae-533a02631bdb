import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:injectable/injectable.dart';

/// 用户体验服务
/// 
/// 提供统一的用户体验优化功能，包括反馈、提示、状态管理等
@injectable
class UserExperienceService {
  // 全局导航器键
  final GlobalKey<NavigatorState>? _navigatorKey;
  
  // 消息显示计时器
  Timer? _messageTimer;
  
  // 当前显示的消息
  ScaffoldMessengerState? _currentMessenger;

  UserExperienceService({GlobalKey<NavigatorState>? navigatorKey})
      : _navigatorKey = navigatorKey;

  /// 获取当前上下文
  BuildContext? get _currentContext => _navigatorKey?.currentContext;

  /// 显示成功消息
  void showSuccessMessage(String message, {Duration? duration}) {
    _showSnackBar(
      message: message,
      backgroundColor: Colors.green,
      icon: Icons.check_circle,
      duration: duration,
    );
  }

  /// 显示错误消息
  void showErrorMessage(String message, {Duration? duration}) {
    _showSnackBar(
      message: message,
      backgroundColor: Colors.red,
      icon: Icons.error,
      duration: duration,
    );
  }

  /// 显示警告消息
  void showWarningMessage(String message, {Duration? duration}) {
    _showSnackBar(
      message: message,
      backgroundColor: Colors.orange,
      icon: Icons.warning,
      duration: duration,
    );
  }

  /// 显示信息消息
  void showInfoMessage(String message, {Duration? duration}) {
    _showSnackBar(
      message: message,
      backgroundColor: Colors.blue,
      icon: Icons.info,
      duration: duration,
    );
  }

  /// 显示自定义消息
  void showCustomMessage({
    required String message,
    Color? backgroundColor,
    IconData? icon,
    Duration? duration,
    List<SnackBarAction>? actions,
  }) {
    _showSnackBar(
      message: message,
      backgroundColor: backgroundColor,
      icon: icon,
      duration: duration,
      actions: actions,
    );
  }

  /// 显示加载提示
  void showLoadingMessage(String message) {
    final context = _currentContext;
    if (context == null) return;

    _showSnackBar(
      message: message,
      icon: null,
      duration: const Duration(days: 1), // 长时间显示，需要手动关闭
      showCloseIcon: false,
      customContent: Row(
        children: [
          const SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(child: Text(message)),
        ],
      ),
    );
  }

  /// 隐藏当前消息
  void hideCurrentMessage() {
    _currentMessenger?.hideCurrentSnackBar();
    _messageTimer?.cancel();
  }

  /// 显示确认对话框
  Future<bool> showConfirmDialog({
    required String title,
    required String message,
    String confirmText = '确认',
    String cancelText = '取消',
    bool isDangerous = false,
  }) async {
    final context = _currentContext;
    if (context == null) return false;

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(cancelText),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: isDangerous
                ? ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  )
                : null,
            child: Text(confirmText),
          ),
        ],
      ),
    );

    return result ?? false;
  }

  /// 显示输入对话框
  Future<String?> showInputDialog({
    required String title,
    String? message,
    String? initialValue,
    String? hintText,
    String confirmText = '确认',
    String cancelText = '取消',
    TextInputType keyboardType = TextInputType.text,
    String? Function(String?)? validator,
  }) async {
    final context = _currentContext;
    if (context == null) return null;

    final controller = TextEditingController(text: initialValue);
    final formKey = GlobalKey<FormState>();

    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (message != null) ...[
                Text(message),
                const SizedBox(height: 16),
              ],
              TextFormField(
                controller: controller,
                decoration: InputDecoration(
                  hintText: hintText,
                  border: const OutlineInputBorder(),
                ),
                keyboardType: keyboardType,
                validator: validator,
                autofocus: true,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(cancelText),
          ),
          ElevatedButton(
            onPressed: () {
              if (formKey.currentState?.validate() ?? false) {
                Navigator.of(context).pop(controller.text);
              }
            },
            child: Text(confirmText),
          ),
        ],
      ),
    );

    controller.dispose();
    return result;
  }

  /// 显示选择对话框
  Future<T?> showChoiceDialog<T>({
    required String title,
    String? message,
    required List<ChoiceItem<T>> choices,
    String cancelText = '取消',
  }) async {
    final context = _currentContext;
    if (context == null) return null;

    return await showDialog<T>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (message != null) ...[
              Text(message),
              const SizedBox(height: 16),
            ],
            ...choices.map((choice) => ListTile(
              leading: choice.icon,
              title: Text(choice.title),
              subtitle: choice.subtitle != null ? Text(choice.subtitle!) : null,
              onTap: () => Navigator.of(context).pop(choice.value),
            )),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(cancelText),
          ),
        ],
      ),
    );
  }

  /// 显示底部操作表
  Future<T?> showActionSheet<T>({
    required String title,
    String? message,
    required List<ActionSheetItem<T>> actions,
    String cancelText = '取消',
  }) async {
    final context = _currentContext;
    if (context == null) return null;

    return await showModalBottomSheet<T>(
      context: context,
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (title.isNotEmpty || message != null)
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    if (title.isNotEmpty)
                      Text(
                        title,
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                    if (message != null) ...[
                      const SizedBox(height: 8),
                      Text(
                        message,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ],
                  ],
                ),
              ),
            ...actions.map((action) => ListTile(
              leading: action.icon,
              title: Text(action.title),
              subtitle: action.subtitle != null ? Text(action.subtitle!) : null,
              onTap: () => Navigator.of(context).pop(action.value),
              textColor: action.isDestructive ? Colors.red : null,
            )),
            const Divider(),
            ListTile(
              title: Text(
                cancelText,
                textAlign: TextAlign.center,
              ),
              onTap: () => Navigator.of(context).pop(),
            ),
          ],
        ),
      ),
    );
  }

  /// 触发触觉反馈
  void hapticFeedback({HapticFeedbackType type = HapticFeedbackType.light}) {
    switch (type) {
      case HapticFeedbackType.light:
        HapticFeedback.lightImpact();
        break;
      case HapticFeedbackType.medium:
        HapticFeedback.mediumImpact();
        break;
      case HapticFeedbackType.heavy:
        HapticFeedback.heavyImpact();
        break;
      case HapticFeedbackType.selection:
        HapticFeedback.selectionClick();
        break;
    }
  }

  /// 显示工具提示
  void showTooltip({
    required GlobalKey key,
    required String message,
    Duration duration = const Duration(seconds: 3),
  }) {
    final dynamic tooltip = key.currentWidget;
    if (tooltip is Tooltip) {
      tooltip.ensureTooltipVisible();
    }
  }

  void _showSnackBar({
    required String message,
    Color? backgroundColor,
    IconData? icon,
    Duration? duration,
    List<SnackBarAction>? actions,
    bool showCloseIcon = true,
    Widget? customContent,
  }) {
    final context = _currentContext;
    if (context == null) return;

    // 取消之前的计时器
    _messageTimer?.cancel();

    // 隐藏当前消息
    ScaffoldMessenger.of(context).hideCurrentSnackBar();

    final snackBar = SnackBar(
      content: customContent ??
          Row(
            children: [
              if (icon != null) ...[
                Icon(icon, color: Colors.white),
                const SizedBox(width: 8),
              ],
              Expanded(child: Text(message)),
            ],
          ),
      backgroundColor: backgroundColor,
      duration: duration ?? const Duration(seconds: 4),
      action: actions?.isNotEmpty == true ? actions!.first : null,
      showCloseIcon: showCloseIcon,
    );

    _currentMessenger = ScaffoldMessenger.of(context);
    _currentMessenger!.showSnackBar(snackBar);

    // 设置自动隐藏计时器
    if (duration != null && duration.inDays < 1) {
      _messageTimer = Timer(duration, () {
        _currentMessenger?.hideCurrentSnackBar();
      });
    }
  }

  /// 清理资源
  void dispose() {
    _messageTimer?.cancel();
  }
}

/// 选择项
class ChoiceItem<T> {
  final T value;
  final String title;
  final String? subtitle;
  final Widget? icon;

  const ChoiceItem({
    required this.value,
    required this.title,
    this.subtitle,
    this.icon,
  });
}

/// 操作表项
class ActionSheetItem<T> {
  final T value;
  final String title;
  final String? subtitle;
  final Widget? icon;
  final bool isDestructive;

  const ActionSheetItem({
    required this.value,
    required this.title,
    this.subtitle,
    this.icon,
    this.isDestructive = false,
  });
}

/// 触觉反馈类型
enum HapticFeedbackType {
  light,
  medium,
  heavy,
  selection,
}
