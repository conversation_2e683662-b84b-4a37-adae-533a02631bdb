import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'core/di/injection.dart';
import 'core/config/feature_config.dart';
import 'core/constants/feature_constants.dart';

/// 应用程序入口点
///
/// 初始化企业级Flutter应用的基础架构
void main() async {
  // 确保Flutter绑定初始化
  WidgetsFlutterBinding.ensureInitialized();

  // 确定运行环境
  const environment = String.fromEnvironment('ENVIRONMENT', defaultValue: 'dev');

  try {
    // 初始化依赖注入
    await configureDependencies(environment: environment);

    if (kDebugMode) {
      print('🚀 Flutter企业级应用启动成功');
      print('📦 环境: $environment');
      print('🔧 已启用功能: ${FeatureConfig.instance.getEnabledFeatures()}');
    }
  } catch (e, stackTrace) {
    if (kDebugMode) {
      print('❌ 应用初始化失败: $e');
      print('📍 堆栈跟踪: $stackTrace');
    }
  }

  runApp(const FlutterEnterpriseApp());
}

/// Flutter企业级应用主类
class FlutterEnterpriseApp extends StatelessWidget {
  const FlutterEnterpriseApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Flutter企业级应用',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
      ),
      home: const HomePage(),
      debugShowCheckedModeBanner: false,
    );
  }
}

/// 主页
class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Flutter企业级应用'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: const Padding(
        padding: EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _WelcomeSection(),
              SizedBox(height: 24),
              _ArchitectureSection(),
              SizedBox(height: 24),
              _FeatureSection(),
            ],
          ),
        ),
      ),
    );
  }
}

/// 欢迎部分
class _WelcomeSection extends StatelessWidget {
  const _WelcomeSection();

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '🎉 欢迎使用Flutter企业级应用',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              '这是一个基于Clean Architecture的企业级Flutter应用模板，'
              '包含完整的架构设计、依赖注入、功能配置管理等企业级特性。',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }
}

/// 架构部分
class _ArchitectureSection extends StatelessWidget {
  const _ArchitectureSection();

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '🏗️ 架构特性',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 12),
            const _ArchitectureItem(
              icon: '📁',
              title: 'Clean Architecture',
              description: '三层架构：数据层、领域层、表现层',
            ),
            const _ArchitectureItem(
              icon: '💉',
              title: '依赖注入',
              description: 'GetIt + Injectable 自动依赖注入',
            ),
            const _ArchitectureItem(
              icon: '🔧',
              title: '功能配置',
              description: '模块化功能管理，支持动态启用/禁用',
            ),
            const _ArchitectureItem(
              icon: '🧪',
              title: '测试框架',
              description: '完整的单元测试和Mock服务支持',
            ),
          ],
        ),
      ),
    );
  }
}

/// 架构项目
class _ArchitectureItem extends StatelessWidget {
  const _ArchitectureItem({
    required this.icon,
    required this.title,
    required this.description,
  });

  final String icon;
  final String title;
  final String description;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(icon, style: const TextStyle(fontSize: 20)),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// 功能部分
class _FeatureSection extends StatelessWidget {
  const _FeatureSection();

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '⚡ 功能状态',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 12),
            const _FeatureStatusList(),
          ],
        ),
      ),
    );
  }
}

/// 功能状态列表
class _FeatureStatusList extends StatelessWidget {
  const _FeatureStatusList();

  @override
  Widget build(BuildContext context) {
    try {
      final enabledFeatures = FeatureConfig.instance.getEnabledFeatures();
      final coreFeatures = [
        Features.authentication,
        Features.authorization,
        Features.internationalization,
        Features.analytics,
      ];

      return Column(
        children: coreFeatures.map((feature) {
          final isEnabled = enabledFeatures.contains(feature);
          return _FeatureStatusItem(
            feature: feature,
            isEnabled: isEnabled,
          );
        }).toList(),
      );
    } catch (e) {
      return Text(
        '功能配置加载中...',
        style: Theme.of(context).textTheme.bodyMedium,
      );
    }
  }
}

/// 功能状态项目
class _FeatureStatusItem extends StatelessWidget {
  const _FeatureStatusItem({
    required this.feature,
    required this.isEnabled,
  });

  final String feature;
  final bool isEnabled;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2.0),
      child: Row(
        children: [
          Icon(
            isEnabled ? Icons.check_circle : Icons.cancel,
            color: isEnabled ? Colors.green : Colors.grey,
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            _getFeatureDisplayName(feature),
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const Spacer(),
          Text(
            isEnabled ? '已启用' : '已禁用',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: isEnabled ? Colors.green : Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  String _getFeatureDisplayName(String feature) {
    switch (feature) {
      case Features.authentication:
        return '用户认证';
      case Features.authorization:
        return '权限管理';
      case Features.internationalization:
        return '国际化';
      case Features.analytics:
        return '数据分析';
      default:
        return feature;
    }
  }
}