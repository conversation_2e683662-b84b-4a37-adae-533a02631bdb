name: Quality Gate

on:
  pull_request:
    branches: [ main, develop ]
  push:
    branches: [ main, develop ]

env:
  FLUTTER_VERSION: '3.16.0'

jobs:
  quality-gate:
    name: Quality Gate Check
    runs-on: ubuntu-latest
    timeout-minutes: 20
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        cache: true
    
    - name: Get dependencies
      run: flutter pub get
    
    # 代码格式检查
    - name: Check code formatting
      run: |
        echo "::group::Code Formatting Check"
        if ! dart format --output=none --set-exit-if-changed .; then
          echo "::error::Code is not properly formatted. Run 'dart format .' to fix."
          exit 1
        fi
        echo "✅ Code formatting is correct"
        echo "::endgroup::"
    
    # 静态分析
    - name: Run static analysis
      run: |
        echo "::group::Static Analysis"
        dart analyze --fatal-infos --fatal-warnings
        echo "✅ Static analysis passed"
        echo "::endgroup::"
    
    # 自定义Lint检查
    - name: Run custom lint
      run: |
        echo "::group::Custom Lint Check"
        dart run custom_lint
        echo "✅ Custom lint passed"
        echo "::endgroup::"
    
    # 代码复杂度检查
    - name: Check code complexity
      run: |
        echo "::group::Code Complexity Check"
        dart run dart_code_metrics:metrics analyze lib --reporter=console
        
        # 检查圈复杂度
        COMPLEXITY=$(dart run dart_code_metrics:metrics analyze lib --reporter=json | jq '.records[].metrics."cyclomatic-complexity".value' | sort -nr | head -1)
        if [ "$COMPLEXITY" -gt 20 ]; then
          echo "::error::Cyclomatic complexity too high: $COMPLEXITY (max: 20)"
          exit 1
        fi
        
        # 检查代码行数
        LINES=$(dart run dart_code_metrics:metrics analyze lib --reporter=json | jq '.records[].metrics."source-lines-of-code".value' | sort -nr | head -1)
        if [ "$LINES" -gt 300 ]; then
          echo "::error::File too long: $LINES lines (max: 300)"
          exit 1
        fi
        
        echo "✅ Code complexity check passed"
        echo "::endgroup::"
    
    # 测试覆盖率检查
    - name: Check test coverage
      run: |
        echo "::group::Test Coverage Check"
        flutter test --coverage
        
        # 安装lcov工具
        sudo apt-get update
        sudo apt-get install -y lcov
        
        # 生成覆盖率报告
        genhtml coverage/lcov.info -o coverage/html
        
        # 检查覆盖率
        COVERAGE=$(lcov --summary coverage/lcov.info | grep "lines" | grep -o '[0-9.]*%' | head -1 | sed 's/%//')
        echo "Current test coverage: $COVERAGE%"
        
        # 设置最低覆盖率要求
        MIN_COVERAGE=80
        if (( $(echo "$COVERAGE < $MIN_COVERAGE" | bc -l) )); then
          echo "::error::Test coverage too low: $COVERAGE% (minimum: $MIN_COVERAGE%)"
          exit 1
        fi
        
        echo "✅ Test coverage check passed: $COVERAGE%"
        echo "::endgroup::"
    
    # 依赖安全检查
    - name: Check dependency security
      run: |
        echo "::group::Dependency Security Check"
        
        # 检查已知漏洞
        dart pub deps --json > deps.json
        
        # 检查过时的依赖
        dart pub outdated --json > outdated.json
        
        # 检查是否有严重的安全漏洞
        if grep -q "security" outdated.json; then
          echo "::warning::Found potential security issues in dependencies"
        fi
        
        echo "✅ Dependency security check completed"
        echo "::endgroup::"
    
    # 构建检查
    - name: Check build
      run: |
        echo "::group::Build Check"
        
        # 检查是否能成功构建
        flutter build apk --debug --target-platform android-arm64
        
        # 检查构建产物大小
        APK_SIZE=$(stat -c%s "build/app/outputs/flutter-apk/app-debug.apk")
        APK_SIZE_MB=$((APK_SIZE / 1024 / 1024))
        
        echo "APK size: ${APK_SIZE_MB}MB"
        
        # 设置APK大小限制
        MAX_APK_SIZE_MB=50
        if [ "$APK_SIZE_MB" -gt "$MAX_APK_SIZE_MB" ]; then
          echo "::error::APK size too large: ${APK_SIZE_MB}MB (max: ${MAX_APK_SIZE_MB}MB)"
          exit 1
        fi
        
        echo "✅ Build check passed"
        echo "::endgroup::"
    
    # 性能检查
    - name: Check performance
      run: |
        echo "::group::Performance Check"
        
        # 运行性能测试
        flutter test test/performance/ --reporter=json > performance_results.json
        
        # 检查性能指标
        if [ -f "performance_results.json" ]; then
          # 检查是否有性能测试失败
          FAILED_TESTS=$(jq '.tests[] | select(.result == "error") | .name' performance_results.json)
          if [ -n "$FAILED_TESTS" ]; then
            echo "::error::Performance tests failed: $FAILED_TESTS"
            exit 1
          fi
        fi
        
        echo "✅ Performance check passed"
        echo "::endgroup::"
    
    # 文档检查
    - name: Check documentation
      run: |
        echo "::group::Documentation Check"
        
        # 检查README文件
        if [ ! -f "README.md" ]; then
          echo "::error::README.md is missing"
          exit 1
        fi
        
        # 检查API文档
        dart doc --validate-links
        
        # 检查公共API是否有文档
        UNDOCUMENTED=$(dart analyze --format=machine | grep "public_member_api_docs" | wc -l)
        if [ "$UNDOCUMENTED" -gt 0 ]; then
          echo "::warning::Found $UNDOCUMENTED undocumented public APIs"
        fi
        
        echo "✅ Documentation check passed"
        echo "::endgroup::"
    
    # 许可证检查
    - name: Check licenses
      run: |
        echo "::group::License Check"
        
        # 检查项目许可证
        if [ ! -f "LICENSE" ]; then
          echo "::warning::LICENSE file is missing"
        fi
        
        # 检查依赖许可证
        flutter pub deps --json | jq -r '.packages[] | select(.kind == "direct") | .name' > direct_deps.txt
        
        echo "✅ License check completed"
        echo "::endgroup::"
    
    # 生成质量报告
    - name: Generate quality report
      run: |
        echo "::group::Quality Report"
        
        # 创建质量报告
        cat > quality_report.md << EOF
        # Quality Gate Report
        
        ## Summary
        - ✅ Code formatting: Passed
        - ✅ Static analysis: Passed
        - ✅ Custom lint: Passed
        - ✅ Code complexity: Passed
        - ✅ Test coverage: Passed
        - ✅ Dependency security: Passed
        - ✅ Build check: Passed
        - ✅ Performance check: Passed
        - ✅ Documentation: Passed
        - ✅ License check: Passed
        
        ## Metrics
        - Test Coverage: $COVERAGE%
        - APK Size: ${APK_SIZE_MB}MB
        - Max Complexity: $COMPLEXITY
        - Max File Lines: $LINES
        
        ## Generated at
        $(date)
        EOF
        
        echo "Quality gate passed! 🎉"
        echo "::endgroup::"
    
    # 上传报告
    - name: Upload quality report
      uses: actions/upload-artifact@v3
      with:
        name: quality-report
        path: |
          quality_report.md
          coverage/html/
          performance_results.json
    
    # 评论PR（如果是PR）
    - name: Comment PR
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          const report = fs.readFileSync('quality_report.md', 'utf8');
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: `## Quality Gate Report\n\n${report}`
          });

  # 质量门禁决策
  quality-gate-decision:
    name: Quality Gate Decision
    runs-on: ubuntu-latest
    needs: quality-gate
    if: always()
    
    steps:
    - name: Check quality gate result
      run: |
        if [ "${{ needs.quality-gate.result }}" != "success" ]; then
          echo "::error::Quality gate failed! Please fix the issues before merging."
          exit 1
        fi
        echo "✅ Quality gate passed! Ready for merge."
    
    - name: Set status check
      uses: actions/github-script@v6
      if: github.event_name == 'pull_request'
      with:
        script: |
          const state = '${{ needs.quality-gate.result }}' === 'success' ? 'success' : 'failure';
          const description = state === 'success' ? 'Quality gate passed' : 'Quality gate failed';
          
          github.rest.repos.createCommitStatus({
            owner: context.repo.owner,
            repo: context.repo.repo,
            sha: context.sha,
            state: state,
            target_url: `${context.serverUrl}/${context.repo.owner}/${context.repo.repo}/actions/runs/${context.runId}`,
            description: description,
            context: 'Quality Gate'
          });
