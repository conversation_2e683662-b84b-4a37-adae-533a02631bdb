name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

env:
  FLUTTER_VERSION: '3.16.0'
  JAVA_VERSION: '17'
  NODE_VERSION: '18'

jobs:
  # 代码质量检查
  code-quality:
    name: Code Quality Analysis
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        cache: true
    
    - name: Get dependencies
      run: flutter pub get
    
    - name: Verify formatting
      run: dart format --output=none --set-exit-if-changed .
    
    - name: Analyze project source
      run: dart analyze --fatal-infos
    
    - name: Run custom lint rules
      run: dart run custom_lint
    
    - name: Check for unused files
      run: dart run dart_code_metrics:metrics check-unused-files lib
    
    - name: Check for unused code
      run: dart run dart_code_metrics:metrics check-unused-code lib
    
    - name: Generate metrics report
      run: dart run dart_code_metrics:metrics analyze lib --reporter=github
    
    - name: Upload code quality results
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: dart-metrics.sarif

  # 安全扫描
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        cache: true
    
    - name: Get dependencies
      run: flutter pub get
    
    - name: Run security audit
      run: dart pub deps --json | dart run security_audit
    
    - name: Check for known vulnerabilities
      run: dart run pubspec_dependency_checker
    
    - name: Scan for secrets
      uses: trufflesecurity/trufflehog@main
      with:
        path: ./
        base: main
        head: HEAD

  # 单元测试
  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    timeout-minutes: 20
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        cache: true
    
    - name: Get dependencies
      run: flutter pub get
    
    - name: Run unit tests
      run: flutter test --coverage --reporter=github
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: coverage/lcov.info
        flags: unittests
        name: codecov-umbrella
    
    - name: Generate coverage report
      run: |
        dart pub global activate coverage
        dart pub global run coverage:format_coverage --lcov --in=coverage --out=coverage/lcov.info --packages=.packages --report-on=lib
    
    - name: Upload test results
      uses: dorny/test-reporter@v1
      if: always()
      with:
        name: Unit Test Results
        path: test-results.json
        reporter: flutter-json

  # 集成测试
  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    timeout-minutes: 30
    
    strategy:
      matrix:
        api-level: [29, 33]
        target: [default, google_apis]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        cache: true
    
    - name: Setup Java
      uses: actions/setup-java@v3
      with:
        distribution: 'zulu'
        java-version: ${{ env.JAVA_VERSION }}
    
    - name: Get dependencies
      run: flutter pub get
    
    - name: Enable KVM group perms
      run: |
        echo 'KERNEL=="kvm", GROUP="kvm", MODE="0666", OPTIONS+="static_node=kvm"' | sudo tee /etc/udev/rules.d/99-kvm4all.rules
        sudo udevadm control --reload-rules
        sudo udevadm trigger --name-match=kvm
    
    - name: Run integration tests
      uses: reactivecircus/android-emulator-runner@v2
      with:
        api-level: ${{ matrix.api-level }}
        target: ${{ matrix.target }}
        arch: x86_64
        profile: Nexus 6
        script: flutter test integration_test/
    
    - name: Upload integration test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: integration-test-results-${{ matrix.api-level }}-${{ matrix.target }}
        path: integration_test/reports/

  # 构建测试
  build-test:
    name: Build Test
    runs-on: ${{ matrix.os }}
    timeout-minutes: 30
    
    strategy:
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
        include:
          - os: ubuntu-latest
            target: android
          - os: macos-latest
            target: ios
          - os: windows-latest
            target: windows
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        cache: true
    
    - name: Setup Java (Android)
      if: matrix.target == 'android'
      uses: actions/setup-java@v3
      with:
        distribution: 'zulu'
        java-version: ${{ env.JAVA_VERSION }}
    
    - name: Setup Xcode (iOS)
      if: matrix.target == 'ios'
      uses: maxim-lobanov/setup-xcode@v1
      with:
        xcode-version: latest-stable
    
    - name: Get dependencies
      run: flutter pub get
    
    - name: Build for ${{ matrix.target }}
      run: |
        case "${{ matrix.target }}" in
          android)
            flutter build apk --debug
            flutter build appbundle --debug
            ;;
          ios)
            flutter build ios --debug --no-codesign
            ;;
          windows)
            flutter build windows --debug
            ;;
        esac
    
    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: build-${{ matrix.target }}-${{ github.sha }}
        path: |
          build/app/outputs/
          build/ios/
          build/windows/

  # 性能测试
  performance-test:
    name: Performance Test
    runs-on: ubuntu-latest
    timeout-minutes: 25
    needs: [unit-tests]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        cache: true
    
    - name: Get dependencies
      run: flutter pub get
    
    - name: Run performance tests
      run: flutter test test/performance/
    
    - name: Analyze bundle size
      run: |
        flutter build apk --analyze-size --target-platform android-arm64
        flutter build appbundle --analyze-size
    
    - name: Upload performance results
      uses: actions/upload-artifact@v3
      with:
        name: performance-results
        path: performance-results.json

  # 部署准备
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    timeout-minutes: 20
    needs: [code-quality, security-scan, unit-tests, integration-tests, build-test]
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    
    environment:
      name: staging
      url: https://staging.example.com
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        cache: true
    
    - name: Get dependencies
      run: flutter pub get
    
    - name: Build for staging
      run: |
        flutter build apk --flavor staging --dart-define=ENVIRONMENT=staging
        flutter build appbundle --flavor staging --dart-define=ENVIRONMENT=staging
    
    - name: Deploy to Firebase App Distribution
      uses: wzieba/Firebase-Distribution-Github-Action@v1
      with:
        appId: ${{ secrets.FIREBASE_STAGING_APP_ID }}
        serviceCredentialsFileContent: ${{ secrets.FIREBASE_SERVICE_ACCOUNT }}
        groups: testers
        file: build/app/outputs/bundle/stagingRelease/app-staging-release.aab
        releaseNotes: "Staging build from commit ${{ github.sha }}"

  # 生产部署
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    timeout-minutes: 30
    needs: [code-quality, security-scan, unit-tests, integration-tests, build-test, performance-test]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    environment:
      name: production
      url: https://app.example.com
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        cache: true
    
    - name: Get dependencies
      run: flutter pub get
    
    - name: Build for production
      run: |
        flutter build apk --release --flavor production --dart-define=ENVIRONMENT=production
        flutter build appbundle --release --flavor production --dart-define=ENVIRONMENT=production
    
    - name: Sign APK
      uses: r0adkll/sign-android-release@v1
      with:
        releaseDirectory: build/app/outputs/apk/productionRelease
        signingKeyBase64: ${{ secrets.ANDROID_SIGNING_KEY }}
        alias: ${{ secrets.ANDROID_KEY_ALIAS }}
        keyStorePassword: ${{ secrets.ANDROID_KEYSTORE_PASSWORD }}
        keyPassword: ${{ secrets.ANDROID_KEY_PASSWORD }}
    
    - name: Deploy to Google Play
      uses: r0adkll/upload-google-play@v1
      with:
        serviceAccountJsonPlainText: ${{ secrets.GOOGLE_PLAY_SERVICE_ACCOUNT }}
        packageName: com.example.flutter_template
        releaseFiles: build/app/outputs/bundle/productionRelease/app-production-release.aab
        track: production
        status: completed

  # 通知
  notify:
    name: Notify Results
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always()
    
    steps:
    - name: Notify Slack
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#ci-cd'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        fields: repo,message,commit,author,action,eventName,ref,workflow
